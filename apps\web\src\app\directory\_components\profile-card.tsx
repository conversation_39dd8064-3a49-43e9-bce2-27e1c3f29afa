"use client"

import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"

export type DirectoryProfile = {
  id: string
  firstName: string
  lastName: string
  displayName?: string
  bio?: string
  profilePicture?: string
  currentPosition?: string
  currentCompany?: string
  location?: string
  industry?: string
  mentorshipOffered: boolean
  mentorshipSought: boolean
}

export function ProfileCard({ profile, onOpen }: { profile: DirectoryProfile; onOpen?: (id: string) => void }) {
  const initials = `${profile.firstName?.[0] ?? ""}${profile.lastName?.[0] ?? ""}`.toUpperCase()

  return (
    <div className="rounded-lg border bg-card p-4">
      <div className="flex items-start gap-3">
        <Avatar className="h-10 w-10">
          <AvatarImage src={profile.profilePicture ?? undefined} alt={profile.displayName ?? profile.firstName} />
          <AvatarFallback>{initials || "AL"}</AvatarFallback>
        </Avatar>
        <div className="min-w-0 flex-1">
          <div className="font-medium truncate">{profile.displayName || `${profile.firstName} ${profile.lastName}`}</div>
          <div className="text-sm text-muted-foreground truncate">
            {profile.currentPosition || "—"}{profile.currentCompany ? ` • ${profile.currentCompany}` : ""}
          </div>
          <div className="mt-2 flex flex-wrap gap-2">
            {profile.industry && <Badge variant="secondary">{profile.industry}</Badge>}
            {profile.location && <Badge variant="secondary">{profile.location}</Badge>}
            {profile.mentorshipOffered && <Badge variant="secondary">Offers mentorship</Badge>}
            {profile.mentorshipSought && <Badge variant="secondary">Seeks mentorship</Badge>}
          </div>
        </div>
        {onOpen ? (
          <Button size="sm" onClick={() => onOpen(profile.id)}>View</Button>
        ) : (
          <Button size="sm" asChild>
            <Link href={`/directory/${profile.id}`}>View</Link>
          </Button>
        )}
      </div>
    </div>
  )
}

export default ProfileCard


