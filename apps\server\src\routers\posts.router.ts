import { z } from "zod";
import { publicProcedure, protectedProcedure } from "../lib/orpc";
import { prisma } from "../lib/database";
import {
  createPostSchema,
  updatePostSchema,
  postFilterSchema,
  commentSchema,
  idSchema,
} from "../lib/validation";
import { getPaginationParams, createPaginationResult } from "../lib/utils";

export const postsRouter = {
  /**
   * Get posts with filtering and pagination (public endpoint)
   */
  list: publicProcedure
    .input(postFilterSchema)
    .handler(async ({ input, context }) => {
      const { page, limit, skip } = getPaginationParams(input);
      const where: any = {
        postType: input.postType ?? undefined,
        organizationId: input.organizationId ?? undefined,
        authorId: undefined,
      };
      if (input.authorId) where.author = { userId: input.authorId };

      const [data, total] = await Promise.all([
        prisma.post.findMany({
          where,
          orderBy: { createdAt: "desc" },
          skip,
          take: limit,
          include: {
            author: true,
            _count: { select: { likes: true, comments: true } },
          },
        }),
        prisma.post.count({ where }),
      ]);
      return createPaginationResult(data, total, page, limit);
    }),

  /**
   * Get specific post by ID with engagement data (public endpoint)
   */
  getById: publicProcedure
    .input(z.object({ id: idSchema }))
    .handler(async ({ input, context }) => {
      const post = await prisma.post.findUnique({
        where: { id: input.id },
        include: {
          author: true,
          comments: { include: { author: true } },
          _count: { select: { likes: true, comments: true } },
        },
      });
      if (!post) throw new Error("Post not found");
      return post;
    }),

  /**
   * Create new post
   */
  create: protectedProcedure
    .input(createPostSchema)
    .handler(async ({ input, context }) => {
      const authorProfile = await prisma.alumniProfile.findUnique({
        where: { userId: context.user!.id },
        select: { id: true },
      });
      if (!authorProfile) throw new Error("Create your alumni profile first");
      return prisma.post.create({
        data: {
          authorId: authorProfile.id,
          ...input,
        },
      });
    }),

  /**
   * Update own post
   */
  update: protectedProcedure
    .input(z.object({ id: idSchema }).merge(updatePostSchema))
    .handler(async ({ input, context }) => {
      const { id, ...updateData } = input;
      // Ensure post belongs to user
      const post = await prisma.post.findUnique({
        where: { id },
        include: { author: { select: { userId: true } } },
      });
      if (!post || post.author.userId !== context.user!.id) {
        throw new Error("Not allowed");
      }
      return prisma.post.update({ where: { id }, data: updateData });
    }),

  /**
   * Delete own post
   */
  delete: protectedProcedure
    .input(z.object({ id: idSchema }))
    .handler(async ({ input, context }) => {
      const post = await prisma.post.findUnique({
        where: { id: input.id },
        include: { author: { select: { userId: true } } },
      });
      if (!post || post.author.userId !== context.user!.id) {
        throw new Error("Not allowed");
      }
      await prisma.post.delete({ where: { id: input.id } });
      return { success: true };
    }),

  /**
   * Like or unlike a post
   */
  toggleLike: protectedProcedure
    .input(z.object({ postId: idSchema }))
    .handler(async ({ input, context }) => {
      const profile = await prisma.alumniProfile.findUnique({
        where: { userId: context.user!.id },
        select: { id: true },
      });
      if (!profile) throw new Error("Create your alumni profile first");
      const existing = await prisma.postLike.findUnique({
        where: { postId_userId: { postId: input.postId, userId: profile.id } },
      });
      if (existing) {
        await prisma.postLike.delete({ where: { id: existing.id } });
        return { liked: false };
      }
      await prisma.postLike.create({
        data: { postId: input.postId, userId: profile.id },
      });
      return { liked: true };
    }),

  /**
   * Get posts by specific author
   */
  getByAuthor: publicProcedure
    .input(
      z.object({
        authorId: idSchema,
        page: z.number().int().min(1).default(1),
        limit: z.number().int().min(1).max(100).default(20),
      })
    )
    .handler(async ({ input }) => {
      const { page, limit, skip } = getPaginationParams(input);
      const where = { author: { userId: input.authorId } } as const;
      const [data, total] = await Promise.all([
        prisma.post.findMany({
          where,
          orderBy: { createdAt: "desc" },
          skip,
          take: limit,
        }),
        prisma.post.count({ where }),
      ]);
      return createPaginationResult(data, total, page, limit);
    }),

  /**
   * Get post engagement statistics
   */
  getStats: publicProcedure
    .input(z.object({ postId: idSchema }))
    .handler(async ({ input }) => {
      const [likes, comments] = await Promise.all([
        prisma.postLike.count({ where: { postId: input.postId } }),
        prisma.comment.count({ where: { postId: input.postId } }),
      ]);
      return { likes, comments };
    }),

  // Comment management
  comments: {
    /**
     * Add comment to post
     */
    create: protectedProcedure
      .input(commentSchema)
      .handler(async ({ input, context }) => {
        const authorProfile = await prisma.alumniProfile.findUnique({
          where: { userId: context.user!.id },
          select: { id: true },
        });
        if (!authorProfile) throw new Error("Create your alumni profile first");
        return prisma.comment.create({
          data: {
            postId: input.postId,
            authorId: authorProfile.id,
            content: input.content,
          },
        });
      }),

    /**
     * Update own comment
     */
    update: protectedProcedure
      .input(
        z.object({
          id: idSchema,
          content: z.string().min(1, "Comment content is required").max(1000),
        })
      )
      .handler(async ({ input, context }) => {
        const comment = await prisma.comment.findUnique({
          where: { id: input.id },
          include: { author: { select: { userId: true } } },
        });
        if (!comment || comment.author.userId !== context.user!.id) {
          throw new Error("Not allowed");
        }
        return prisma.comment.update({
          where: { id: input.id },
          data: { content: input.content },
        });
      }),

    /**
     * Delete own comment
     */
    delete: protectedProcedure
      .input(z.object({ id: idSchema }))
      .handler(async ({ input, context }) => {
        const comment = await prisma.comment.findUnique({
          where: { id: input.id },
          include: { author: { select: { userId: true } } },
        });
        if (!comment || comment.author.userId !== context.user!.id) {
          throw new Error("Not allowed");
        }
        await prisma.comment.delete({ where: { id: input.id } });
        return { success: true };
      }),
  },

  // Moderation endpoints for organization admins
  moderation: {
    /**
     * Get posts requiring moderation (admin only)
     */
    getPending: protectedProcedure
      .input(
        z.object({
          page: z.number().int().min(1).default(1),
          limit: z.number().int().min(1).max(100).default(20),
        })
      )
      .handler(async ({ input, context }) => {
        const { page, limit, skip } = getPaginationParams(input);
        const where = {
          organizationId: context.session?.session.activeOrganizationId,
        } as any;
        const [data, total] = await Promise.all([
          prisma.post.findMany({ where, orderBy: { createdAt: "desc" }, skip, take: limit }),
          prisma.post.count({ where }),
        ]);
        return createPaginationResult(data, total, page, limit);
      }),

    /**
     * Moderate post content (admin only)
     */
    moderate: protectedProcedure
      .input(
        z.object({
          postId: idSchema,
          action: z.enum(["approve", "reject", "flag"]),
          reason: z.string().optional(),
        })
      )
      .handler(async ({ input, context }) => {
        const post = await prisma.post.findUnique({ where: { id: input.postId } });
        if (!post) throw new Error("Post not found");
        return {
          success: true,
          action: input.action,
          postId: input.postId,
          moderatedBy: context.user?.id,
          moderatedAt: new Date(),
        };
      }),
  },
};
