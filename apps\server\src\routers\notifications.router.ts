import { z } from "zod";
import { protectedProcedure } from "../lib/orpc";
import { prisma } from "../lib/database";
import {
  markNotificationReadSchema,
  notificationFilterSchema,
  idSchema,
} from "../lib/validation";
import { getPaginationParams, createPaginationResult } from "../lib/utils";

export const notificationsRouter = {
  /**
   * Get user notifications
   */
  list: protectedProcedure
    .input(notificationFilterSchema)
    .handler(async ({ input, context }) => {
      const { page, limit, skip } = getPaginationParams(input);
      const profile = await prisma.alumniProfile.findUnique({ where: { userId: context.user!.id }, select: { id: true } });
      if (!profile) throw new Error("Alumni profile not found");
      const where: any = { recipientId: profile.id };
      if (input.type) where.type = input.type;
      if (typeof input.isRead === "boolean") where.isRead = input.isRead;
      const [data, total] = await Promise.all([
        prisma.notification.findMany({ where, orderBy: { createdAt: "desc" }, skip, take: limit }),
        prisma.notification.count({ where }),
      ]);
      return createPaginationResult(data, total, page, limit);
    }),

  /**
   * Mark notification as read
   */
  markRead: protectedProcedure
    .input(markNotificationReadSchema)
    .handler(async ({ input, context }) => {
      const profile = await prisma.alumniProfile.findUnique({ where: { userId: context.user!.id }, select: { id: true } });
      const notif = await prisma.notification.findUnique({ where: { id: input.notificationId } });
      if (!profile || !notif || notif.recipientId !== profile.id) throw new Error("Not allowed");
      return prisma.notification.update({ where: { id: input.notificationId }, data: { isRead: true } });
    }),

  /**
   * Mark all notifications as read
   */
  markAllRead: protectedProcedure
    .input(
      z.object({
        organizationId: idSchema.optional(),
      })
    )
    .handler(async ({ input, context }) => {
      const profile = await prisma.alumniProfile.findUnique({ where: { userId: context.user!.id }, select: { id: true } });
      if (!profile) throw new Error("Alumni profile not found");
      await prisma.notification.updateMany({
        where: { recipientId: profile.id, organizationId: input.organizationId ?? undefined, isRead: false },
        data: { isRead: true },
      });
      return { success: true };
    }),

  /**
   * Delete notification
   */
  delete: protectedProcedure
    .input(z.object({ id: idSchema }))
    .handler(async ({ input, context }) => {
      const profile = await prisma.alumniProfile.findUnique({ where: { userId: context.user!.id }, select: { id: true } });
      const notif = await prisma.notification.findUnique({ where: { id: input.id } });
      if (!profile || !notif || notif.recipientId !== profile.id) throw new Error("Not allowed");
      await prisma.notification.delete({ where: { id: input.id } });
      return { success: true };
    }),

  /**
   * Get unread notification count
   */
  unreadCount: protectedProcedure
    .input(
      z.object({
        organizationId: idSchema.optional(),
      })
    )
    .handler(async ({ input, context }) => {
      const profile = await prisma.alumniProfile.findUnique({ where: { userId: context.user!.id }, select: { id: true } });
      if (!profile) throw new Error("Alumni profile not found");
      const count = await prisma.notification.count({ where: { recipientId: profile.id, organizationId: input.organizationId ?? undefined, isRead: false } });
      return { count } as any;
    }),

  /**
   * Get notification statistics
   */
  stats: protectedProcedure
    .input(
      z.object({
        organizationId: idSchema.optional(),
      })
    )
    .handler(async ({ input, context }) => {
      const profile = await prisma.alumniProfile.findUnique({ where: { userId: context.user!.id }, select: { id: true } });
      if (!profile) throw new Error("Alumni profile not found");
      const whereBase: any = { recipientId: profile.id };
      if (input.organizationId) whereBase.organizationId = input.organizationId;
      const [total, unread] = await Promise.all([
        prisma.notification.count({ where: whereBase }),
        prisma.notification.count({ where: { ...whereBase, isRead: false } }),
      ]);
      return { total, unread };
    }),
};
