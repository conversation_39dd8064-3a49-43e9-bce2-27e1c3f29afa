"use client"

import { useMemo, useState } from "react"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { orpc, client } from "@/utils/orpc"
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Pagination, PaginationContent, PaginationItem, PaginationNext, PaginationPrevious } from "@/components/ui/pagination"
import Link from "next/link"
import { Switch } from "@/components/ui/switch"
import { useForm } from "react-hook-form"

type FilterInput = {
  eventType?: "WORKSHOP" | "NETWORKING" | "CONFERENCE" | "WEBINAR" | "SOCIAL" | "FUNDRAISING" | "MENTORSHIP"
  isVirtual?: boolean
}

export default function EventList() {
  const [page, setPage] = useState(1)
  const [filters, setFilters] = useState<FilterInput>({})
  const queryClient = useQueryClient()

  const list = useQuery(orpc.events.list.queryOptions({ input: { page, limit: 10, ...filters } }))

  const totalPages = useMemo(() => {
    const total = (list.data as any)?.total ?? 0
    const limit = (list.data as any)?.limit ?? 10
    return Math.max(1, Math.ceil(total / limit))
  }, [list.data])

  const register = useMutation({
    mutationFn: async (eventId: string) => client.events.register({ eventId }),
    onSuccess: async () => queryClient.invalidateQueries(),
  })

  const form = useForm<FilterInput>({ defaultValues: filters })

  return (
    <div className="space-y-4">
      <div className="rounded-lg border bg-card p-4">
        <Form {...form}>
          <form className="grid gap-4 md:grid-cols-4" onSubmit={form.handleSubmit((v) => { setPage(1); setFilters(v) })}>
            <FormField name="eventType" control={form.control} render={({ field }) => (
              <FormItem>
                <FormLabel>Type</FormLabel>
                <FormControl>
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger><SelectValue placeholder="All" /></SelectTrigger>
                    <SelectContent>
                      <SelectItem value="WORKSHOP">Workshop</SelectItem>
                      <SelectItem value="NETWORKING">Networking</SelectItem>
                      <SelectItem value="CONFERENCE">Conference</SelectItem>
                      <SelectItem value="WEBINAR">Webinar</SelectItem>
                      <SelectItem value="SOCIAL">Social</SelectItem>
                      <SelectItem value="FUNDRAISING">Fundraising</SelectItem>
                      <SelectItem value="MENTORSHIP">Mentorship</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
              </FormItem>
            )} />
            <FormField name="isVirtual" control={form.control} render={({ field }) => (
              <FormItem>
                <FormLabel>Virtual</FormLabel>
                <FormControl><div className="flex items-center gap-3"><Switch checked={!!field.value} onCheckedChange={field.onChange} /></div></FormControl>
              </FormItem>
            )} />
            <div className="md:col-span-2 flex items-end justify-end">
              <Button type="submit">Apply</Button>
            </div>
          </form>
        </Form>
      </div>

      <div className="grid gap-3">
        {list.isLoading && (<div className="text-sm text-muted-foreground">Loading...</div>)}
        {(list.data as any)?.data?.map((e: any) => (
          <div key={e.id} className="rounded-lg border bg-card p-4">
            <div className="flex items-center justify-between">
              <div className="font-medium truncate">{e.title}</div>
              <div className="text-xs text-muted-foreground">{new Date(e.startDateTime).toLocaleString()}</div>
            </div>
            <div className="mt-1 text-sm text-muted-foreground truncate">{e.location || (e.isVirtual ? "Virtual" : "")}</div>
            <div className="mt-2 text-sm whitespace-pre-wrap line-clamp-3">{e.description}</div>
            <div className="mt-3 flex items-center gap-2">
              <Button size="sm" onClick={() => register.mutate(e.id)} disabled={register.isPending}>RSVP</Button>
              <Button size="sm" variant="outline" asChild>
                <Link href={`/events/${e.id}`}>Open</Link>
              </Button>
            </div>
          </div>
        ))}
      </div>

      <div className="flex items-center justify-end">
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious href="#" onClick={(e) => { e.preventDefault(); setPage((p) => Math.max(1, p - 1)) }} />
            </PaginationItem>
            <PaginationItem>
              <span className="text-sm text-muted-foreground px-3 py-2">Page {page} of {totalPages}</span>
            </PaginationItem>
            <PaginationItem>
              <PaginationNext href="#" onClick={(e) => { e.preventDefault(); setPage((p) => Math.min(totalPages, p + 1)) }} />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </div>
    </div>
  )
}


