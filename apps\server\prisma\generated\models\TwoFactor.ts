
/* !!! This is code generated by <PERSON>risma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `TwoFactor` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model TwoFactor
 * 
 */
export type TwoFactorModel = runtime.Types.Result.DefaultSelection<Prisma.$TwoFactorPayload>

export type AggregateTwoFactor = {
  _count: TwoFactorCountAggregateOutputType | null
  _min: TwoFactorMinAggregateOutputType | null
  _max: TwoFactorMaxAggregateOutputType | null
}

export type TwoFactorMinAggregateOutputType = {
  id: string | null
  secret: string | null
  backupCodes: string | null
  userId: string | null
}

export type TwoFactorMaxAggregateOutputType = {
  id: string | null
  secret: string | null
  backupCodes: string | null
  userId: string | null
}

export type TwoFactorCountAggregateOutputType = {
  id: number
  secret: number
  backupCodes: number
  userId: number
  _all: number
}


export type TwoFactorMinAggregateInputType = {
  id?: true
  secret?: true
  backupCodes?: true
  userId?: true
}

export type TwoFactorMaxAggregateInputType = {
  id?: true
  secret?: true
  backupCodes?: true
  userId?: true
}

export type TwoFactorCountAggregateInputType = {
  id?: true
  secret?: true
  backupCodes?: true
  userId?: true
  _all?: true
}

export type TwoFactorAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which TwoFactor to aggregate.
   */
  where?: Prisma.TwoFactorWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of TwoFactors to fetch.
   */
  orderBy?: Prisma.TwoFactorOrderByWithRelationInput | Prisma.TwoFactorOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.TwoFactorWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` TwoFactors from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` TwoFactors.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned TwoFactors
  **/
  _count?: true | TwoFactorCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: TwoFactorMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: TwoFactorMaxAggregateInputType
}

export type GetTwoFactorAggregateType<T extends TwoFactorAggregateArgs> = {
      [P in keyof T & keyof AggregateTwoFactor]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateTwoFactor[P]>
    : Prisma.GetScalarType<T[P], AggregateTwoFactor[P]>
}




export type TwoFactorGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.TwoFactorWhereInput
  orderBy?: Prisma.TwoFactorOrderByWithAggregationInput | Prisma.TwoFactorOrderByWithAggregationInput[]
  by: Prisma.TwoFactorScalarFieldEnum[] | Prisma.TwoFactorScalarFieldEnum
  having?: Prisma.TwoFactorScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: TwoFactorCountAggregateInputType | true
  _min?: TwoFactorMinAggregateInputType
  _max?: TwoFactorMaxAggregateInputType
}

export type TwoFactorGroupByOutputType = {
  id: string
  secret: string
  backupCodes: string
  userId: string
  _count: TwoFactorCountAggregateOutputType | null
  _min: TwoFactorMinAggregateOutputType | null
  _max: TwoFactorMaxAggregateOutputType | null
}

type GetTwoFactorGroupByPayload<T extends TwoFactorGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<TwoFactorGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof TwoFactorGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], TwoFactorGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], TwoFactorGroupByOutputType[P]>
      }
    >
  >



export type TwoFactorWhereInput = {
  AND?: Prisma.TwoFactorWhereInput | Prisma.TwoFactorWhereInput[]
  OR?: Prisma.TwoFactorWhereInput[]
  NOT?: Prisma.TwoFactorWhereInput | Prisma.TwoFactorWhereInput[]
  id?: Prisma.StringFilter<"TwoFactor"> | string
  secret?: Prisma.StringFilter<"TwoFactor"> | string
  backupCodes?: Prisma.StringFilter<"TwoFactor"> | string
  userId?: Prisma.StringFilter<"TwoFactor"> | string
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
}

export type TwoFactorOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  secret?: Prisma.SortOrder
  backupCodes?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  user?: Prisma.UserOrderByWithRelationInput
  _relevance?: Prisma.TwoFactorOrderByRelevanceInput
}

export type TwoFactorWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  AND?: Prisma.TwoFactorWhereInput | Prisma.TwoFactorWhereInput[]
  OR?: Prisma.TwoFactorWhereInput[]
  NOT?: Prisma.TwoFactorWhereInput | Prisma.TwoFactorWhereInput[]
  secret?: Prisma.StringFilter<"TwoFactor"> | string
  backupCodes?: Prisma.StringFilter<"TwoFactor"> | string
  userId?: Prisma.StringFilter<"TwoFactor"> | string
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
}, "id">

export type TwoFactorOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  secret?: Prisma.SortOrder
  backupCodes?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  _count?: Prisma.TwoFactorCountOrderByAggregateInput
  _max?: Prisma.TwoFactorMaxOrderByAggregateInput
  _min?: Prisma.TwoFactorMinOrderByAggregateInput
}

export type TwoFactorScalarWhereWithAggregatesInput = {
  AND?: Prisma.TwoFactorScalarWhereWithAggregatesInput | Prisma.TwoFactorScalarWhereWithAggregatesInput[]
  OR?: Prisma.TwoFactorScalarWhereWithAggregatesInput[]
  NOT?: Prisma.TwoFactorScalarWhereWithAggregatesInput | Prisma.TwoFactorScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"TwoFactor"> | string
  secret?: Prisma.StringWithAggregatesFilter<"TwoFactor"> | string
  backupCodes?: Prisma.StringWithAggregatesFilter<"TwoFactor"> | string
  userId?: Prisma.StringWithAggregatesFilter<"TwoFactor"> | string
}

export type TwoFactorCreateInput = {
  id?: string
  secret: string
  backupCodes: string
  user: Prisma.UserCreateNestedOneWithoutTwoFactorInput
}

export type TwoFactorUncheckedCreateInput = {
  id?: string
  secret: string
  backupCodes: string
  userId: string
}

export type TwoFactorUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  secret?: Prisma.StringFieldUpdateOperationsInput | string
  backupCodes?: Prisma.StringFieldUpdateOperationsInput | string
  user?: Prisma.UserUpdateOneRequiredWithoutTwoFactorNestedInput
}

export type TwoFactorUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  secret?: Prisma.StringFieldUpdateOperationsInput | string
  backupCodes?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type TwoFactorCreateManyInput = {
  id?: string
  secret: string
  backupCodes: string
  userId: string
}

export type TwoFactorUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  secret?: Prisma.StringFieldUpdateOperationsInput | string
  backupCodes?: Prisma.StringFieldUpdateOperationsInput | string
}

export type TwoFactorUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  secret?: Prisma.StringFieldUpdateOperationsInput | string
  backupCodes?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type TwoFactorListRelationFilter = {
  every?: Prisma.TwoFactorWhereInput
  some?: Prisma.TwoFactorWhereInput
  none?: Prisma.TwoFactorWhereInput
}

export type TwoFactorOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type TwoFactorOrderByRelevanceInput = {
  fields: Prisma.TwoFactorOrderByRelevanceFieldEnum | Prisma.TwoFactorOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type TwoFactorCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  secret?: Prisma.SortOrder
  backupCodes?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type TwoFactorMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  secret?: Prisma.SortOrder
  backupCodes?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type TwoFactorMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  secret?: Prisma.SortOrder
  backupCodes?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type TwoFactorCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.TwoFactorCreateWithoutUserInput, Prisma.TwoFactorUncheckedCreateWithoutUserInput> | Prisma.TwoFactorCreateWithoutUserInput[] | Prisma.TwoFactorUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.TwoFactorCreateOrConnectWithoutUserInput | Prisma.TwoFactorCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.TwoFactorCreateManyUserInputEnvelope
  connect?: Prisma.TwoFactorWhereUniqueInput | Prisma.TwoFactorWhereUniqueInput[]
}

export type TwoFactorUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.TwoFactorCreateWithoutUserInput, Prisma.TwoFactorUncheckedCreateWithoutUserInput> | Prisma.TwoFactorCreateWithoutUserInput[] | Prisma.TwoFactorUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.TwoFactorCreateOrConnectWithoutUserInput | Prisma.TwoFactorCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.TwoFactorCreateManyUserInputEnvelope
  connect?: Prisma.TwoFactorWhereUniqueInput | Prisma.TwoFactorWhereUniqueInput[]
}

export type TwoFactorUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.TwoFactorCreateWithoutUserInput, Prisma.TwoFactorUncheckedCreateWithoutUserInput> | Prisma.TwoFactorCreateWithoutUserInput[] | Prisma.TwoFactorUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.TwoFactorCreateOrConnectWithoutUserInput | Prisma.TwoFactorCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.TwoFactorUpsertWithWhereUniqueWithoutUserInput | Prisma.TwoFactorUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.TwoFactorCreateManyUserInputEnvelope
  set?: Prisma.TwoFactorWhereUniqueInput | Prisma.TwoFactorWhereUniqueInput[]
  disconnect?: Prisma.TwoFactorWhereUniqueInput | Prisma.TwoFactorWhereUniqueInput[]
  delete?: Prisma.TwoFactorWhereUniqueInput | Prisma.TwoFactorWhereUniqueInput[]
  connect?: Prisma.TwoFactorWhereUniqueInput | Prisma.TwoFactorWhereUniqueInput[]
  update?: Prisma.TwoFactorUpdateWithWhereUniqueWithoutUserInput | Prisma.TwoFactorUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.TwoFactorUpdateManyWithWhereWithoutUserInput | Prisma.TwoFactorUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.TwoFactorScalarWhereInput | Prisma.TwoFactorScalarWhereInput[]
}

export type TwoFactorUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.TwoFactorCreateWithoutUserInput, Prisma.TwoFactorUncheckedCreateWithoutUserInput> | Prisma.TwoFactorCreateWithoutUserInput[] | Prisma.TwoFactorUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.TwoFactorCreateOrConnectWithoutUserInput | Prisma.TwoFactorCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.TwoFactorUpsertWithWhereUniqueWithoutUserInput | Prisma.TwoFactorUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.TwoFactorCreateManyUserInputEnvelope
  set?: Prisma.TwoFactorWhereUniqueInput | Prisma.TwoFactorWhereUniqueInput[]
  disconnect?: Prisma.TwoFactorWhereUniqueInput | Prisma.TwoFactorWhereUniqueInput[]
  delete?: Prisma.TwoFactorWhereUniqueInput | Prisma.TwoFactorWhereUniqueInput[]
  connect?: Prisma.TwoFactorWhereUniqueInput | Prisma.TwoFactorWhereUniqueInput[]
  update?: Prisma.TwoFactorUpdateWithWhereUniqueWithoutUserInput | Prisma.TwoFactorUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.TwoFactorUpdateManyWithWhereWithoutUserInput | Prisma.TwoFactorUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.TwoFactorScalarWhereInput | Prisma.TwoFactorScalarWhereInput[]
}

export type TwoFactorCreateWithoutUserInput = {
  id?: string
  secret: string
  backupCodes: string
}

export type TwoFactorUncheckedCreateWithoutUserInput = {
  id?: string
  secret: string
  backupCodes: string
}

export type TwoFactorCreateOrConnectWithoutUserInput = {
  where: Prisma.TwoFactorWhereUniqueInput
  create: Prisma.XOR<Prisma.TwoFactorCreateWithoutUserInput, Prisma.TwoFactorUncheckedCreateWithoutUserInput>
}

export type TwoFactorCreateManyUserInputEnvelope = {
  data: Prisma.TwoFactorCreateManyUserInput | Prisma.TwoFactorCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type TwoFactorUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.TwoFactorWhereUniqueInput
  update: Prisma.XOR<Prisma.TwoFactorUpdateWithoutUserInput, Prisma.TwoFactorUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.TwoFactorCreateWithoutUserInput, Prisma.TwoFactorUncheckedCreateWithoutUserInput>
}

export type TwoFactorUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.TwoFactorWhereUniqueInput
  data: Prisma.XOR<Prisma.TwoFactorUpdateWithoutUserInput, Prisma.TwoFactorUncheckedUpdateWithoutUserInput>
}

export type TwoFactorUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.TwoFactorScalarWhereInput
  data: Prisma.XOR<Prisma.TwoFactorUpdateManyMutationInput, Prisma.TwoFactorUncheckedUpdateManyWithoutUserInput>
}

export type TwoFactorScalarWhereInput = {
  AND?: Prisma.TwoFactorScalarWhereInput | Prisma.TwoFactorScalarWhereInput[]
  OR?: Prisma.TwoFactorScalarWhereInput[]
  NOT?: Prisma.TwoFactorScalarWhereInput | Prisma.TwoFactorScalarWhereInput[]
  id?: Prisma.StringFilter<"TwoFactor"> | string
  secret?: Prisma.StringFilter<"TwoFactor"> | string
  backupCodes?: Prisma.StringFilter<"TwoFactor"> | string
  userId?: Prisma.StringFilter<"TwoFactor"> | string
}

export type TwoFactorCreateManyUserInput = {
  id?: string
  secret: string
  backupCodes: string
}

export type TwoFactorUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  secret?: Prisma.StringFieldUpdateOperationsInput | string
  backupCodes?: Prisma.StringFieldUpdateOperationsInput | string
}

export type TwoFactorUncheckedUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  secret?: Prisma.StringFieldUpdateOperationsInput | string
  backupCodes?: Prisma.StringFieldUpdateOperationsInput | string
}

export type TwoFactorUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  secret?: Prisma.StringFieldUpdateOperationsInput | string
  backupCodes?: Prisma.StringFieldUpdateOperationsInput | string
}



export type TwoFactorSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  secret?: boolean
  backupCodes?: boolean
  userId?: boolean
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}, ExtArgs["result"]["twoFactor"]>



export type TwoFactorSelectScalar = {
  id?: boolean
  secret?: boolean
  backupCodes?: boolean
  userId?: boolean
}

export type TwoFactorOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "secret" | "backupCodes" | "userId", ExtArgs["result"]["twoFactor"]>
export type TwoFactorInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}

export type $TwoFactorPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "TwoFactor"
  objects: {
    user: Prisma.$UserPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    secret: string
    backupCodes: string
    userId: string
  }, ExtArgs["result"]["twoFactor"]>
  composites: {}
}

export type TwoFactorGetPayload<S extends boolean | null | undefined | TwoFactorDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$TwoFactorPayload, S>

export type TwoFactorCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<TwoFactorFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: TwoFactorCountAggregateInputType | true
  }

export interface TwoFactorDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['TwoFactor'], meta: { name: 'TwoFactor' } }
  /**
   * Find zero or one TwoFactor that matches the filter.
   * @param {TwoFactorFindUniqueArgs} args - Arguments to find a TwoFactor
   * @example
   * // Get one TwoFactor
   * const twoFactor = await prisma.twoFactor.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends TwoFactorFindUniqueArgs>(args: Prisma.SelectSubset<T, TwoFactorFindUniqueArgs<ExtArgs>>): Prisma.Prisma__TwoFactorClient<runtime.Types.Result.GetResult<Prisma.$TwoFactorPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one TwoFactor that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {TwoFactorFindUniqueOrThrowArgs} args - Arguments to find a TwoFactor
   * @example
   * // Get one TwoFactor
   * const twoFactor = await prisma.twoFactor.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends TwoFactorFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, TwoFactorFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__TwoFactorClient<runtime.Types.Result.GetResult<Prisma.$TwoFactorPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first TwoFactor that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TwoFactorFindFirstArgs} args - Arguments to find a TwoFactor
   * @example
   * // Get one TwoFactor
   * const twoFactor = await prisma.twoFactor.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends TwoFactorFindFirstArgs>(args?: Prisma.SelectSubset<T, TwoFactorFindFirstArgs<ExtArgs>>): Prisma.Prisma__TwoFactorClient<runtime.Types.Result.GetResult<Prisma.$TwoFactorPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first TwoFactor that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TwoFactorFindFirstOrThrowArgs} args - Arguments to find a TwoFactor
   * @example
   * // Get one TwoFactor
   * const twoFactor = await prisma.twoFactor.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends TwoFactorFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, TwoFactorFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__TwoFactorClient<runtime.Types.Result.GetResult<Prisma.$TwoFactorPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more TwoFactors that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TwoFactorFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all TwoFactors
   * const twoFactors = await prisma.twoFactor.findMany()
   * 
   * // Get first 10 TwoFactors
   * const twoFactors = await prisma.twoFactor.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const twoFactorWithIdOnly = await prisma.twoFactor.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends TwoFactorFindManyArgs>(args?: Prisma.SelectSubset<T, TwoFactorFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TwoFactorPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a TwoFactor.
   * @param {TwoFactorCreateArgs} args - Arguments to create a TwoFactor.
   * @example
   * // Create one TwoFactor
   * const TwoFactor = await prisma.twoFactor.create({
   *   data: {
   *     // ... data to create a TwoFactor
   *   }
   * })
   * 
   */
  create<T extends TwoFactorCreateArgs>(args: Prisma.SelectSubset<T, TwoFactorCreateArgs<ExtArgs>>): Prisma.Prisma__TwoFactorClient<runtime.Types.Result.GetResult<Prisma.$TwoFactorPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many TwoFactors.
   * @param {TwoFactorCreateManyArgs} args - Arguments to create many TwoFactors.
   * @example
   * // Create many TwoFactors
   * const twoFactor = await prisma.twoFactor.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends TwoFactorCreateManyArgs>(args?: Prisma.SelectSubset<T, TwoFactorCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a TwoFactor.
   * @param {TwoFactorDeleteArgs} args - Arguments to delete one TwoFactor.
   * @example
   * // Delete one TwoFactor
   * const TwoFactor = await prisma.twoFactor.delete({
   *   where: {
   *     // ... filter to delete one TwoFactor
   *   }
   * })
   * 
   */
  delete<T extends TwoFactorDeleteArgs>(args: Prisma.SelectSubset<T, TwoFactorDeleteArgs<ExtArgs>>): Prisma.Prisma__TwoFactorClient<runtime.Types.Result.GetResult<Prisma.$TwoFactorPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one TwoFactor.
   * @param {TwoFactorUpdateArgs} args - Arguments to update one TwoFactor.
   * @example
   * // Update one TwoFactor
   * const twoFactor = await prisma.twoFactor.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends TwoFactorUpdateArgs>(args: Prisma.SelectSubset<T, TwoFactorUpdateArgs<ExtArgs>>): Prisma.Prisma__TwoFactorClient<runtime.Types.Result.GetResult<Prisma.$TwoFactorPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more TwoFactors.
   * @param {TwoFactorDeleteManyArgs} args - Arguments to filter TwoFactors to delete.
   * @example
   * // Delete a few TwoFactors
   * const { count } = await prisma.twoFactor.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends TwoFactorDeleteManyArgs>(args?: Prisma.SelectSubset<T, TwoFactorDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more TwoFactors.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TwoFactorUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many TwoFactors
   * const twoFactor = await prisma.twoFactor.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends TwoFactorUpdateManyArgs>(args: Prisma.SelectSubset<T, TwoFactorUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one TwoFactor.
   * @param {TwoFactorUpsertArgs} args - Arguments to update or create a TwoFactor.
   * @example
   * // Update or create a TwoFactor
   * const twoFactor = await prisma.twoFactor.upsert({
   *   create: {
   *     // ... data to create a TwoFactor
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the TwoFactor we want to update
   *   }
   * })
   */
  upsert<T extends TwoFactorUpsertArgs>(args: Prisma.SelectSubset<T, TwoFactorUpsertArgs<ExtArgs>>): Prisma.Prisma__TwoFactorClient<runtime.Types.Result.GetResult<Prisma.$TwoFactorPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of TwoFactors.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TwoFactorCountArgs} args - Arguments to filter TwoFactors to count.
   * @example
   * // Count the number of TwoFactors
   * const count = await prisma.twoFactor.count({
   *   where: {
   *     // ... the filter for the TwoFactors we want to count
   *   }
   * })
  **/
  count<T extends TwoFactorCountArgs>(
    args?: Prisma.Subset<T, TwoFactorCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], TwoFactorCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a TwoFactor.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TwoFactorAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends TwoFactorAggregateArgs>(args: Prisma.Subset<T, TwoFactorAggregateArgs>): Prisma.PrismaPromise<GetTwoFactorAggregateType<T>>

  /**
   * Group by TwoFactor.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TwoFactorGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends TwoFactorGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: TwoFactorGroupByArgs['orderBy'] }
      : { orderBy?: TwoFactorGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, TwoFactorGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetTwoFactorGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the TwoFactor model
 */
readonly fields: TwoFactorFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for TwoFactor.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__TwoFactorClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  user<T extends Prisma.UserDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.UserDefaultArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the TwoFactor model
 */
export interface TwoFactorFieldRefs {
  readonly id: Prisma.FieldRef<"TwoFactor", 'String'>
  readonly secret: Prisma.FieldRef<"TwoFactor", 'String'>
  readonly backupCodes: Prisma.FieldRef<"TwoFactor", 'String'>
  readonly userId: Prisma.FieldRef<"TwoFactor", 'String'>
}
    

// Custom InputTypes
/**
 * TwoFactor findUnique
 */
export type TwoFactorFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TwoFactor
   */
  select?: Prisma.TwoFactorSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TwoFactor
   */
  omit?: Prisma.TwoFactorOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TwoFactorInclude<ExtArgs> | null
  /**
   * Filter, which TwoFactor to fetch.
   */
  where: Prisma.TwoFactorWhereUniqueInput
}

/**
 * TwoFactor findUniqueOrThrow
 */
export type TwoFactorFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TwoFactor
   */
  select?: Prisma.TwoFactorSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TwoFactor
   */
  omit?: Prisma.TwoFactorOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TwoFactorInclude<ExtArgs> | null
  /**
   * Filter, which TwoFactor to fetch.
   */
  where: Prisma.TwoFactorWhereUniqueInput
}

/**
 * TwoFactor findFirst
 */
export type TwoFactorFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TwoFactor
   */
  select?: Prisma.TwoFactorSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TwoFactor
   */
  omit?: Prisma.TwoFactorOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TwoFactorInclude<ExtArgs> | null
  /**
   * Filter, which TwoFactor to fetch.
   */
  where?: Prisma.TwoFactorWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of TwoFactors to fetch.
   */
  orderBy?: Prisma.TwoFactorOrderByWithRelationInput | Prisma.TwoFactorOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for TwoFactors.
   */
  cursor?: Prisma.TwoFactorWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` TwoFactors from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` TwoFactors.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of TwoFactors.
   */
  distinct?: Prisma.TwoFactorScalarFieldEnum | Prisma.TwoFactorScalarFieldEnum[]
}

/**
 * TwoFactor findFirstOrThrow
 */
export type TwoFactorFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TwoFactor
   */
  select?: Prisma.TwoFactorSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TwoFactor
   */
  omit?: Prisma.TwoFactorOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TwoFactorInclude<ExtArgs> | null
  /**
   * Filter, which TwoFactor to fetch.
   */
  where?: Prisma.TwoFactorWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of TwoFactors to fetch.
   */
  orderBy?: Prisma.TwoFactorOrderByWithRelationInput | Prisma.TwoFactorOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for TwoFactors.
   */
  cursor?: Prisma.TwoFactorWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` TwoFactors from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` TwoFactors.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of TwoFactors.
   */
  distinct?: Prisma.TwoFactorScalarFieldEnum | Prisma.TwoFactorScalarFieldEnum[]
}

/**
 * TwoFactor findMany
 */
export type TwoFactorFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TwoFactor
   */
  select?: Prisma.TwoFactorSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TwoFactor
   */
  omit?: Prisma.TwoFactorOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TwoFactorInclude<ExtArgs> | null
  /**
   * Filter, which TwoFactors to fetch.
   */
  where?: Prisma.TwoFactorWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of TwoFactors to fetch.
   */
  orderBy?: Prisma.TwoFactorOrderByWithRelationInput | Prisma.TwoFactorOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing TwoFactors.
   */
  cursor?: Prisma.TwoFactorWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` TwoFactors from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` TwoFactors.
   */
  skip?: number
  distinct?: Prisma.TwoFactorScalarFieldEnum | Prisma.TwoFactorScalarFieldEnum[]
}

/**
 * TwoFactor create
 */
export type TwoFactorCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TwoFactor
   */
  select?: Prisma.TwoFactorSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TwoFactor
   */
  omit?: Prisma.TwoFactorOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TwoFactorInclude<ExtArgs> | null
  /**
   * The data needed to create a TwoFactor.
   */
  data: Prisma.XOR<Prisma.TwoFactorCreateInput, Prisma.TwoFactorUncheckedCreateInput>
}

/**
 * TwoFactor createMany
 */
export type TwoFactorCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many TwoFactors.
   */
  data: Prisma.TwoFactorCreateManyInput | Prisma.TwoFactorCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * TwoFactor update
 */
export type TwoFactorUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TwoFactor
   */
  select?: Prisma.TwoFactorSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TwoFactor
   */
  omit?: Prisma.TwoFactorOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TwoFactorInclude<ExtArgs> | null
  /**
   * The data needed to update a TwoFactor.
   */
  data: Prisma.XOR<Prisma.TwoFactorUpdateInput, Prisma.TwoFactorUncheckedUpdateInput>
  /**
   * Choose, which TwoFactor to update.
   */
  where: Prisma.TwoFactorWhereUniqueInput
}

/**
 * TwoFactor updateMany
 */
export type TwoFactorUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update TwoFactors.
   */
  data: Prisma.XOR<Prisma.TwoFactorUpdateManyMutationInput, Prisma.TwoFactorUncheckedUpdateManyInput>
  /**
   * Filter which TwoFactors to update
   */
  where?: Prisma.TwoFactorWhereInput
  /**
   * Limit how many TwoFactors to update.
   */
  limit?: number
}

/**
 * TwoFactor upsert
 */
export type TwoFactorUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TwoFactor
   */
  select?: Prisma.TwoFactorSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TwoFactor
   */
  omit?: Prisma.TwoFactorOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TwoFactorInclude<ExtArgs> | null
  /**
   * The filter to search for the TwoFactor to update in case it exists.
   */
  where: Prisma.TwoFactorWhereUniqueInput
  /**
   * In case the TwoFactor found by the `where` argument doesn't exist, create a new TwoFactor with this data.
   */
  create: Prisma.XOR<Prisma.TwoFactorCreateInput, Prisma.TwoFactorUncheckedCreateInput>
  /**
   * In case the TwoFactor was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.TwoFactorUpdateInput, Prisma.TwoFactorUncheckedUpdateInput>
}

/**
 * TwoFactor delete
 */
export type TwoFactorDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TwoFactor
   */
  select?: Prisma.TwoFactorSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TwoFactor
   */
  omit?: Prisma.TwoFactorOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TwoFactorInclude<ExtArgs> | null
  /**
   * Filter which TwoFactor to delete.
   */
  where: Prisma.TwoFactorWhereUniqueInput
}

/**
 * TwoFactor deleteMany
 */
export type TwoFactorDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which TwoFactors to delete
   */
  where?: Prisma.TwoFactorWhereInput
  /**
   * Limit how many TwoFactors to delete.
   */
  limit?: number
}

/**
 * TwoFactor without action
 */
export type TwoFactorDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TwoFactor
   */
  select?: Prisma.TwoFactorSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TwoFactor
   */
  omit?: Prisma.TwoFactorOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TwoFactorInclude<ExtArgs> | null
}
