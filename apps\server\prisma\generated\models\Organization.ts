
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `Organization` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model Organization
 * 
 */
export type OrganizationModel = runtime.Types.Result.DefaultSelection<Prisma.$OrganizationPayload>

export type AggregateOrganization = {
  _count: OrganizationCountAggregateOutputType | null
  _min: OrganizationMinAggregateOutputType | null
  _max: OrganizationMaxAggregateOutputType | null
}

export type OrganizationMinAggregateOutputType = {
  id: string | null
  name: string | null
  slug: string | null
  logo: string | null
  createdAt: Date | null
  metadata: string | null
}

export type OrganizationMaxAggregateOutputType = {
  id: string | null
  name: string | null
  slug: string | null
  logo: string | null
  createdAt: Date | null
  metadata: string | null
}

export type OrganizationCountAggregateOutputType = {
  id: number
  name: number
  slug: number
  logo: number
  createdAt: number
  metadata: number
  _all: number
}


export type OrganizationMinAggregateInputType = {
  id?: true
  name?: true
  slug?: true
  logo?: true
  createdAt?: true
  metadata?: true
}

export type OrganizationMaxAggregateInputType = {
  id?: true
  name?: true
  slug?: true
  logo?: true
  createdAt?: true
  metadata?: true
}

export type OrganizationCountAggregateInputType = {
  id?: true
  name?: true
  slug?: true
  logo?: true
  createdAt?: true
  metadata?: true
  _all?: true
}

export type OrganizationAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Organization to aggregate.
   */
  where?: Prisma.OrganizationWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Organizations to fetch.
   */
  orderBy?: Prisma.OrganizationOrderByWithRelationInput | Prisma.OrganizationOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.OrganizationWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Organizations from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Organizations.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Organizations
  **/
  _count?: true | OrganizationCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: OrganizationMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: OrganizationMaxAggregateInputType
}

export type GetOrganizationAggregateType<T extends OrganizationAggregateArgs> = {
      [P in keyof T & keyof AggregateOrganization]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateOrganization[P]>
    : Prisma.GetScalarType<T[P], AggregateOrganization[P]>
}




export type OrganizationGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.OrganizationWhereInput
  orderBy?: Prisma.OrganizationOrderByWithAggregationInput | Prisma.OrganizationOrderByWithAggregationInput[]
  by: Prisma.OrganizationScalarFieldEnum[] | Prisma.OrganizationScalarFieldEnum
  having?: Prisma.OrganizationScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: OrganizationCountAggregateInputType | true
  _min?: OrganizationMinAggregateInputType
  _max?: OrganizationMaxAggregateInputType
}

export type OrganizationGroupByOutputType = {
  id: string
  name: string
  slug: string | null
  logo: string | null
  createdAt: Date
  metadata: string | null
  _count: OrganizationCountAggregateOutputType | null
  _min: OrganizationMinAggregateOutputType | null
  _max: OrganizationMaxAggregateOutputType | null
}

type GetOrganizationGroupByPayload<T extends OrganizationGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<OrganizationGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof OrganizationGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], OrganizationGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], OrganizationGroupByOutputType[P]>
      }
    >
  >



export type OrganizationWhereInput = {
  AND?: Prisma.OrganizationWhereInput | Prisma.OrganizationWhereInput[]
  OR?: Prisma.OrganizationWhereInput[]
  NOT?: Prisma.OrganizationWhereInput | Prisma.OrganizationWhereInput[]
  id?: Prisma.StringFilter<"Organization"> | string
  name?: Prisma.StringFilter<"Organization"> | string
  slug?: Prisma.StringNullableFilter<"Organization"> | string | null
  logo?: Prisma.StringNullableFilter<"Organization"> | string | null
  createdAt?: Prisma.DateTimeFilter<"Organization"> | Date | string
  metadata?: Prisma.StringNullableFilter<"Organization"> | string | null
  members?: Prisma.MemberListRelationFilter
  invitations?: Prisma.InvitationListRelationFilter
  notifications?: Prisma.NotificationListRelationFilter
  alumniProfiles?: Prisma.AlumniProfileListRelationFilter
  events?: Prisma.EventListRelationFilter
  newsArticles?: Prisma.NewsArticleListRelationFilter
  donations?: Prisma.DonationListRelationFilter
  posts?: Prisma.PostListRelationFilter
}

export type OrganizationOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  slug?: Prisma.SortOrderInput | Prisma.SortOrder
  logo?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  metadata?: Prisma.SortOrderInput | Prisma.SortOrder
  members?: Prisma.MemberOrderByRelationAggregateInput
  invitations?: Prisma.InvitationOrderByRelationAggregateInput
  notifications?: Prisma.NotificationOrderByRelationAggregateInput
  alumniProfiles?: Prisma.AlumniProfileOrderByRelationAggregateInput
  events?: Prisma.EventOrderByRelationAggregateInput
  newsArticles?: Prisma.NewsArticleOrderByRelationAggregateInput
  donations?: Prisma.DonationOrderByRelationAggregateInput
  posts?: Prisma.PostOrderByRelationAggregateInput
  _relevance?: Prisma.OrganizationOrderByRelevanceInput
}

export type OrganizationWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  slug?: string
  AND?: Prisma.OrganizationWhereInput | Prisma.OrganizationWhereInput[]
  OR?: Prisma.OrganizationWhereInput[]
  NOT?: Prisma.OrganizationWhereInput | Prisma.OrganizationWhereInput[]
  name?: Prisma.StringFilter<"Organization"> | string
  logo?: Prisma.StringNullableFilter<"Organization"> | string | null
  createdAt?: Prisma.DateTimeFilter<"Organization"> | Date | string
  metadata?: Prisma.StringNullableFilter<"Organization"> | string | null
  members?: Prisma.MemberListRelationFilter
  invitations?: Prisma.InvitationListRelationFilter
  notifications?: Prisma.NotificationListRelationFilter
  alumniProfiles?: Prisma.AlumniProfileListRelationFilter
  events?: Prisma.EventListRelationFilter
  newsArticles?: Prisma.NewsArticleListRelationFilter
  donations?: Prisma.DonationListRelationFilter
  posts?: Prisma.PostListRelationFilter
}, "id" | "slug">

export type OrganizationOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  slug?: Prisma.SortOrderInput | Prisma.SortOrder
  logo?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  metadata?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.OrganizationCountOrderByAggregateInput
  _max?: Prisma.OrganizationMaxOrderByAggregateInput
  _min?: Prisma.OrganizationMinOrderByAggregateInput
}

export type OrganizationScalarWhereWithAggregatesInput = {
  AND?: Prisma.OrganizationScalarWhereWithAggregatesInput | Prisma.OrganizationScalarWhereWithAggregatesInput[]
  OR?: Prisma.OrganizationScalarWhereWithAggregatesInput[]
  NOT?: Prisma.OrganizationScalarWhereWithAggregatesInput | Prisma.OrganizationScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"Organization"> | string
  name?: Prisma.StringWithAggregatesFilter<"Organization"> | string
  slug?: Prisma.StringNullableWithAggregatesFilter<"Organization"> | string | null
  logo?: Prisma.StringNullableWithAggregatesFilter<"Organization"> | string | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"Organization"> | Date | string
  metadata?: Prisma.StringNullableWithAggregatesFilter<"Organization"> | string | null
}

export type OrganizationCreateInput = {
  id?: string
  name: string
  slug?: string | null
  logo?: string | null
  createdAt: Date | string
  metadata?: string | null
  members?: Prisma.MemberCreateNestedManyWithoutOrganizationInput
  invitations?: Prisma.InvitationCreateNestedManyWithoutOrganizationInput
  notifications?: Prisma.NotificationCreateNestedManyWithoutOrganizationInput
  alumniProfiles?: Prisma.AlumniProfileCreateNestedManyWithoutPrimaryOrganizationInput
  events?: Prisma.EventCreateNestedManyWithoutOrganizationInput
  newsArticles?: Prisma.NewsArticleCreateNestedManyWithoutOrganizationInput
  donations?: Prisma.DonationCreateNestedManyWithoutOrganizationInput
  posts?: Prisma.PostCreateNestedManyWithoutOrganizationInput
}

export type OrganizationUncheckedCreateInput = {
  id?: string
  name: string
  slug?: string | null
  logo?: string | null
  createdAt: Date | string
  metadata?: string | null
  members?: Prisma.MemberUncheckedCreateNestedManyWithoutOrganizationInput
  invitations?: Prisma.InvitationUncheckedCreateNestedManyWithoutOrganizationInput
  notifications?: Prisma.NotificationUncheckedCreateNestedManyWithoutOrganizationInput
  alumniProfiles?: Prisma.AlumniProfileUncheckedCreateNestedManyWithoutPrimaryOrganizationInput
  events?: Prisma.EventUncheckedCreateNestedManyWithoutOrganizationInput
  newsArticles?: Prisma.NewsArticleUncheckedCreateNestedManyWithoutOrganizationInput
  donations?: Prisma.DonationUncheckedCreateNestedManyWithoutOrganizationInput
  posts?: Prisma.PostUncheckedCreateNestedManyWithoutOrganizationInput
}

export type OrganizationUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  slug?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  metadata?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  members?: Prisma.MemberUpdateManyWithoutOrganizationNestedInput
  invitations?: Prisma.InvitationUpdateManyWithoutOrganizationNestedInput
  notifications?: Prisma.NotificationUpdateManyWithoutOrganizationNestedInput
  alumniProfiles?: Prisma.AlumniProfileUpdateManyWithoutPrimaryOrganizationNestedInput
  events?: Prisma.EventUpdateManyWithoutOrganizationNestedInput
  newsArticles?: Prisma.NewsArticleUpdateManyWithoutOrganizationNestedInput
  donations?: Prisma.DonationUpdateManyWithoutOrganizationNestedInput
  posts?: Prisma.PostUpdateManyWithoutOrganizationNestedInput
}

export type OrganizationUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  slug?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  metadata?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  members?: Prisma.MemberUncheckedUpdateManyWithoutOrganizationNestedInput
  invitations?: Prisma.InvitationUncheckedUpdateManyWithoutOrganizationNestedInput
  notifications?: Prisma.NotificationUncheckedUpdateManyWithoutOrganizationNestedInput
  alumniProfiles?: Prisma.AlumniProfileUncheckedUpdateManyWithoutPrimaryOrganizationNestedInput
  events?: Prisma.EventUncheckedUpdateManyWithoutOrganizationNestedInput
  newsArticles?: Prisma.NewsArticleUncheckedUpdateManyWithoutOrganizationNestedInput
  donations?: Prisma.DonationUncheckedUpdateManyWithoutOrganizationNestedInput
  posts?: Prisma.PostUncheckedUpdateManyWithoutOrganizationNestedInput
}

export type OrganizationCreateManyInput = {
  id?: string
  name: string
  slug?: string | null
  logo?: string | null
  createdAt: Date | string
  metadata?: string | null
}

export type OrganizationUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  slug?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  metadata?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type OrganizationUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  slug?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  metadata?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type OrganizationOrderByRelevanceInput = {
  fields: Prisma.OrganizationOrderByRelevanceFieldEnum | Prisma.OrganizationOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type OrganizationCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  slug?: Prisma.SortOrder
  logo?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  metadata?: Prisma.SortOrder
}

export type OrganizationMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  slug?: Prisma.SortOrder
  logo?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  metadata?: Prisma.SortOrder
}

export type OrganizationMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  slug?: Prisma.SortOrder
  logo?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  metadata?: Prisma.SortOrder
}

export type OrganizationScalarRelationFilter = {
  is?: Prisma.OrganizationWhereInput
  isNot?: Prisma.OrganizationWhereInput
}

export type OrganizationNullableScalarRelationFilter = {
  is?: Prisma.OrganizationWhereInput | null
  isNot?: Prisma.OrganizationWhereInput | null
}

export type OrganizationCreateNestedOneWithoutMembersInput = {
  create?: Prisma.XOR<Prisma.OrganizationCreateWithoutMembersInput, Prisma.OrganizationUncheckedCreateWithoutMembersInput>
  connectOrCreate?: Prisma.OrganizationCreateOrConnectWithoutMembersInput
  connect?: Prisma.OrganizationWhereUniqueInput
}

export type OrganizationUpdateOneRequiredWithoutMembersNestedInput = {
  create?: Prisma.XOR<Prisma.OrganizationCreateWithoutMembersInput, Prisma.OrganizationUncheckedCreateWithoutMembersInput>
  connectOrCreate?: Prisma.OrganizationCreateOrConnectWithoutMembersInput
  upsert?: Prisma.OrganizationUpsertWithoutMembersInput
  connect?: Prisma.OrganizationWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.OrganizationUpdateToOneWithWhereWithoutMembersInput, Prisma.OrganizationUpdateWithoutMembersInput>, Prisma.OrganizationUncheckedUpdateWithoutMembersInput>
}

export type OrganizationCreateNestedOneWithoutInvitationsInput = {
  create?: Prisma.XOR<Prisma.OrganizationCreateWithoutInvitationsInput, Prisma.OrganizationUncheckedCreateWithoutInvitationsInput>
  connectOrCreate?: Prisma.OrganizationCreateOrConnectWithoutInvitationsInput
  connect?: Prisma.OrganizationWhereUniqueInput
}

export type OrganizationUpdateOneRequiredWithoutInvitationsNestedInput = {
  create?: Prisma.XOR<Prisma.OrganizationCreateWithoutInvitationsInput, Prisma.OrganizationUncheckedCreateWithoutInvitationsInput>
  connectOrCreate?: Prisma.OrganizationCreateOrConnectWithoutInvitationsInput
  upsert?: Prisma.OrganizationUpsertWithoutInvitationsInput
  connect?: Prisma.OrganizationWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.OrganizationUpdateToOneWithWhereWithoutInvitationsInput, Prisma.OrganizationUpdateWithoutInvitationsInput>, Prisma.OrganizationUncheckedUpdateWithoutInvitationsInput>
}

export type OrganizationCreateNestedOneWithoutAlumniProfilesInput = {
  create?: Prisma.XOR<Prisma.OrganizationCreateWithoutAlumniProfilesInput, Prisma.OrganizationUncheckedCreateWithoutAlumniProfilesInput>
  connectOrCreate?: Prisma.OrganizationCreateOrConnectWithoutAlumniProfilesInput
  connect?: Prisma.OrganizationWhereUniqueInput
}

export type OrganizationUpdateOneWithoutAlumniProfilesNestedInput = {
  create?: Prisma.XOR<Prisma.OrganizationCreateWithoutAlumniProfilesInput, Prisma.OrganizationUncheckedCreateWithoutAlumniProfilesInput>
  connectOrCreate?: Prisma.OrganizationCreateOrConnectWithoutAlumniProfilesInput
  upsert?: Prisma.OrganizationUpsertWithoutAlumniProfilesInput
  disconnect?: Prisma.OrganizationWhereInput | boolean
  delete?: Prisma.OrganizationWhereInput | boolean
  connect?: Prisma.OrganizationWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.OrganizationUpdateToOneWithWhereWithoutAlumniProfilesInput, Prisma.OrganizationUpdateWithoutAlumniProfilesInput>, Prisma.OrganizationUncheckedUpdateWithoutAlumniProfilesInput>
}

export type OrganizationCreateNestedOneWithoutPostsInput = {
  create?: Prisma.XOR<Prisma.OrganizationCreateWithoutPostsInput, Prisma.OrganizationUncheckedCreateWithoutPostsInput>
  connectOrCreate?: Prisma.OrganizationCreateOrConnectWithoutPostsInput
  connect?: Prisma.OrganizationWhereUniqueInput
}

export type OrganizationUpdateOneWithoutPostsNestedInput = {
  create?: Prisma.XOR<Prisma.OrganizationCreateWithoutPostsInput, Prisma.OrganizationUncheckedCreateWithoutPostsInput>
  connectOrCreate?: Prisma.OrganizationCreateOrConnectWithoutPostsInput
  upsert?: Prisma.OrganizationUpsertWithoutPostsInput
  disconnect?: Prisma.OrganizationWhereInput | boolean
  delete?: Prisma.OrganizationWhereInput | boolean
  connect?: Prisma.OrganizationWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.OrganizationUpdateToOneWithWhereWithoutPostsInput, Prisma.OrganizationUpdateWithoutPostsInput>, Prisma.OrganizationUncheckedUpdateWithoutPostsInput>
}

export type OrganizationCreateNestedOneWithoutEventsInput = {
  create?: Prisma.XOR<Prisma.OrganizationCreateWithoutEventsInput, Prisma.OrganizationUncheckedCreateWithoutEventsInput>
  connectOrCreate?: Prisma.OrganizationCreateOrConnectWithoutEventsInput
  connect?: Prisma.OrganizationWhereUniqueInput
}

export type OrganizationUpdateOneWithoutEventsNestedInput = {
  create?: Prisma.XOR<Prisma.OrganizationCreateWithoutEventsInput, Prisma.OrganizationUncheckedCreateWithoutEventsInput>
  connectOrCreate?: Prisma.OrganizationCreateOrConnectWithoutEventsInput
  upsert?: Prisma.OrganizationUpsertWithoutEventsInput
  disconnect?: Prisma.OrganizationWhereInput | boolean
  delete?: Prisma.OrganizationWhereInput | boolean
  connect?: Prisma.OrganizationWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.OrganizationUpdateToOneWithWhereWithoutEventsInput, Prisma.OrganizationUpdateWithoutEventsInput>, Prisma.OrganizationUncheckedUpdateWithoutEventsInput>
}

export type OrganizationCreateNestedOneWithoutDonationsInput = {
  create?: Prisma.XOR<Prisma.OrganizationCreateWithoutDonationsInput, Prisma.OrganizationUncheckedCreateWithoutDonationsInput>
  connectOrCreate?: Prisma.OrganizationCreateOrConnectWithoutDonationsInput
  connect?: Prisma.OrganizationWhereUniqueInput
}

export type OrganizationUpdateOneRequiredWithoutDonationsNestedInput = {
  create?: Prisma.XOR<Prisma.OrganizationCreateWithoutDonationsInput, Prisma.OrganizationUncheckedCreateWithoutDonationsInput>
  connectOrCreate?: Prisma.OrganizationCreateOrConnectWithoutDonationsInput
  upsert?: Prisma.OrganizationUpsertWithoutDonationsInput
  connect?: Prisma.OrganizationWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.OrganizationUpdateToOneWithWhereWithoutDonationsInput, Prisma.OrganizationUpdateWithoutDonationsInput>, Prisma.OrganizationUncheckedUpdateWithoutDonationsInput>
}

export type OrganizationCreateNestedOneWithoutNewsArticlesInput = {
  create?: Prisma.XOR<Prisma.OrganizationCreateWithoutNewsArticlesInput, Prisma.OrganizationUncheckedCreateWithoutNewsArticlesInput>
  connectOrCreate?: Prisma.OrganizationCreateOrConnectWithoutNewsArticlesInput
  connect?: Prisma.OrganizationWhereUniqueInput
}

export type OrganizationUpdateOneWithoutNewsArticlesNestedInput = {
  create?: Prisma.XOR<Prisma.OrganizationCreateWithoutNewsArticlesInput, Prisma.OrganizationUncheckedCreateWithoutNewsArticlesInput>
  connectOrCreate?: Prisma.OrganizationCreateOrConnectWithoutNewsArticlesInput
  upsert?: Prisma.OrganizationUpsertWithoutNewsArticlesInput
  disconnect?: Prisma.OrganizationWhereInput | boolean
  delete?: Prisma.OrganizationWhereInput | boolean
  connect?: Prisma.OrganizationWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.OrganizationUpdateToOneWithWhereWithoutNewsArticlesInput, Prisma.OrganizationUpdateWithoutNewsArticlesInput>, Prisma.OrganizationUncheckedUpdateWithoutNewsArticlesInput>
}

export type OrganizationCreateNestedOneWithoutNotificationsInput = {
  create?: Prisma.XOR<Prisma.OrganizationCreateWithoutNotificationsInput, Prisma.OrganizationUncheckedCreateWithoutNotificationsInput>
  connectOrCreate?: Prisma.OrganizationCreateOrConnectWithoutNotificationsInput
  connect?: Prisma.OrganizationWhereUniqueInput
}

export type OrganizationUpdateOneWithoutNotificationsNestedInput = {
  create?: Prisma.XOR<Prisma.OrganizationCreateWithoutNotificationsInput, Prisma.OrganizationUncheckedCreateWithoutNotificationsInput>
  connectOrCreate?: Prisma.OrganizationCreateOrConnectWithoutNotificationsInput
  upsert?: Prisma.OrganizationUpsertWithoutNotificationsInput
  disconnect?: Prisma.OrganizationWhereInput | boolean
  delete?: Prisma.OrganizationWhereInput | boolean
  connect?: Prisma.OrganizationWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.OrganizationUpdateToOneWithWhereWithoutNotificationsInput, Prisma.OrganizationUpdateWithoutNotificationsInput>, Prisma.OrganizationUncheckedUpdateWithoutNotificationsInput>
}

export type OrganizationCreateWithoutMembersInput = {
  id?: string
  name: string
  slug?: string | null
  logo?: string | null
  createdAt: Date | string
  metadata?: string | null
  invitations?: Prisma.InvitationCreateNestedManyWithoutOrganizationInput
  notifications?: Prisma.NotificationCreateNestedManyWithoutOrganizationInput
  alumniProfiles?: Prisma.AlumniProfileCreateNestedManyWithoutPrimaryOrganizationInput
  events?: Prisma.EventCreateNestedManyWithoutOrganizationInput
  newsArticles?: Prisma.NewsArticleCreateNestedManyWithoutOrganizationInput
  donations?: Prisma.DonationCreateNestedManyWithoutOrganizationInput
  posts?: Prisma.PostCreateNestedManyWithoutOrganizationInput
}

export type OrganizationUncheckedCreateWithoutMembersInput = {
  id?: string
  name: string
  slug?: string | null
  logo?: string | null
  createdAt: Date | string
  metadata?: string | null
  invitations?: Prisma.InvitationUncheckedCreateNestedManyWithoutOrganizationInput
  notifications?: Prisma.NotificationUncheckedCreateNestedManyWithoutOrganizationInput
  alumniProfiles?: Prisma.AlumniProfileUncheckedCreateNestedManyWithoutPrimaryOrganizationInput
  events?: Prisma.EventUncheckedCreateNestedManyWithoutOrganizationInput
  newsArticles?: Prisma.NewsArticleUncheckedCreateNestedManyWithoutOrganizationInput
  donations?: Prisma.DonationUncheckedCreateNestedManyWithoutOrganizationInput
  posts?: Prisma.PostUncheckedCreateNestedManyWithoutOrganizationInput
}

export type OrganizationCreateOrConnectWithoutMembersInput = {
  where: Prisma.OrganizationWhereUniqueInput
  create: Prisma.XOR<Prisma.OrganizationCreateWithoutMembersInput, Prisma.OrganizationUncheckedCreateWithoutMembersInput>
}

export type OrganizationUpsertWithoutMembersInput = {
  update: Prisma.XOR<Prisma.OrganizationUpdateWithoutMembersInput, Prisma.OrganizationUncheckedUpdateWithoutMembersInput>
  create: Prisma.XOR<Prisma.OrganizationCreateWithoutMembersInput, Prisma.OrganizationUncheckedCreateWithoutMembersInput>
  where?: Prisma.OrganizationWhereInput
}

export type OrganizationUpdateToOneWithWhereWithoutMembersInput = {
  where?: Prisma.OrganizationWhereInput
  data: Prisma.XOR<Prisma.OrganizationUpdateWithoutMembersInput, Prisma.OrganizationUncheckedUpdateWithoutMembersInput>
}

export type OrganizationUpdateWithoutMembersInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  slug?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  metadata?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  invitations?: Prisma.InvitationUpdateManyWithoutOrganizationNestedInput
  notifications?: Prisma.NotificationUpdateManyWithoutOrganizationNestedInput
  alumniProfiles?: Prisma.AlumniProfileUpdateManyWithoutPrimaryOrganizationNestedInput
  events?: Prisma.EventUpdateManyWithoutOrganizationNestedInput
  newsArticles?: Prisma.NewsArticleUpdateManyWithoutOrganizationNestedInput
  donations?: Prisma.DonationUpdateManyWithoutOrganizationNestedInput
  posts?: Prisma.PostUpdateManyWithoutOrganizationNestedInput
}

export type OrganizationUncheckedUpdateWithoutMembersInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  slug?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  metadata?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  invitations?: Prisma.InvitationUncheckedUpdateManyWithoutOrganizationNestedInput
  notifications?: Prisma.NotificationUncheckedUpdateManyWithoutOrganizationNestedInput
  alumniProfiles?: Prisma.AlumniProfileUncheckedUpdateManyWithoutPrimaryOrganizationNestedInput
  events?: Prisma.EventUncheckedUpdateManyWithoutOrganizationNestedInput
  newsArticles?: Prisma.NewsArticleUncheckedUpdateManyWithoutOrganizationNestedInput
  donations?: Prisma.DonationUncheckedUpdateManyWithoutOrganizationNestedInput
  posts?: Prisma.PostUncheckedUpdateManyWithoutOrganizationNestedInput
}

export type OrganizationCreateWithoutInvitationsInput = {
  id?: string
  name: string
  slug?: string | null
  logo?: string | null
  createdAt: Date | string
  metadata?: string | null
  members?: Prisma.MemberCreateNestedManyWithoutOrganizationInput
  notifications?: Prisma.NotificationCreateNestedManyWithoutOrganizationInput
  alumniProfiles?: Prisma.AlumniProfileCreateNestedManyWithoutPrimaryOrganizationInput
  events?: Prisma.EventCreateNestedManyWithoutOrganizationInput
  newsArticles?: Prisma.NewsArticleCreateNestedManyWithoutOrganizationInput
  donations?: Prisma.DonationCreateNestedManyWithoutOrganizationInput
  posts?: Prisma.PostCreateNestedManyWithoutOrganizationInput
}

export type OrganizationUncheckedCreateWithoutInvitationsInput = {
  id?: string
  name: string
  slug?: string | null
  logo?: string | null
  createdAt: Date | string
  metadata?: string | null
  members?: Prisma.MemberUncheckedCreateNestedManyWithoutOrganizationInput
  notifications?: Prisma.NotificationUncheckedCreateNestedManyWithoutOrganizationInput
  alumniProfiles?: Prisma.AlumniProfileUncheckedCreateNestedManyWithoutPrimaryOrganizationInput
  events?: Prisma.EventUncheckedCreateNestedManyWithoutOrganizationInput
  newsArticles?: Prisma.NewsArticleUncheckedCreateNestedManyWithoutOrganizationInput
  donations?: Prisma.DonationUncheckedCreateNestedManyWithoutOrganizationInput
  posts?: Prisma.PostUncheckedCreateNestedManyWithoutOrganizationInput
}

export type OrganizationCreateOrConnectWithoutInvitationsInput = {
  where: Prisma.OrganizationWhereUniqueInput
  create: Prisma.XOR<Prisma.OrganizationCreateWithoutInvitationsInput, Prisma.OrganizationUncheckedCreateWithoutInvitationsInput>
}

export type OrganizationUpsertWithoutInvitationsInput = {
  update: Prisma.XOR<Prisma.OrganizationUpdateWithoutInvitationsInput, Prisma.OrganizationUncheckedUpdateWithoutInvitationsInput>
  create: Prisma.XOR<Prisma.OrganizationCreateWithoutInvitationsInput, Prisma.OrganizationUncheckedCreateWithoutInvitationsInput>
  where?: Prisma.OrganizationWhereInput
}

export type OrganizationUpdateToOneWithWhereWithoutInvitationsInput = {
  where?: Prisma.OrganizationWhereInput
  data: Prisma.XOR<Prisma.OrganizationUpdateWithoutInvitationsInput, Prisma.OrganizationUncheckedUpdateWithoutInvitationsInput>
}

export type OrganizationUpdateWithoutInvitationsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  slug?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  metadata?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  members?: Prisma.MemberUpdateManyWithoutOrganizationNestedInput
  notifications?: Prisma.NotificationUpdateManyWithoutOrganizationNestedInput
  alumniProfiles?: Prisma.AlumniProfileUpdateManyWithoutPrimaryOrganizationNestedInput
  events?: Prisma.EventUpdateManyWithoutOrganizationNestedInput
  newsArticles?: Prisma.NewsArticleUpdateManyWithoutOrganizationNestedInput
  donations?: Prisma.DonationUpdateManyWithoutOrganizationNestedInput
  posts?: Prisma.PostUpdateManyWithoutOrganizationNestedInput
}

export type OrganizationUncheckedUpdateWithoutInvitationsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  slug?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  metadata?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  members?: Prisma.MemberUncheckedUpdateManyWithoutOrganizationNestedInput
  notifications?: Prisma.NotificationUncheckedUpdateManyWithoutOrganizationNestedInput
  alumniProfiles?: Prisma.AlumniProfileUncheckedUpdateManyWithoutPrimaryOrganizationNestedInput
  events?: Prisma.EventUncheckedUpdateManyWithoutOrganizationNestedInput
  newsArticles?: Prisma.NewsArticleUncheckedUpdateManyWithoutOrganizationNestedInput
  donations?: Prisma.DonationUncheckedUpdateManyWithoutOrganizationNestedInput
  posts?: Prisma.PostUncheckedUpdateManyWithoutOrganizationNestedInput
}

export type OrganizationCreateWithoutAlumniProfilesInput = {
  id?: string
  name: string
  slug?: string | null
  logo?: string | null
  createdAt: Date | string
  metadata?: string | null
  members?: Prisma.MemberCreateNestedManyWithoutOrganizationInput
  invitations?: Prisma.InvitationCreateNestedManyWithoutOrganizationInput
  notifications?: Prisma.NotificationCreateNestedManyWithoutOrganizationInput
  events?: Prisma.EventCreateNestedManyWithoutOrganizationInput
  newsArticles?: Prisma.NewsArticleCreateNestedManyWithoutOrganizationInput
  donations?: Prisma.DonationCreateNestedManyWithoutOrganizationInput
  posts?: Prisma.PostCreateNestedManyWithoutOrganizationInput
}

export type OrganizationUncheckedCreateWithoutAlumniProfilesInput = {
  id?: string
  name: string
  slug?: string | null
  logo?: string | null
  createdAt: Date | string
  metadata?: string | null
  members?: Prisma.MemberUncheckedCreateNestedManyWithoutOrganizationInput
  invitations?: Prisma.InvitationUncheckedCreateNestedManyWithoutOrganizationInput
  notifications?: Prisma.NotificationUncheckedCreateNestedManyWithoutOrganizationInput
  events?: Prisma.EventUncheckedCreateNestedManyWithoutOrganizationInput
  newsArticles?: Prisma.NewsArticleUncheckedCreateNestedManyWithoutOrganizationInput
  donations?: Prisma.DonationUncheckedCreateNestedManyWithoutOrganizationInput
  posts?: Prisma.PostUncheckedCreateNestedManyWithoutOrganizationInput
}

export type OrganizationCreateOrConnectWithoutAlumniProfilesInput = {
  where: Prisma.OrganizationWhereUniqueInput
  create: Prisma.XOR<Prisma.OrganizationCreateWithoutAlumniProfilesInput, Prisma.OrganizationUncheckedCreateWithoutAlumniProfilesInput>
}

export type OrganizationUpsertWithoutAlumniProfilesInput = {
  update: Prisma.XOR<Prisma.OrganizationUpdateWithoutAlumniProfilesInput, Prisma.OrganizationUncheckedUpdateWithoutAlumniProfilesInput>
  create: Prisma.XOR<Prisma.OrganizationCreateWithoutAlumniProfilesInput, Prisma.OrganizationUncheckedCreateWithoutAlumniProfilesInput>
  where?: Prisma.OrganizationWhereInput
}

export type OrganizationUpdateToOneWithWhereWithoutAlumniProfilesInput = {
  where?: Prisma.OrganizationWhereInput
  data: Prisma.XOR<Prisma.OrganizationUpdateWithoutAlumniProfilesInput, Prisma.OrganizationUncheckedUpdateWithoutAlumniProfilesInput>
}

export type OrganizationUpdateWithoutAlumniProfilesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  slug?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  metadata?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  members?: Prisma.MemberUpdateManyWithoutOrganizationNestedInput
  invitations?: Prisma.InvitationUpdateManyWithoutOrganizationNestedInput
  notifications?: Prisma.NotificationUpdateManyWithoutOrganizationNestedInput
  events?: Prisma.EventUpdateManyWithoutOrganizationNestedInput
  newsArticles?: Prisma.NewsArticleUpdateManyWithoutOrganizationNestedInput
  donations?: Prisma.DonationUpdateManyWithoutOrganizationNestedInput
  posts?: Prisma.PostUpdateManyWithoutOrganizationNestedInput
}

export type OrganizationUncheckedUpdateWithoutAlumniProfilesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  slug?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  metadata?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  members?: Prisma.MemberUncheckedUpdateManyWithoutOrganizationNestedInput
  invitations?: Prisma.InvitationUncheckedUpdateManyWithoutOrganizationNestedInput
  notifications?: Prisma.NotificationUncheckedUpdateManyWithoutOrganizationNestedInput
  events?: Prisma.EventUncheckedUpdateManyWithoutOrganizationNestedInput
  newsArticles?: Prisma.NewsArticleUncheckedUpdateManyWithoutOrganizationNestedInput
  donations?: Prisma.DonationUncheckedUpdateManyWithoutOrganizationNestedInput
  posts?: Prisma.PostUncheckedUpdateManyWithoutOrganizationNestedInput
}

export type OrganizationCreateWithoutPostsInput = {
  id?: string
  name: string
  slug?: string | null
  logo?: string | null
  createdAt: Date | string
  metadata?: string | null
  members?: Prisma.MemberCreateNestedManyWithoutOrganizationInput
  invitations?: Prisma.InvitationCreateNestedManyWithoutOrganizationInput
  notifications?: Prisma.NotificationCreateNestedManyWithoutOrganizationInput
  alumniProfiles?: Prisma.AlumniProfileCreateNestedManyWithoutPrimaryOrganizationInput
  events?: Prisma.EventCreateNestedManyWithoutOrganizationInput
  newsArticles?: Prisma.NewsArticleCreateNestedManyWithoutOrganizationInput
  donations?: Prisma.DonationCreateNestedManyWithoutOrganizationInput
}

export type OrganizationUncheckedCreateWithoutPostsInput = {
  id?: string
  name: string
  slug?: string | null
  logo?: string | null
  createdAt: Date | string
  metadata?: string | null
  members?: Prisma.MemberUncheckedCreateNestedManyWithoutOrganizationInput
  invitations?: Prisma.InvitationUncheckedCreateNestedManyWithoutOrganizationInput
  notifications?: Prisma.NotificationUncheckedCreateNestedManyWithoutOrganizationInput
  alumniProfiles?: Prisma.AlumniProfileUncheckedCreateNestedManyWithoutPrimaryOrganizationInput
  events?: Prisma.EventUncheckedCreateNestedManyWithoutOrganizationInput
  newsArticles?: Prisma.NewsArticleUncheckedCreateNestedManyWithoutOrganizationInput
  donations?: Prisma.DonationUncheckedCreateNestedManyWithoutOrganizationInput
}

export type OrganizationCreateOrConnectWithoutPostsInput = {
  where: Prisma.OrganizationWhereUniqueInput
  create: Prisma.XOR<Prisma.OrganizationCreateWithoutPostsInput, Prisma.OrganizationUncheckedCreateWithoutPostsInput>
}

export type OrganizationUpsertWithoutPostsInput = {
  update: Prisma.XOR<Prisma.OrganizationUpdateWithoutPostsInput, Prisma.OrganizationUncheckedUpdateWithoutPostsInput>
  create: Prisma.XOR<Prisma.OrganizationCreateWithoutPostsInput, Prisma.OrganizationUncheckedCreateWithoutPostsInput>
  where?: Prisma.OrganizationWhereInput
}

export type OrganizationUpdateToOneWithWhereWithoutPostsInput = {
  where?: Prisma.OrganizationWhereInput
  data: Prisma.XOR<Prisma.OrganizationUpdateWithoutPostsInput, Prisma.OrganizationUncheckedUpdateWithoutPostsInput>
}

export type OrganizationUpdateWithoutPostsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  slug?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  metadata?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  members?: Prisma.MemberUpdateManyWithoutOrganizationNestedInput
  invitations?: Prisma.InvitationUpdateManyWithoutOrganizationNestedInput
  notifications?: Prisma.NotificationUpdateManyWithoutOrganizationNestedInput
  alumniProfiles?: Prisma.AlumniProfileUpdateManyWithoutPrimaryOrganizationNestedInput
  events?: Prisma.EventUpdateManyWithoutOrganizationNestedInput
  newsArticles?: Prisma.NewsArticleUpdateManyWithoutOrganizationNestedInput
  donations?: Prisma.DonationUpdateManyWithoutOrganizationNestedInput
}

export type OrganizationUncheckedUpdateWithoutPostsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  slug?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  metadata?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  members?: Prisma.MemberUncheckedUpdateManyWithoutOrganizationNestedInput
  invitations?: Prisma.InvitationUncheckedUpdateManyWithoutOrganizationNestedInput
  notifications?: Prisma.NotificationUncheckedUpdateManyWithoutOrganizationNestedInput
  alumniProfiles?: Prisma.AlumniProfileUncheckedUpdateManyWithoutPrimaryOrganizationNestedInput
  events?: Prisma.EventUncheckedUpdateManyWithoutOrganizationNestedInput
  newsArticles?: Prisma.NewsArticleUncheckedUpdateManyWithoutOrganizationNestedInput
  donations?: Prisma.DonationUncheckedUpdateManyWithoutOrganizationNestedInput
}

export type OrganizationCreateWithoutEventsInput = {
  id?: string
  name: string
  slug?: string | null
  logo?: string | null
  createdAt: Date | string
  metadata?: string | null
  members?: Prisma.MemberCreateNestedManyWithoutOrganizationInput
  invitations?: Prisma.InvitationCreateNestedManyWithoutOrganizationInput
  notifications?: Prisma.NotificationCreateNestedManyWithoutOrganizationInput
  alumniProfiles?: Prisma.AlumniProfileCreateNestedManyWithoutPrimaryOrganizationInput
  newsArticles?: Prisma.NewsArticleCreateNestedManyWithoutOrganizationInput
  donations?: Prisma.DonationCreateNestedManyWithoutOrganizationInput
  posts?: Prisma.PostCreateNestedManyWithoutOrganizationInput
}

export type OrganizationUncheckedCreateWithoutEventsInput = {
  id?: string
  name: string
  slug?: string | null
  logo?: string | null
  createdAt: Date | string
  metadata?: string | null
  members?: Prisma.MemberUncheckedCreateNestedManyWithoutOrganizationInput
  invitations?: Prisma.InvitationUncheckedCreateNestedManyWithoutOrganizationInput
  notifications?: Prisma.NotificationUncheckedCreateNestedManyWithoutOrganizationInput
  alumniProfiles?: Prisma.AlumniProfileUncheckedCreateNestedManyWithoutPrimaryOrganizationInput
  newsArticles?: Prisma.NewsArticleUncheckedCreateNestedManyWithoutOrganizationInput
  donations?: Prisma.DonationUncheckedCreateNestedManyWithoutOrganizationInput
  posts?: Prisma.PostUncheckedCreateNestedManyWithoutOrganizationInput
}

export type OrganizationCreateOrConnectWithoutEventsInput = {
  where: Prisma.OrganizationWhereUniqueInput
  create: Prisma.XOR<Prisma.OrganizationCreateWithoutEventsInput, Prisma.OrganizationUncheckedCreateWithoutEventsInput>
}

export type OrganizationUpsertWithoutEventsInput = {
  update: Prisma.XOR<Prisma.OrganizationUpdateWithoutEventsInput, Prisma.OrganizationUncheckedUpdateWithoutEventsInput>
  create: Prisma.XOR<Prisma.OrganizationCreateWithoutEventsInput, Prisma.OrganizationUncheckedCreateWithoutEventsInput>
  where?: Prisma.OrganizationWhereInput
}

export type OrganizationUpdateToOneWithWhereWithoutEventsInput = {
  where?: Prisma.OrganizationWhereInput
  data: Prisma.XOR<Prisma.OrganizationUpdateWithoutEventsInput, Prisma.OrganizationUncheckedUpdateWithoutEventsInput>
}

export type OrganizationUpdateWithoutEventsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  slug?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  metadata?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  members?: Prisma.MemberUpdateManyWithoutOrganizationNestedInput
  invitations?: Prisma.InvitationUpdateManyWithoutOrganizationNestedInput
  notifications?: Prisma.NotificationUpdateManyWithoutOrganizationNestedInput
  alumniProfiles?: Prisma.AlumniProfileUpdateManyWithoutPrimaryOrganizationNestedInput
  newsArticles?: Prisma.NewsArticleUpdateManyWithoutOrganizationNestedInput
  donations?: Prisma.DonationUpdateManyWithoutOrganizationNestedInput
  posts?: Prisma.PostUpdateManyWithoutOrganizationNestedInput
}

export type OrganizationUncheckedUpdateWithoutEventsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  slug?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  metadata?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  members?: Prisma.MemberUncheckedUpdateManyWithoutOrganizationNestedInput
  invitations?: Prisma.InvitationUncheckedUpdateManyWithoutOrganizationNestedInput
  notifications?: Prisma.NotificationUncheckedUpdateManyWithoutOrganizationNestedInput
  alumniProfiles?: Prisma.AlumniProfileUncheckedUpdateManyWithoutPrimaryOrganizationNestedInput
  newsArticles?: Prisma.NewsArticleUncheckedUpdateManyWithoutOrganizationNestedInput
  donations?: Prisma.DonationUncheckedUpdateManyWithoutOrganizationNestedInput
  posts?: Prisma.PostUncheckedUpdateManyWithoutOrganizationNestedInput
}

export type OrganizationCreateWithoutDonationsInput = {
  id?: string
  name: string
  slug?: string | null
  logo?: string | null
  createdAt: Date | string
  metadata?: string | null
  members?: Prisma.MemberCreateNestedManyWithoutOrganizationInput
  invitations?: Prisma.InvitationCreateNestedManyWithoutOrganizationInput
  notifications?: Prisma.NotificationCreateNestedManyWithoutOrganizationInput
  alumniProfiles?: Prisma.AlumniProfileCreateNestedManyWithoutPrimaryOrganizationInput
  events?: Prisma.EventCreateNestedManyWithoutOrganizationInput
  newsArticles?: Prisma.NewsArticleCreateNestedManyWithoutOrganizationInput
  posts?: Prisma.PostCreateNestedManyWithoutOrganizationInput
}

export type OrganizationUncheckedCreateWithoutDonationsInput = {
  id?: string
  name: string
  slug?: string | null
  logo?: string | null
  createdAt: Date | string
  metadata?: string | null
  members?: Prisma.MemberUncheckedCreateNestedManyWithoutOrganizationInput
  invitations?: Prisma.InvitationUncheckedCreateNestedManyWithoutOrganizationInput
  notifications?: Prisma.NotificationUncheckedCreateNestedManyWithoutOrganizationInput
  alumniProfiles?: Prisma.AlumniProfileUncheckedCreateNestedManyWithoutPrimaryOrganizationInput
  events?: Prisma.EventUncheckedCreateNestedManyWithoutOrganizationInput
  newsArticles?: Prisma.NewsArticleUncheckedCreateNestedManyWithoutOrganizationInput
  posts?: Prisma.PostUncheckedCreateNestedManyWithoutOrganizationInput
}

export type OrganizationCreateOrConnectWithoutDonationsInput = {
  where: Prisma.OrganizationWhereUniqueInput
  create: Prisma.XOR<Prisma.OrganizationCreateWithoutDonationsInput, Prisma.OrganizationUncheckedCreateWithoutDonationsInput>
}

export type OrganizationUpsertWithoutDonationsInput = {
  update: Prisma.XOR<Prisma.OrganizationUpdateWithoutDonationsInput, Prisma.OrganizationUncheckedUpdateWithoutDonationsInput>
  create: Prisma.XOR<Prisma.OrganizationCreateWithoutDonationsInput, Prisma.OrganizationUncheckedCreateWithoutDonationsInput>
  where?: Prisma.OrganizationWhereInput
}

export type OrganizationUpdateToOneWithWhereWithoutDonationsInput = {
  where?: Prisma.OrganizationWhereInput
  data: Prisma.XOR<Prisma.OrganizationUpdateWithoutDonationsInput, Prisma.OrganizationUncheckedUpdateWithoutDonationsInput>
}

export type OrganizationUpdateWithoutDonationsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  slug?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  metadata?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  members?: Prisma.MemberUpdateManyWithoutOrganizationNestedInput
  invitations?: Prisma.InvitationUpdateManyWithoutOrganizationNestedInput
  notifications?: Prisma.NotificationUpdateManyWithoutOrganizationNestedInput
  alumniProfiles?: Prisma.AlumniProfileUpdateManyWithoutPrimaryOrganizationNestedInput
  events?: Prisma.EventUpdateManyWithoutOrganizationNestedInput
  newsArticles?: Prisma.NewsArticleUpdateManyWithoutOrganizationNestedInput
  posts?: Prisma.PostUpdateManyWithoutOrganizationNestedInput
}

export type OrganizationUncheckedUpdateWithoutDonationsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  slug?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  metadata?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  members?: Prisma.MemberUncheckedUpdateManyWithoutOrganizationNestedInput
  invitations?: Prisma.InvitationUncheckedUpdateManyWithoutOrganizationNestedInput
  notifications?: Prisma.NotificationUncheckedUpdateManyWithoutOrganizationNestedInput
  alumniProfiles?: Prisma.AlumniProfileUncheckedUpdateManyWithoutPrimaryOrganizationNestedInput
  events?: Prisma.EventUncheckedUpdateManyWithoutOrganizationNestedInput
  newsArticles?: Prisma.NewsArticleUncheckedUpdateManyWithoutOrganizationNestedInput
  posts?: Prisma.PostUncheckedUpdateManyWithoutOrganizationNestedInput
}

export type OrganizationCreateWithoutNewsArticlesInput = {
  id?: string
  name: string
  slug?: string | null
  logo?: string | null
  createdAt: Date | string
  metadata?: string | null
  members?: Prisma.MemberCreateNestedManyWithoutOrganizationInput
  invitations?: Prisma.InvitationCreateNestedManyWithoutOrganizationInput
  notifications?: Prisma.NotificationCreateNestedManyWithoutOrganizationInput
  alumniProfiles?: Prisma.AlumniProfileCreateNestedManyWithoutPrimaryOrganizationInput
  events?: Prisma.EventCreateNestedManyWithoutOrganizationInput
  donations?: Prisma.DonationCreateNestedManyWithoutOrganizationInput
  posts?: Prisma.PostCreateNestedManyWithoutOrganizationInput
}

export type OrganizationUncheckedCreateWithoutNewsArticlesInput = {
  id?: string
  name: string
  slug?: string | null
  logo?: string | null
  createdAt: Date | string
  metadata?: string | null
  members?: Prisma.MemberUncheckedCreateNestedManyWithoutOrganizationInput
  invitations?: Prisma.InvitationUncheckedCreateNestedManyWithoutOrganizationInput
  notifications?: Prisma.NotificationUncheckedCreateNestedManyWithoutOrganizationInput
  alumniProfiles?: Prisma.AlumniProfileUncheckedCreateNestedManyWithoutPrimaryOrganizationInput
  events?: Prisma.EventUncheckedCreateNestedManyWithoutOrganizationInput
  donations?: Prisma.DonationUncheckedCreateNestedManyWithoutOrganizationInput
  posts?: Prisma.PostUncheckedCreateNestedManyWithoutOrganizationInput
}

export type OrganizationCreateOrConnectWithoutNewsArticlesInput = {
  where: Prisma.OrganizationWhereUniqueInput
  create: Prisma.XOR<Prisma.OrganizationCreateWithoutNewsArticlesInput, Prisma.OrganizationUncheckedCreateWithoutNewsArticlesInput>
}

export type OrganizationUpsertWithoutNewsArticlesInput = {
  update: Prisma.XOR<Prisma.OrganizationUpdateWithoutNewsArticlesInput, Prisma.OrganizationUncheckedUpdateWithoutNewsArticlesInput>
  create: Prisma.XOR<Prisma.OrganizationCreateWithoutNewsArticlesInput, Prisma.OrganizationUncheckedCreateWithoutNewsArticlesInput>
  where?: Prisma.OrganizationWhereInput
}

export type OrganizationUpdateToOneWithWhereWithoutNewsArticlesInput = {
  where?: Prisma.OrganizationWhereInput
  data: Prisma.XOR<Prisma.OrganizationUpdateWithoutNewsArticlesInput, Prisma.OrganizationUncheckedUpdateWithoutNewsArticlesInput>
}

export type OrganizationUpdateWithoutNewsArticlesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  slug?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  metadata?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  members?: Prisma.MemberUpdateManyWithoutOrganizationNestedInput
  invitations?: Prisma.InvitationUpdateManyWithoutOrganizationNestedInput
  notifications?: Prisma.NotificationUpdateManyWithoutOrganizationNestedInput
  alumniProfiles?: Prisma.AlumniProfileUpdateManyWithoutPrimaryOrganizationNestedInput
  events?: Prisma.EventUpdateManyWithoutOrganizationNestedInput
  donations?: Prisma.DonationUpdateManyWithoutOrganizationNestedInput
  posts?: Prisma.PostUpdateManyWithoutOrganizationNestedInput
}

export type OrganizationUncheckedUpdateWithoutNewsArticlesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  slug?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  metadata?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  members?: Prisma.MemberUncheckedUpdateManyWithoutOrganizationNestedInput
  invitations?: Prisma.InvitationUncheckedUpdateManyWithoutOrganizationNestedInput
  notifications?: Prisma.NotificationUncheckedUpdateManyWithoutOrganizationNestedInput
  alumniProfiles?: Prisma.AlumniProfileUncheckedUpdateManyWithoutPrimaryOrganizationNestedInput
  events?: Prisma.EventUncheckedUpdateManyWithoutOrganizationNestedInput
  donations?: Prisma.DonationUncheckedUpdateManyWithoutOrganizationNestedInput
  posts?: Prisma.PostUncheckedUpdateManyWithoutOrganizationNestedInput
}

export type OrganizationCreateWithoutNotificationsInput = {
  id?: string
  name: string
  slug?: string | null
  logo?: string | null
  createdAt: Date | string
  metadata?: string | null
  members?: Prisma.MemberCreateNestedManyWithoutOrganizationInput
  invitations?: Prisma.InvitationCreateNestedManyWithoutOrganizationInput
  alumniProfiles?: Prisma.AlumniProfileCreateNestedManyWithoutPrimaryOrganizationInput
  events?: Prisma.EventCreateNestedManyWithoutOrganizationInput
  newsArticles?: Prisma.NewsArticleCreateNestedManyWithoutOrganizationInput
  donations?: Prisma.DonationCreateNestedManyWithoutOrganizationInput
  posts?: Prisma.PostCreateNestedManyWithoutOrganizationInput
}

export type OrganizationUncheckedCreateWithoutNotificationsInput = {
  id?: string
  name: string
  slug?: string | null
  logo?: string | null
  createdAt: Date | string
  metadata?: string | null
  members?: Prisma.MemberUncheckedCreateNestedManyWithoutOrganizationInput
  invitations?: Prisma.InvitationUncheckedCreateNestedManyWithoutOrganizationInput
  alumniProfiles?: Prisma.AlumniProfileUncheckedCreateNestedManyWithoutPrimaryOrganizationInput
  events?: Prisma.EventUncheckedCreateNestedManyWithoutOrganizationInput
  newsArticles?: Prisma.NewsArticleUncheckedCreateNestedManyWithoutOrganizationInput
  donations?: Prisma.DonationUncheckedCreateNestedManyWithoutOrganizationInput
  posts?: Prisma.PostUncheckedCreateNestedManyWithoutOrganizationInput
}

export type OrganizationCreateOrConnectWithoutNotificationsInput = {
  where: Prisma.OrganizationWhereUniqueInput
  create: Prisma.XOR<Prisma.OrganizationCreateWithoutNotificationsInput, Prisma.OrganizationUncheckedCreateWithoutNotificationsInput>
}

export type OrganizationUpsertWithoutNotificationsInput = {
  update: Prisma.XOR<Prisma.OrganizationUpdateWithoutNotificationsInput, Prisma.OrganizationUncheckedUpdateWithoutNotificationsInput>
  create: Prisma.XOR<Prisma.OrganizationCreateWithoutNotificationsInput, Prisma.OrganizationUncheckedCreateWithoutNotificationsInput>
  where?: Prisma.OrganizationWhereInput
}

export type OrganizationUpdateToOneWithWhereWithoutNotificationsInput = {
  where?: Prisma.OrganizationWhereInput
  data: Prisma.XOR<Prisma.OrganizationUpdateWithoutNotificationsInput, Prisma.OrganizationUncheckedUpdateWithoutNotificationsInput>
}

export type OrganizationUpdateWithoutNotificationsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  slug?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  metadata?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  members?: Prisma.MemberUpdateManyWithoutOrganizationNestedInput
  invitations?: Prisma.InvitationUpdateManyWithoutOrganizationNestedInput
  alumniProfiles?: Prisma.AlumniProfileUpdateManyWithoutPrimaryOrganizationNestedInput
  events?: Prisma.EventUpdateManyWithoutOrganizationNestedInput
  newsArticles?: Prisma.NewsArticleUpdateManyWithoutOrganizationNestedInput
  donations?: Prisma.DonationUpdateManyWithoutOrganizationNestedInput
  posts?: Prisma.PostUpdateManyWithoutOrganizationNestedInput
}

export type OrganizationUncheckedUpdateWithoutNotificationsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  slug?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  metadata?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  members?: Prisma.MemberUncheckedUpdateManyWithoutOrganizationNestedInput
  invitations?: Prisma.InvitationUncheckedUpdateManyWithoutOrganizationNestedInput
  alumniProfiles?: Prisma.AlumniProfileUncheckedUpdateManyWithoutPrimaryOrganizationNestedInput
  events?: Prisma.EventUncheckedUpdateManyWithoutOrganizationNestedInput
  newsArticles?: Prisma.NewsArticleUncheckedUpdateManyWithoutOrganizationNestedInput
  donations?: Prisma.DonationUncheckedUpdateManyWithoutOrganizationNestedInput
  posts?: Prisma.PostUncheckedUpdateManyWithoutOrganizationNestedInput
}


/**
 * Count Type OrganizationCountOutputType
 */

export type OrganizationCountOutputType = {
  members: number
  invitations: number
  notifications: number
  alumniProfiles: number
  events: number
  newsArticles: number
  donations: number
  posts: number
}

export type OrganizationCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  members?: boolean | OrganizationCountOutputTypeCountMembersArgs
  invitations?: boolean | OrganizationCountOutputTypeCountInvitationsArgs
  notifications?: boolean | OrganizationCountOutputTypeCountNotificationsArgs
  alumniProfiles?: boolean | OrganizationCountOutputTypeCountAlumniProfilesArgs
  events?: boolean | OrganizationCountOutputTypeCountEventsArgs
  newsArticles?: boolean | OrganizationCountOutputTypeCountNewsArticlesArgs
  donations?: boolean | OrganizationCountOutputTypeCountDonationsArgs
  posts?: boolean | OrganizationCountOutputTypeCountPostsArgs
}

/**
 * OrganizationCountOutputType without action
 */
export type OrganizationCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the OrganizationCountOutputType
   */
  select?: Prisma.OrganizationCountOutputTypeSelect<ExtArgs> | null
}

/**
 * OrganizationCountOutputType without action
 */
export type OrganizationCountOutputTypeCountMembersArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.MemberWhereInput
}

/**
 * OrganizationCountOutputType without action
 */
export type OrganizationCountOutputTypeCountInvitationsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.InvitationWhereInput
}

/**
 * OrganizationCountOutputType without action
 */
export type OrganizationCountOutputTypeCountNotificationsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.NotificationWhereInput
}

/**
 * OrganizationCountOutputType without action
 */
export type OrganizationCountOutputTypeCountAlumniProfilesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.AlumniProfileWhereInput
}

/**
 * OrganizationCountOutputType without action
 */
export type OrganizationCountOutputTypeCountEventsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.EventWhereInput
}

/**
 * OrganizationCountOutputType without action
 */
export type OrganizationCountOutputTypeCountNewsArticlesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.NewsArticleWhereInput
}

/**
 * OrganizationCountOutputType without action
 */
export type OrganizationCountOutputTypeCountDonationsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.DonationWhereInput
}

/**
 * OrganizationCountOutputType without action
 */
export type OrganizationCountOutputTypeCountPostsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.PostWhereInput
}


export type OrganizationSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  slug?: boolean
  logo?: boolean
  createdAt?: boolean
  metadata?: boolean
  members?: boolean | Prisma.Organization$membersArgs<ExtArgs>
  invitations?: boolean | Prisma.Organization$invitationsArgs<ExtArgs>
  notifications?: boolean | Prisma.Organization$notificationsArgs<ExtArgs>
  alumniProfiles?: boolean | Prisma.Organization$alumniProfilesArgs<ExtArgs>
  events?: boolean | Prisma.Organization$eventsArgs<ExtArgs>
  newsArticles?: boolean | Prisma.Organization$newsArticlesArgs<ExtArgs>
  donations?: boolean | Prisma.Organization$donationsArgs<ExtArgs>
  posts?: boolean | Prisma.Organization$postsArgs<ExtArgs>
  _count?: boolean | Prisma.OrganizationCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["organization"]>



export type OrganizationSelectScalar = {
  id?: boolean
  name?: boolean
  slug?: boolean
  logo?: boolean
  createdAt?: boolean
  metadata?: boolean
}

export type OrganizationOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "name" | "slug" | "logo" | "createdAt" | "metadata", ExtArgs["result"]["organization"]>
export type OrganizationInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  members?: boolean | Prisma.Organization$membersArgs<ExtArgs>
  invitations?: boolean | Prisma.Organization$invitationsArgs<ExtArgs>
  notifications?: boolean | Prisma.Organization$notificationsArgs<ExtArgs>
  alumniProfiles?: boolean | Prisma.Organization$alumniProfilesArgs<ExtArgs>
  events?: boolean | Prisma.Organization$eventsArgs<ExtArgs>
  newsArticles?: boolean | Prisma.Organization$newsArticlesArgs<ExtArgs>
  donations?: boolean | Prisma.Organization$donationsArgs<ExtArgs>
  posts?: boolean | Prisma.Organization$postsArgs<ExtArgs>
  _count?: boolean | Prisma.OrganizationCountOutputTypeDefaultArgs<ExtArgs>
}

export type $OrganizationPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Organization"
  objects: {
    members: Prisma.$MemberPayload<ExtArgs>[]
    invitations: Prisma.$InvitationPayload<ExtArgs>[]
    notifications: Prisma.$NotificationPayload<ExtArgs>[]
    alumniProfiles: Prisma.$AlumniProfilePayload<ExtArgs>[]
    events: Prisma.$EventPayload<ExtArgs>[]
    newsArticles: Prisma.$NewsArticlePayload<ExtArgs>[]
    donations: Prisma.$DonationPayload<ExtArgs>[]
    posts: Prisma.$PostPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    name: string
    slug: string | null
    logo: string | null
    createdAt: Date
    metadata: string | null
  }, ExtArgs["result"]["organization"]>
  composites: {}
}

export type OrganizationGetPayload<S extends boolean | null | undefined | OrganizationDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$OrganizationPayload, S>

export type OrganizationCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<OrganizationFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: OrganizationCountAggregateInputType | true
  }

export interface OrganizationDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Organization'], meta: { name: 'Organization' } }
  /**
   * Find zero or one Organization that matches the filter.
   * @param {OrganizationFindUniqueArgs} args - Arguments to find a Organization
   * @example
   * // Get one Organization
   * const organization = await prisma.organization.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends OrganizationFindUniqueArgs>(args: Prisma.SelectSubset<T, OrganizationFindUniqueArgs<ExtArgs>>): Prisma.Prisma__OrganizationClient<runtime.Types.Result.GetResult<Prisma.$OrganizationPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Organization that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {OrganizationFindUniqueOrThrowArgs} args - Arguments to find a Organization
   * @example
   * // Get one Organization
   * const organization = await prisma.organization.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends OrganizationFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, OrganizationFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__OrganizationClient<runtime.Types.Result.GetResult<Prisma.$OrganizationPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Organization that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {OrganizationFindFirstArgs} args - Arguments to find a Organization
   * @example
   * // Get one Organization
   * const organization = await prisma.organization.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends OrganizationFindFirstArgs>(args?: Prisma.SelectSubset<T, OrganizationFindFirstArgs<ExtArgs>>): Prisma.Prisma__OrganizationClient<runtime.Types.Result.GetResult<Prisma.$OrganizationPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Organization that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {OrganizationFindFirstOrThrowArgs} args - Arguments to find a Organization
   * @example
   * // Get one Organization
   * const organization = await prisma.organization.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends OrganizationFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, OrganizationFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__OrganizationClient<runtime.Types.Result.GetResult<Prisma.$OrganizationPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Organizations that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {OrganizationFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Organizations
   * const organizations = await prisma.organization.findMany()
   * 
   * // Get first 10 Organizations
   * const organizations = await prisma.organization.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const organizationWithIdOnly = await prisma.organization.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends OrganizationFindManyArgs>(args?: Prisma.SelectSubset<T, OrganizationFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$OrganizationPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Organization.
   * @param {OrganizationCreateArgs} args - Arguments to create a Organization.
   * @example
   * // Create one Organization
   * const Organization = await prisma.organization.create({
   *   data: {
   *     // ... data to create a Organization
   *   }
   * })
   * 
   */
  create<T extends OrganizationCreateArgs>(args: Prisma.SelectSubset<T, OrganizationCreateArgs<ExtArgs>>): Prisma.Prisma__OrganizationClient<runtime.Types.Result.GetResult<Prisma.$OrganizationPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Organizations.
   * @param {OrganizationCreateManyArgs} args - Arguments to create many Organizations.
   * @example
   * // Create many Organizations
   * const organization = await prisma.organization.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends OrganizationCreateManyArgs>(args?: Prisma.SelectSubset<T, OrganizationCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Organization.
   * @param {OrganizationDeleteArgs} args - Arguments to delete one Organization.
   * @example
   * // Delete one Organization
   * const Organization = await prisma.organization.delete({
   *   where: {
   *     // ... filter to delete one Organization
   *   }
   * })
   * 
   */
  delete<T extends OrganizationDeleteArgs>(args: Prisma.SelectSubset<T, OrganizationDeleteArgs<ExtArgs>>): Prisma.Prisma__OrganizationClient<runtime.Types.Result.GetResult<Prisma.$OrganizationPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Organization.
   * @param {OrganizationUpdateArgs} args - Arguments to update one Organization.
   * @example
   * // Update one Organization
   * const organization = await prisma.organization.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends OrganizationUpdateArgs>(args: Prisma.SelectSubset<T, OrganizationUpdateArgs<ExtArgs>>): Prisma.Prisma__OrganizationClient<runtime.Types.Result.GetResult<Prisma.$OrganizationPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Organizations.
   * @param {OrganizationDeleteManyArgs} args - Arguments to filter Organizations to delete.
   * @example
   * // Delete a few Organizations
   * const { count } = await prisma.organization.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends OrganizationDeleteManyArgs>(args?: Prisma.SelectSubset<T, OrganizationDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Organizations.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {OrganizationUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Organizations
   * const organization = await prisma.organization.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends OrganizationUpdateManyArgs>(args: Prisma.SelectSubset<T, OrganizationUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Organization.
   * @param {OrganizationUpsertArgs} args - Arguments to update or create a Organization.
   * @example
   * // Update or create a Organization
   * const organization = await prisma.organization.upsert({
   *   create: {
   *     // ... data to create a Organization
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Organization we want to update
   *   }
   * })
   */
  upsert<T extends OrganizationUpsertArgs>(args: Prisma.SelectSubset<T, OrganizationUpsertArgs<ExtArgs>>): Prisma.Prisma__OrganizationClient<runtime.Types.Result.GetResult<Prisma.$OrganizationPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Organizations.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {OrganizationCountArgs} args - Arguments to filter Organizations to count.
   * @example
   * // Count the number of Organizations
   * const count = await prisma.organization.count({
   *   where: {
   *     // ... the filter for the Organizations we want to count
   *   }
   * })
  **/
  count<T extends OrganizationCountArgs>(
    args?: Prisma.Subset<T, OrganizationCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], OrganizationCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Organization.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {OrganizationAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends OrganizationAggregateArgs>(args: Prisma.Subset<T, OrganizationAggregateArgs>): Prisma.PrismaPromise<GetOrganizationAggregateType<T>>

  /**
   * Group by Organization.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {OrganizationGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends OrganizationGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: OrganizationGroupByArgs['orderBy'] }
      : { orderBy?: OrganizationGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, OrganizationGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetOrganizationGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Organization model
 */
readonly fields: OrganizationFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Organization.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__OrganizationClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  members<T extends Prisma.Organization$membersArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Organization$membersArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$MemberPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  invitations<T extends Prisma.Organization$invitationsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Organization$invitationsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$InvitationPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  notifications<T extends Prisma.Organization$notificationsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Organization$notificationsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$NotificationPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  alumniProfiles<T extends Prisma.Organization$alumniProfilesArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Organization$alumniProfilesArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$AlumniProfilePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  events<T extends Prisma.Organization$eventsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Organization$eventsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$EventPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  newsArticles<T extends Prisma.Organization$newsArticlesArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Organization$newsArticlesArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$NewsArticlePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  donations<T extends Prisma.Organization$donationsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Organization$donationsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$DonationPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  posts<T extends Prisma.Organization$postsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Organization$postsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$PostPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Organization model
 */
export interface OrganizationFieldRefs {
  readonly id: Prisma.FieldRef<"Organization", 'String'>
  readonly name: Prisma.FieldRef<"Organization", 'String'>
  readonly slug: Prisma.FieldRef<"Organization", 'String'>
  readonly logo: Prisma.FieldRef<"Organization", 'String'>
  readonly createdAt: Prisma.FieldRef<"Organization", 'DateTime'>
  readonly metadata: Prisma.FieldRef<"Organization", 'String'>
}
    

// Custom InputTypes
/**
 * Organization findUnique
 */
export type OrganizationFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Organization
   */
  select?: Prisma.OrganizationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Organization
   */
  omit?: Prisma.OrganizationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.OrganizationInclude<ExtArgs> | null
  /**
   * Filter, which Organization to fetch.
   */
  where: Prisma.OrganizationWhereUniqueInput
}

/**
 * Organization findUniqueOrThrow
 */
export type OrganizationFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Organization
   */
  select?: Prisma.OrganizationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Organization
   */
  omit?: Prisma.OrganizationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.OrganizationInclude<ExtArgs> | null
  /**
   * Filter, which Organization to fetch.
   */
  where: Prisma.OrganizationWhereUniqueInput
}

/**
 * Organization findFirst
 */
export type OrganizationFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Organization
   */
  select?: Prisma.OrganizationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Organization
   */
  omit?: Prisma.OrganizationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.OrganizationInclude<ExtArgs> | null
  /**
   * Filter, which Organization to fetch.
   */
  where?: Prisma.OrganizationWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Organizations to fetch.
   */
  orderBy?: Prisma.OrganizationOrderByWithRelationInput | Prisma.OrganizationOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Organizations.
   */
  cursor?: Prisma.OrganizationWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Organizations from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Organizations.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Organizations.
   */
  distinct?: Prisma.OrganizationScalarFieldEnum | Prisma.OrganizationScalarFieldEnum[]
}

/**
 * Organization findFirstOrThrow
 */
export type OrganizationFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Organization
   */
  select?: Prisma.OrganizationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Organization
   */
  omit?: Prisma.OrganizationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.OrganizationInclude<ExtArgs> | null
  /**
   * Filter, which Organization to fetch.
   */
  where?: Prisma.OrganizationWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Organizations to fetch.
   */
  orderBy?: Prisma.OrganizationOrderByWithRelationInput | Prisma.OrganizationOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Organizations.
   */
  cursor?: Prisma.OrganizationWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Organizations from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Organizations.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Organizations.
   */
  distinct?: Prisma.OrganizationScalarFieldEnum | Prisma.OrganizationScalarFieldEnum[]
}

/**
 * Organization findMany
 */
export type OrganizationFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Organization
   */
  select?: Prisma.OrganizationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Organization
   */
  omit?: Prisma.OrganizationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.OrganizationInclude<ExtArgs> | null
  /**
   * Filter, which Organizations to fetch.
   */
  where?: Prisma.OrganizationWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Organizations to fetch.
   */
  orderBy?: Prisma.OrganizationOrderByWithRelationInput | Prisma.OrganizationOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Organizations.
   */
  cursor?: Prisma.OrganizationWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Organizations from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Organizations.
   */
  skip?: number
  distinct?: Prisma.OrganizationScalarFieldEnum | Prisma.OrganizationScalarFieldEnum[]
}

/**
 * Organization create
 */
export type OrganizationCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Organization
   */
  select?: Prisma.OrganizationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Organization
   */
  omit?: Prisma.OrganizationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.OrganizationInclude<ExtArgs> | null
  /**
   * The data needed to create a Organization.
   */
  data: Prisma.XOR<Prisma.OrganizationCreateInput, Prisma.OrganizationUncheckedCreateInput>
}

/**
 * Organization createMany
 */
export type OrganizationCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Organizations.
   */
  data: Prisma.OrganizationCreateManyInput | Prisma.OrganizationCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Organization update
 */
export type OrganizationUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Organization
   */
  select?: Prisma.OrganizationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Organization
   */
  omit?: Prisma.OrganizationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.OrganizationInclude<ExtArgs> | null
  /**
   * The data needed to update a Organization.
   */
  data: Prisma.XOR<Prisma.OrganizationUpdateInput, Prisma.OrganizationUncheckedUpdateInput>
  /**
   * Choose, which Organization to update.
   */
  where: Prisma.OrganizationWhereUniqueInput
}

/**
 * Organization updateMany
 */
export type OrganizationUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Organizations.
   */
  data: Prisma.XOR<Prisma.OrganizationUpdateManyMutationInput, Prisma.OrganizationUncheckedUpdateManyInput>
  /**
   * Filter which Organizations to update
   */
  where?: Prisma.OrganizationWhereInput
  /**
   * Limit how many Organizations to update.
   */
  limit?: number
}

/**
 * Organization upsert
 */
export type OrganizationUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Organization
   */
  select?: Prisma.OrganizationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Organization
   */
  omit?: Prisma.OrganizationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.OrganizationInclude<ExtArgs> | null
  /**
   * The filter to search for the Organization to update in case it exists.
   */
  where: Prisma.OrganizationWhereUniqueInput
  /**
   * In case the Organization found by the `where` argument doesn't exist, create a new Organization with this data.
   */
  create: Prisma.XOR<Prisma.OrganizationCreateInput, Prisma.OrganizationUncheckedCreateInput>
  /**
   * In case the Organization was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.OrganizationUpdateInput, Prisma.OrganizationUncheckedUpdateInput>
}

/**
 * Organization delete
 */
export type OrganizationDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Organization
   */
  select?: Prisma.OrganizationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Organization
   */
  omit?: Prisma.OrganizationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.OrganizationInclude<ExtArgs> | null
  /**
   * Filter which Organization to delete.
   */
  where: Prisma.OrganizationWhereUniqueInput
}

/**
 * Organization deleteMany
 */
export type OrganizationDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Organizations to delete
   */
  where?: Prisma.OrganizationWhereInput
  /**
   * Limit how many Organizations to delete.
   */
  limit?: number
}

/**
 * Organization.members
 */
export type Organization$membersArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Member
   */
  select?: Prisma.MemberSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Member
   */
  omit?: Prisma.MemberOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MemberInclude<ExtArgs> | null
  where?: Prisma.MemberWhereInput
  orderBy?: Prisma.MemberOrderByWithRelationInput | Prisma.MemberOrderByWithRelationInput[]
  cursor?: Prisma.MemberWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.MemberScalarFieldEnum | Prisma.MemberScalarFieldEnum[]
}

/**
 * Organization.invitations
 */
export type Organization$invitationsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Invitation
   */
  select?: Prisma.InvitationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Invitation
   */
  omit?: Prisma.InvitationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.InvitationInclude<ExtArgs> | null
  where?: Prisma.InvitationWhereInput
  orderBy?: Prisma.InvitationOrderByWithRelationInput | Prisma.InvitationOrderByWithRelationInput[]
  cursor?: Prisma.InvitationWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.InvitationScalarFieldEnum | Prisma.InvitationScalarFieldEnum[]
}

/**
 * Organization.notifications
 */
export type Organization$notificationsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Notification
   */
  select?: Prisma.NotificationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Notification
   */
  omit?: Prisma.NotificationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.NotificationInclude<ExtArgs> | null
  where?: Prisma.NotificationWhereInput
  orderBy?: Prisma.NotificationOrderByWithRelationInput | Prisma.NotificationOrderByWithRelationInput[]
  cursor?: Prisma.NotificationWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.NotificationScalarFieldEnum | Prisma.NotificationScalarFieldEnum[]
}

/**
 * Organization.alumniProfiles
 */
export type Organization$alumniProfilesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AlumniProfile
   */
  select?: Prisma.AlumniProfileSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AlumniProfile
   */
  omit?: Prisma.AlumniProfileOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AlumniProfileInclude<ExtArgs> | null
  where?: Prisma.AlumniProfileWhereInput
  orderBy?: Prisma.AlumniProfileOrderByWithRelationInput | Prisma.AlumniProfileOrderByWithRelationInput[]
  cursor?: Prisma.AlumniProfileWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.AlumniProfileScalarFieldEnum | Prisma.AlumniProfileScalarFieldEnum[]
}

/**
 * Organization.events
 */
export type Organization$eventsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Event
   */
  select?: Prisma.EventSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Event
   */
  omit?: Prisma.EventOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.EventInclude<ExtArgs> | null
  where?: Prisma.EventWhereInput
  orderBy?: Prisma.EventOrderByWithRelationInput | Prisma.EventOrderByWithRelationInput[]
  cursor?: Prisma.EventWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.EventScalarFieldEnum | Prisma.EventScalarFieldEnum[]
}

/**
 * Organization.newsArticles
 */
export type Organization$newsArticlesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the NewsArticle
   */
  select?: Prisma.NewsArticleSelect<ExtArgs> | null
  /**
   * Omit specific fields from the NewsArticle
   */
  omit?: Prisma.NewsArticleOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.NewsArticleInclude<ExtArgs> | null
  where?: Prisma.NewsArticleWhereInput
  orderBy?: Prisma.NewsArticleOrderByWithRelationInput | Prisma.NewsArticleOrderByWithRelationInput[]
  cursor?: Prisma.NewsArticleWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.NewsArticleScalarFieldEnum | Prisma.NewsArticleScalarFieldEnum[]
}

/**
 * Organization.donations
 */
export type Organization$donationsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Donation
   */
  select?: Prisma.DonationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Donation
   */
  omit?: Prisma.DonationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DonationInclude<ExtArgs> | null
  where?: Prisma.DonationWhereInput
  orderBy?: Prisma.DonationOrderByWithRelationInput | Prisma.DonationOrderByWithRelationInput[]
  cursor?: Prisma.DonationWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.DonationScalarFieldEnum | Prisma.DonationScalarFieldEnum[]
}

/**
 * Organization.posts
 */
export type Organization$postsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Post
   */
  select?: Prisma.PostSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Post
   */
  omit?: Prisma.PostOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PostInclude<ExtArgs> | null
  where?: Prisma.PostWhereInput
  orderBy?: Prisma.PostOrderByWithRelationInput | Prisma.PostOrderByWithRelationInput[]
  cursor?: Prisma.PostWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.PostScalarFieldEnum | Prisma.PostScalarFieldEnum[]
}

/**
 * Organization without action
 */
export type OrganizationDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Organization
   */
  select?: Prisma.OrganizationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Organization
   */
  omit?: Prisma.OrganizationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.OrganizationInclude<ExtArgs> | null
}
