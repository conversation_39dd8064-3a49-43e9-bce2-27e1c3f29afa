"use client"

import { useMemo, useState } from "react"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { orpc, client } from "@/utils/orpc"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Pagin<PERSON>, PaginationContent, PaginationItem, PaginationNext, PaginationPrevious } from "@/components/ui/pagination"
import Link from "next/link"
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useForm } from "react-hook-form"

type FilterInput = {
  postType?: "GENERAL" | "SUCCESS_STORY" | "JOB_OPPORTUNITY" | "MENTORSHIP" | "ANNOUNCEMENT"
}

export default function PostList() {
  const [page, setPage] = useState(1)
  const [filters, setFilters] = useState<FilterInput>({})
  const queryClient = useQueryClient()

  const list = useQuery(orpc.posts.list.queryOptions({ input: { page, limit: 10, ...filters } }))

  const totalPages = useMemo(() => {
    const total = (list.data as any)?.total ?? 0
    const limit = (list.data as any)?.limit ?? 10
    return Math.max(1, Math.ceil(total / limit))
  }, [list.data])

  const toggleLike = useMutation({
    mutationFn: async (postId: string) => client.posts.toggleLike({ postId }),
    onSuccess: async () => queryClient.invalidateQueries(),
  })

  const form = useForm<FilterInput>({ defaultValues: filters })

  return (
    <div className="space-y-4">
      <div className="rounded-lg border bg-card p-4">
        <Form {...form}>
          <form className="grid gap-4 md:grid-cols-4" onSubmit={form.handleSubmit((v) => { setPage(1); setFilters(v) })}>
            <FormField name="postType" control={form.control} render={({ field }) => (
              <FormItem>
                <FormLabel>Type</FormLabel>
                <FormControl>
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger><SelectValue placeholder="All" /></SelectTrigger>
                    <SelectContent>
                      <SelectItem value="GENERAL">General</SelectItem>
                      <SelectItem value="SUCCESS_STORY">Success story</SelectItem>
                      <SelectItem value="JOB_OPPORTUNITY">Job opportunity</SelectItem>
                      <SelectItem value="MENTORSHIP">Mentorship</SelectItem>
                      <SelectItem value="ANNOUNCEMENT">Announcement</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
              </FormItem>
            )} />
            <div className="md:col-span-3 flex items-end justify-end">
              <Button type="submit">Apply</Button>
            </div>
          </form>
        </Form>
      </div>

      <div className="grid gap-3">
        {list.isLoading && (<div className="text-sm text-muted-foreground">Loading...</div>)}
        {(list.data as any)?.data?.map((post: any) => (
          <div key={post.id} className="rounded-lg border bg-card p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {post.postType && <Badge variant="secondary">{post.postType}</Badge>}
                {post.title && <div className="font-medium truncate">{post.title}</div>}
              </div>
              <div className="text-xs text-muted-foreground">{new Date(post.createdAt).toLocaleString()}</div>
            </div>
            <div className="mt-2 text-sm whitespace-pre-wrap">{post.content}</div>
            <div className="mt-3 flex items-center gap-2">
              <Button size="sm" variant="outline" onClick={() => toggleLike.mutate(post.id)} disabled={toggleLike.isPending}>Like ({post._count?.likes ?? 0})</Button>
              <Button size="sm" asChild>
                <Link href={`/feed/${post.id}`}>Open</Link>
              </Button>
            </div>
          </div>
        ))}
      </div>

      <div className="flex items-center justify-end">
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious href="#" onClick={(e) => { e.preventDefault(); setPage((p) => Math.max(1, p - 1)) }} />
            </PaginationItem>
            <PaginationItem>
              <span className="text-sm text-muted-foreground px-3 py-2">Page {page} of {totalPages}</span>
            </PaginationItem>
            <PaginationItem>
              <PaginationNext href="#" onClick={(e) => { e.preventDefault(); setPage((p) => Math.min(totalPages, p + 1)) }} />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </div>
    </div>
  )
}


