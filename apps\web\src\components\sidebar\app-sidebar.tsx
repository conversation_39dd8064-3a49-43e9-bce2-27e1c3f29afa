"use client";

import * as React from "react";
import {
  LayoutDashboard,
  LifeBuoy,
  MessageCircle,
  Users,
  Settings,
  BarChart2,
  Calendar,
  Bell,
  Newspaper,
  Building2,
  HandCoins,
  UserPlus,
} from "lucide-react";
import { NavUser } from "@/components/sidebar/nav-user";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import NavMenu from "./nav-menu";
import Link from "next/link";


import NavHeader from "./nav-header";

type NavGroup = {
  name: string;
  url: string;
  icon: React.ElementType;
  process?: boolean;
  new?: boolean;
};

type NavData = {
  navMain: NavGroup[];
  community: NavGroup[];
  organization: NavGroup[];
  navSecondary: NavGroup[];
};

const data: NavData = {
  navMain: [
    {
      name: "Dashboard",
      url: "/dashboard",
      icon: LayoutDashboard,
    },
    {
      name: "Feed",
      url: "/feed",
      icon: Newspaper,
    },
    {
      name: "Directory",
      url: "/directory",
      icon: Users,
    },
    {
      name: "Messages",
      url: "/messages",
      icon: MessageCircle,
    },
  ],
  community: [
    {
      name: "Connections",
      url: "/connections",
      icon: UserPlus,
    },
    {
      name: "Events",
      url: "/events",
      icon: Calendar,
    },
    {
      name: "News",
      url: "/news",
      icon: Newspaper,
    },
    {
      name: "Donations",
      url: "/donations",
      icon: HandCoins,
    },
    {
      name: "Notifications",
      url: "/notifications",
      icon: Bell,
    },
  ],
  organization: [
    {
      name: "Organizations",
      url: "/organizations",
      icon: Building2,
    },
    {
      name: "Analytics",
      url: "/analytics",
      icon: BarChart2,
    },
  ],
  navSecondary: [
    {
      name: "Settings",
      url: "/settings",
      icon: Settings,
    },
    {
      name: "Support",
      url: "/support",
      icon: LifeBuoy,
    },
  ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar variant="floating" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="sm" asChild>
              <NavHeader />
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <div className="grow">
          <NavMenu items={data.navMain} title="Overview" />
          <NavMenu items={data.community} title="Community" />
          <NavMenu items={data.organization} title="Organization" />
        </div>
        <SidebarGroup {...props}>
          <SidebarGroupContent>
            <SidebarMenu>
              {data.navSecondary.map((item) => (
                <SidebarMenuItem key={item.name}>
                  <SidebarMenuButton asChild size="sm">
                    <Link href={item.url}>
                      <item.icon />
                      <span>{item.name}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
    </Sidebar>
  );
}
