"use client"

import { useQuery } from "@tanstack/react-query"
import { orpc } from "@/utils/orpc"

export default function OrgMembers() {
  // Assuming activeOrganizationId is set in session; server resolves from context
  const profiles = useQuery(orpc.profiles.getByOrganization.queryOptions({ input: { organizationId: "active" as any, page: 1, limit: 20 } }))

  return (
    <div className="rounded-lg border bg-card p-4">
      <div className="text-sm font-medium text-muted-foreground mb-2">Members</div>
      <div className="grid gap-2">
        {profiles.isLoading && (<div className="text-sm text-muted-foreground">Loading...</div>)}
        {(profiles.data as any)?.data?.map((p: any) => (
          <div key={p.id} className="rounded-md border p-3">
            <div className="font-medium">{p.displayName || `${p.firstName} ${p.lastName}`}</div>
            <div className="text-sm text-muted-foreground">{p.currentPosition || ""} {p.currentCompany ? `• ${p.currentCompany}` : ""}</div>
          </div>
        ))}
      </div>
    </div>
  )
}


