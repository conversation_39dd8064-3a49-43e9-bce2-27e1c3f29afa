import { z } from "zod";

/**
 * Common validation schemas
 */
export const idSchema = z.string().min(1, "ID is required");
export const emailSchema = z.string().email("Invalid email address");
export const phoneSchema = z.string().regex(/^\+?[1-9]\d{1,14}$/, "Invalid phone number");
export const urlSchema = z.string().url("Invalid URL");

/**
 * Pagination schemas
 */
export const paginationSchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(20),
});

/**
 * Date range schema
 */
export const dateRangeSchema = z.object({
  from: z.date().optional(),
  to: z.date().optional(),
});

/**
 * Alumni Profile schemas
 */
export const createAlumniProfileSchema = z.object({
  firstName: z.string().min(1, "First name is required").max(100),
  lastName: z.string().min(1, "Last name is required").max(100),
  displayName: z.string().max(100).optional(),
  bio: z.string().max(1000).optional(),
  profilePicture: z.string().url().optional(),
  graduationYear: z.number().int().min(1980).max(new Date().getFullYear()),
  programType: z.string().min(1, "Program type is required").max(100),
  centerLocation: z.string().min(1, "Center location is required").max(100),
  achievements: z.string().max(2000).optional(),
  currentPosition: z.string().max(100).optional(),
  currentCompany: z.string().max(100).optional(),
  industry: z.string().max(100).optional(),
  location: z.string().max(100).optional(),
  linkedInUrl: urlSchema.optional(),
  phoneNumber: phoneSchema.optional(),
  mentorshipOffered: z.boolean().default(false),
  mentorshipSought: z.boolean().default(false),
  skillsOffered: z.string().max(1000).optional(),
  skillsWanted: z.string().max(1000).optional(),
  primaryOrganizationId: idSchema.optional(),
});

export const updateAlumniProfileSchema = createAlumniProfileSchema.partial();

export const profilePrivacySchema = z.object({
  profileVisibility: z.enum(["PUBLIC", "ALUMNI_ONLY", "PRIVATE"]),
  showEmail: z.boolean(),
  showPhone: z.boolean(),
  showLocation: z.boolean(),
});

export const profileSearchSchema = z.object({
  search: z.string().optional(),
  graduationYear: z.number().int().optional(),
  programType: z.string().optional(),
  industry: z.string().optional(),
  location: z.string().optional(),
  centerLocation: z.string().optional(),
  mentorshipOffered: z.boolean().optional(),
  mentorshipSought: z.boolean().optional(),
  organizationId: idSchema.optional(),
}).merge(paginationSchema);

/**
 * Connection schemas
 */
export const connectionRequestSchema = z.object({
  requestedId: idSchema,
  message: z.string().max(500).optional(),
});

export const connectionActionSchema = z.object({
  connectionId: idSchema,
});

/**
 * Message schemas
 */
export const sendMessageSchema = z.object({
  receiverId: idSchema,
  content: z.string().min(1, "Message content is required").max(2000),
});

export const markMessageReadSchema = z.object({
  messageId: idSchema,
});

/**
 * Post schemas
 */
export const createPostSchema = z.object({
  title: z.string().max(200).optional(),
  content: z.string().min(1, "Post content is required").max(5000),
  imageUrl: urlSchema.optional(),
  postType: z.enum(["GENERAL", "SUCCESS_STORY", "JOB_OPPORTUNITY", "MENTORSHIP", "ANNOUNCEMENT"]).default("GENERAL"),
  organizationId: idSchema.optional(),
});

export const updatePostSchema = createPostSchema.partial();

export const postFilterSchema = z.object({
  postType: z.enum(["GENERAL", "SUCCESS_STORY", "JOB_OPPORTUNITY", "MENTORSHIP", "ANNOUNCEMENT"]).optional(),
  organizationId: idSchema.optional(),
  authorId: idSchema.optional(),
}).merge(paginationSchema);

export const commentSchema = z.object({
  postId: idSchema,
  content: z.string().min(1, "Comment content is required").max(1000),
});

/**
 * Event schemas
 */
export const createEventSchema = z.object({
  title: z.string().min(1, "Event title is required").max(200),
  description: z.string().min(1, "Event description is required").max(5000),
  eventType: z.enum(["WORKSHOP", "NETWORKING", "CONFERENCE", "WEBINAR", "SOCIAL", "FUNDRAISING", "MENTORSHIP"]).default("WORKSHOP"),
  location: z.string().max(200).optional(),
  isVirtual: z.boolean().default(false),
  virtualLink: urlSchema.optional(),
  startDateTime: z.date(),
  endDateTime: z.date(),
  maxAttendees: z.number().int().min(1).optional(),
  imageUrl: urlSchema.optional(),
  organizationId: idSchema.optional(),
});

export const updateEventSchema = createEventSchema.partial();

export const eventFilterSchema = z.object({
  eventType: z.enum(["WORKSHOP", "NETWORKING", "CONFERENCE", "WEBINAR", "SOCIAL", "FUNDRAISING", "MENTORSHIP"]).optional(),
  organizationId: idSchema.optional(),
  isVirtual: z.boolean().optional(),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
}).merge(paginationSchema);

export const eventRegistrationSchema = z.object({
  eventId: idSchema,
});

/**
 * Donation schemas
 */
export const createDonationSchema = z.object({
  amount: z.number().positive("Amount must be positive"),
  currency: z.string().default("ZAR"),
  donationType: z.enum(["ONE_TIME", "MONTHLY", "QUARTERLY", "ANNUAL"]).default("ONE_TIME"),
  organizationId: idSchema,
  purpose: z.string().max(200).optional(),
  isAnonymous: z.boolean().default(false),
  paymentMethod: z.string().optional(),
});

export const recurringDonationSchema = z.object({
  donationId: idSchema,
  isActive: z.boolean(),
});

/**
 * News Article schemas
 */
export const createNewsArticleSchema = z.object({
  title: z.string().min(1, "Title is required").max(200),
  content: z.string().min(1, "Content is required").max(10000),
  excerpt: z.string().max(500).optional(),
  imageUrl: urlSchema.optional(),
  authorName: z.string().min(1, "Author name is required").max(100),
  category: z.enum(["GENERAL", "EVENTS", "SUCCESS_STORIES", "OPPORTUNITIES", "ANNOUNCEMENTS"]).default("GENERAL"),
  organizationId: idSchema.optional(),
});

export const updateNewsArticleSchema = createNewsArticleSchema.partial();

export const newsFilterSchema = z.object({
  category: z.enum(["GENERAL", "EVENTS", "SUCCESS_STORIES", "OPPORTUNITIES", "ANNOUNCEMENTS"]).optional(),
  organizationId: idSchema.optional(),
  authorName: z.string().optional(),
  isPublished: z.boolean().optional(),
}).merge(paginationSchema);

/**
 * Notification schemas
 */
export const markNotificationReadSchema = z.object({
  notificationId: idSchema,
});

export const notificationFilterSchema = z.object({
  type: z.enum([
    "CONNECTION_REQUEST",
    "CONNECTION_ACCEPTED", 
    "EVENT_REMINDER",
    "EVENT_REGISTRATION",
    "NEW_MESSAGE",
    "POST_LIKE",
    "POST_COMMENT",
    "DONATION_CONFIRMATION",
    "NEWS_ARTICLE",
    "SYSTEM_ANNOUNCEMENT"
  ]).optional(),
  isRead: z.boolean().optional(),
}).merge(paginationSchema);

/**
 * Organization schemas
 */
export const createOrganizationSchema = z.object({
  name: z.string().min(1, "Organization name is required").max(200),
  slug: z.string().min(1, "Slug is required").max(100).regex(/^[a-z0-9-]+$/, "Slug must contain only lowercase letters, numbers, and hyphens"),
  logo: urlSchema.optional(),
  metadata: z.string().optional(),
});

export const updateOrganizationSchema = createOrganizationSchema.partial();

export const updateMemberRoleSchema = z.object({
  userId: idSchema,
  role: z.string().min(1, "Role is required"),
});