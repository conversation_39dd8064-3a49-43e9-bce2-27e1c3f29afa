import { z } from "zod";
import { publicProcedure, protectedProcedure } from "../lib/orpc";
import { prisma } from "../lib/database";
import {
  createNewsArticleSchema,
  updateNewsArticleSchema,
  newsFilterSchema,
  idSchema,
} from "../lib/validation";
import { getPaginationParams, createPaginationResult } from "../lib/utils";

export const newsRouter = {
  /**
   * Get published news articles with filtering (public endpoint)
   */
  list: publicProcedure
    .input(newsFilterSchema)
    .handler(async ({ input, context }) => {
      const { page, limit, skip } = getPaginationParams(input);
      const where: any = {
        isPublished: input.isPublished ?? true,
        category: input.category ?? undefined,
        organizationId: input.organizationId ?? undefined,
        authorName: input.authorName ? { contains: input.authorName, mode: "insensitive" } : undefined,
      };
      const [data, total] = await Promise.all([
        prisma.newsArticle.findMany({
          where,
          orderBy: { createdAt: "desc" },
          skip,
          take: limit,
        }),
        prisma.newsArticle.count({ where }),
      ]);
      return createPaginationResult(data, total, page, limit);
    }),

  /**
   * Get specific news article (public endpoint)
   */
  getById: publicProcedure
    .input(z.object({ id: idSchema }))
    .handler(async ({ input, context }) => {
      const article = await prisma.newsArticle.findUnique({ where: { id: input.id } });
      if (!article) throw new Error("News article not found");
      return article;
    }),

  /**
   * Get articles by category (public endpoint)
   */
  getByCategory: publicProcedure
    .input(
      z.object({
        category: z.enum([
          "GENERAL",
          "EVENTS",
          "SUCCESS_STORIES",
          "OPPORTUNITIES",
          "ANNOUNCEMENTS",
        ]),
        page: z.number().int().min(1).default(1),
        limit: z.number().int().min(1).max(100).default(20),
        organizationId: idSchema.optional(),
      })
    )
    .handler(async ({ input }) => {
      const { page, limit, skip } = getPaginationParams(input);
      const where: any = { category: input.category, isPublished: true };
      if (input.organizationId) where.organizationId = input.organizationId;
      const [data, total] = await Promise.all([
        prisma.newsArticle.findMany({ where, orderBy: { createdAt: "desc" }, skip, take: limit }),
        prisma.newsArticle.count({ where }),
      ]);
      return createPaginationResult(data, total, page, limit);
    }),

  /**
   * Get recent articles (public endpoint)
   */
  getRecent: publicProcedure
    .input(
      z.object({
        limit: z.number().int().min(1).max(50).default(10),
        organizationId: idSchema.optional(),
      })
    )
    .handler(async ({ input }) => {
      const where: any = { isPublished: true };
      if (input.organizationId) where.organizationId = input.organizationId;
      return prisma.newsArticle.findMany({ where, orderBy: { createdAt: "desc" }, take: input.limit });
    }),

  /**
   * Get featured articles (public endpoint)
   */
  getFeatured: publicProcedure
    .input(
      z.object({
        limit: z.number().int().min(1).max(20).default(5),
        organizationId: idSchema.optional(),
      })
    )
    .handler(async ({ input }) => {
      const where: any = { isPublished: true };
      if (input.organizationId) where.organizationId = input.organizationId;
      return prisma.newsArticle.findMany({ where, orderBy: { publishedAt: "desc" }, take: input.limit });
    }),

  /**
   * Get articles by author (public endpoint)
   */
  getByAuthor: publicProcedure
    .input(
      z.object({
        authorName: z.string().min(1),
        page: z.number().int().min(1).default(1),
        limit: z.number().int().min(1).max(100).default(20),
        organizationId: idSchema.optional(),
      })
    )
    .handler(async ({ input }) => {
      const { page, limit, skip } = getPaginationParams(input);
      const where: any = { authorName: { contains: input.authorName, mode: "insensitive" }, isPublished: true };
      if (input.organizationId) where.organizationId = input.organizationId;
      const [data, total] = await Promise.all([
        prisma.newsArticle.findMany({ where, orderBy: { createdAt: "desc" }, skip, take: limit }),
        prisma.newsArticle.count({ where }),
      ]);
      return createPaginationResult(data, total, page, limit);
    }),

  /**
   * Get news statistics (public endpoint)
   */
  getStats: publicProcedure
    .input(
      z.object({
        organizationId: idSchema.optional(),
      })
    )
    .handler(async ({ input }) => {
      const where: any = {};
      if (input.organizationId) where.organizationId = input.organizationId;
      const [total, published] = await Promise.all([
        prisma.newsArticle.count({ where }),
        prisma.newsArticle.count({ where: { ...where, isPublished: true } }),
      ]);
      return { total, published };
    }),

  // Admin endpoints for news management
  admin: {
    /**
     * Create news article (admin only)
     */
    create: protectedProcedure
      .input(createNewsArticleSchema)
      .handler(async ({ input, context }) => {
        return prisma.newsArticle.create({
          data: {
            ...input,
            organizationId:
              input.organizationId ||
              context.session?.session.activeOrganizationId ||
              undefined,
          },
        });
      }),

    /**
     * Update news article (admin only)
     */
    update: protectedProcedure
      .input(z.object({ id: idSchema }).merge(updateNewsArticleSchema))
      .handler(async ({ input, context }) => {
        const { id, ...updateData } = input;
        return prisma.newsArticle.update({ where: { id }, data: updateData });
      }),

    /**
     * Delete news article (admin only)
     */
    delete: protectedProcedure
      .input(z.object({ id: idSchema }))
      .handler(async ({ input, context }) => {
        await prisma.newsArticle.delete({ where: { id: input.id } });
        return { success: true };
      }),

    /**
     * Publish/unpublish article (admin only)
     */
    togglePublication: protectedProcedure
      .input(
        z.object({
          articleId: idSchema,
          isPublished: z.boolean(),
        })
      )
      .handler(async ({ input }) => {
        return prisma.newsArticle.update({
          where: { id: input.articleId },
          data: {
            isPublished: input.isPublished,
            publishedAt: input.isPublished ? new Date() : null,
          },
        });
      }),

    /**
     * Get organization articles (admin only)
     */
    getOrganizationArticles: protectedProcedure
      .input(
        z.object({
          page: z.number().int().min(1).default(1),
          limit: z.number().int().min(1).max(100).default(20),
          isPublished: z.boolean().optional(),
        })
      )
      .handler(async ({ input, context }) => {
        const orgId = context.session?.session.activeOrganizationId;
        if (!orgId) throw new Error("Organization not set");
        const { page, limit, skip } = getPaginationParams(input);
        const where: any = { organizationId: orgId };
        if (typeof input.isPublished === "boolean") {
          where.isPublished = input.isPublished;
        }
        const [data, total] = await Promise.all([
          prisma.newsArticle.findMany({ where, orderBy: { createdAt: "desc" }, skip, take: limit }),
          prisma.newsArticle.count({ where }),
        ]);
        return createPaginationResult(data, total, page, limit);
      }),

    /**
     * Get organization news statistics (admin only)
     */
    getOrganizationStats: protectedProcedure.handler(async ({ context }) => {
      const orgId = context.session?.session.activeOrganizationId;
      if (!orgId) throw new Error("Organization not set");
      const [total, published] = await Promise.all([
        prisma.newsArticle.count({ where: { organizationId: orgId } }),
        prisma.newsArticle.count({ where: { organizationId: orgId, isPublished: true } }),
      ]);
      return { total, published };
    }),
  },
};
