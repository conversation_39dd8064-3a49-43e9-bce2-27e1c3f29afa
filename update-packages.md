# Package Update Commands for PROTEC Monorepo

## Option 1: PNPM Workspace Commands (Recommended)

### Update all packages to latest versions
```bash
# Update all dependencies across all workspaces
pnpm update --recursive

# Update all dependencies to latest (ignoring semver)
pnpm update --recursive --latest

# Interactive update (choose which packages to update)
pnpm update --recursive --interactive
```

### Check for outdated packages
```bash
# Check outdated packages across all workspaces
pnpm outdated --recursive
```

## Option 2: Turbo + PNPM Commands

### Add to package.json scripts
Add these scripts to your root `package.json`:

```json
{
  "scripts": {
    "update": "pnpm update --recursive",
    "update:latest": "pnpm update --recursive --latest",
    "update:interactive": "pnpm update --recursive --interactive",
    "outdated": "pnpm outdated --recursive"
  }
}
```

Then run:
```bash
pnpm run update
pnpm run update:latest
pnpm run update:interactive
pnpm run outdated
```

## Option 3: Individual App Updates

### Update specific apps
```bash
# Update server app only
pnpm --filter server update

# Update web app only  
pnpm --filter web update

# Update native app only
pnpm --filter native update
```

## Option 4: Using npm-check-updates (ncu)

### Install globally
```bash
pnpm add -g npm-check-updates
```

### Update all workspaces
```bash
# Check what would be updated
ncu --workspaces

# Update all package.json files
ncu --workspaces -u

# Then install the updates
pnpm install
```

## Recommended Workflow

1. **Check outdated packages first:**
   ```bash
   pnpm outdated --recursive
   ```

2. **Use interactive update for safety:**
   ```bash
   pnpm update --recursive --interactive
   ```

3. **Test after updates:**
   ```bash
   pnpm run check-types
   pnpm run build
   ```

4. **Run development servers to verify:**
   ```bash
   pnpm run dev
   ```

## Notes

- The `--recursive` flag ensures all workspace packages are updated
- The `--latest` flag ignores semver constraints and updates to the absolute latest versions
- The `--interactive` flag lets you choose which packages to update
- Always test your applications after major updates
- Consider updating in stages (dev dependencies first, then production dependencies)