generator client {
  provider     = "prisma-client"
  output       = "../generated"
  moduleFormat = "esm"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// Alumni Profile Models
model AlumniProfile {
  id     String @id @default(cuid())
  userId String @unique
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Basic Information
  firstName      String
  lastName       String
  displayName    String?
  bio            String? @db.Text
  profilePicture String?

  // Organization Context (optional - for multi-org support)
  primaryOrganizationId String?
  primaryOrganization   Organization? @relation(fields: [primaryOrganizationId], references: [id])

  // PROTEC History
  graduationYear Int
  programType    String // e.g., "Mathematics", "Science", "Engineering"
  centerLocation String // PROTEC center they attended
  achievements   String? @db.Text

  // Career Information
  currentPosition String?
  currentCompany  String?
  industry        String?
  location        String?
  linkedInUrl     String?
  phoneNumber     String?

  // Additional PROTEC-specific fields
  mentorshipOffered <PERSON><PERSON><PERSON> @default(false)
  mentorshipSought  Boolean @default(false)
  skillsOffered     String? @db.Text // Skills they can mentor in
  skillsWanted      String? @db.Text // Skills they want to learn

  // Privacy Settings
  profileVisibility ProfileVisibility @default(PUBLIC)
  showEmail         Boolean           @default(false)
  showPhone         Boolean           @default(false)
  showLocation      Boolean           @default(true)

  // Relationships
  posts              Post[]
  connections        Connection[]        @relation("UserConnections")
  connectionRequests Connection[]        @relation("RequestedConnections")
  eventRegistrations EventRegistration[]
  donations          Donation[]
  messages           Message[]           @relation("MessageSender")
  receivedMessages   Message[]           @relation("MessageReceiver")
  postLikes          PostLike[]
  comments           Comment[]
  notifications      Notification[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("alumni_profile")
}

// Connection/Networking Models
model Connection {
  id          String           @id @default(cuid())
  requesterId String
  requester   AlumniProfile    @relation("UserConnections", fields: [requesterId], references: [id], onDelete: Cascade)
  requestedId String
  requested   AlumniProfile    @relation("RequestedConnections", fields: [requestedId], references: [id], onDelete: Cascade)
  status      ConnectionStatus @default(PENDING)
  requestedAt DateTime         @default(now())
  acceptedAt  DateTime?

  @@unique([requesterId, requestedId])
  @@map("connection")
}

model Message {
  id         String        @id @default(cuid())
  senderId   String
  sender     AlumniProfile @relation("MessageSender", fields: [senderId], references: [id], onDelete: Cascade)
  receiverId String
  receiver   AlumniProfile @relation("MessageReceiver", fields: [receiverId], references: [id], onDelete: Cascade)
  content    String        @db.Text
  isRead     Boolean       @default(false)
  createdAt  DateTime      @default(now())

  @@map("message")
}

// Content Models
model Post {
  id          String        @id @default(cuid())
  authorId    String
  author      AlumniProfile @relation(fields: [authorId], references: [id], onDelete: Cascade)
  title       String?
  content     String        @db.Text
  imageUrl    String?
  postType    PostType      @default(GENERAL)
  isPublished Boolean       @default(true)

  // Organization scoping for posts
  organizationId String?
  organization   Organization? @relation(fields: [organizationId], references: [id])

  // Engagement
  likes    PostLike[]
  comments Comment[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("post")
}

model PostLike {
  id        String        @id @default(cuid())
  postId    String
  post      Post          @relation(fields: [postId], references: [id], onDelete: Cascade)
  userId    String
  user      AlumniProfile @relation(fields: [userId], references: [id], onDelete: Cascade)
  createdAt DateTime      @default(now())

  @@unique([postId, userId])
  @@map("post_like")
}

model Comment {
  id        String        @id @default(cuid())
  postId    String
  post      Post          @relation(fields: [postId], references: [id], onDelete: Cascade)
  authorId  String
  author    AlumniProfile @relation(fields: [authorId], references: [id], onDelete: Cascade)
  content   String        @db.Text
  createdAt DateTime      @default(now())
  updatedAt DateTime      @updatedAt

  @@map("comment")
}

// Event Models
model Event {
  id            String    @id @default(cuid())
  title         String
  description   String    @db.Text
  eventType     EventType @default(WORKSHOP)
  location      String?
  isVirtual     Boolean   @default(false)
  virtualLink   String?
  startDateTime DateTime
  endDateTime   DateTime
  maxAttendees  Int?
  imageUrl      String?

  // Organization scoping
  organizationId String?
  organization   Organization? @relation(fields: [organizationId], references: [id])

  // Registration
  registrations EventRegistration[]

  // Organizer (organization member)
  organizerId String?
  organizer   Member? @relation(fields: [organizerId], references: [id])

  isPublished Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("event")
}

model EventRegistration {
  id           String             @id @default(cuid())
  eventId      String
  event        Event              @relation(fields: [eventId], references: [id], onDelete: Cascade)
  attendeeId   String
  attendee     AlumniProfile      @relation(fields: [attendeeId], references: [id], onDelete: Cascade)
  status       RegistrationStatus @default(REGISTERED)
  registeredAt DateTime           @default(now())

  @@unique([eventId, attendeeId])
  @@map("event_registration")
}

// Donation Models
model Donation {
  id           String        @id @default(cuid())
  donorId      String
  donor        AlumniProfile @relation(fields: [donorId], references: [id], onDelete: Cascade)
  amount       Decimal       @db.Decimal(10, 2)
  currency     String        @default("ZAR")
  donationType DonationType  @default(ONE_TIME)

  // Organization receiving the donation
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id])

  // Payment Information
  paymentMethod String? // e.g., "PayFast", "PayPal", "Card"
  transactionId String?       @unique
  paymentStatus PaymentStatus @default(PENDING)

  // Purpose/Campaign
  purpose     String? // What the donation is for
  isAnonymous Boolean @default(false)

  // Recurring donation info
  recurringId String? // For linking recurring donations
  nextDueDate DateTime? // For recurring donations

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("donation")
}

// News and Announcements
model NewsArticle {
  id          String       @id @default(cuid())
  title       String
  content     String       @db.Text
  excerpt     String?      @db.Text
  imageUrl    String?
  authorName  String // PROTEC staff member name
  category    NewsCategory @default(GENERAL)
  isPublished Boolean      @default(false)
  publishedAt DateTime?

  // Organization scoping
  organizationId String?
  organization   Organization? @relation(fields: [organizationId], references: [id])

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("news_article")
}

// Notification System
model Notification {
  id             String           @id @default(cuid())
  recipientId    String
  recipient      AlumniProfile    @relation(fields: [recipientId], references: [id], onDelete: Cascade)
  type           NotificationType
  title          String
  message        String           @db.Text
  isRead         Boolean          @default(false)
  actionUrl      String? // Deep link to relevant content
  createdAt      DateTime         @default(now())
  Organization   Organization?    @relation(fields: [organizationId], references: [id])
  organizationId String?

  @@map("notification")
}

// Enums
enum ProfileVisibility {
  PUBLIC
  ALUMNI_ONLY
  PRIVATE
}

enum ConnectionStatus {
  PENDING
  ACCEPTED
  DECLINED
  BLOCKED
}

enum PostType {
  GENERAL
  SUCCESS_STORY
  JOB_OPPORTUNITY
  MENTORSHIP
  ANNOUNCEMENT
}

enum EventType {
  WORKSHOP
  NETWORKING
  CONFERENCE
  WEBINAR
  SOCIAL
  FUNDRAISING
  MENTORSHIP
}

enum RegistrationStatus {
  REGISTERED
  ATTENDED
  NO_SHOW
  CANCELLED
}

enum DonationType {
  ONE_TIME
  MONTHLY
  QUARTERLY
  ANNUAL
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
  CANCELLED
}

enum NewsCategory {
  GENERAL
  EVENTS
  SUCCESS_STORIES
  OPPORTUNITIES
  ANNOUNCEMENTS
}

enum NotificationType {
  CONNECTION_REQUEST
  CONNECTION_ACCEPTED
  EVENT_REMINDER
  EVENT_REGISTRATION
  NEW_MESSAGE
  POST_LIKE
  POST_COMMENT
  DONATION_CONFIRMATION
  NEWS_ARTICLE
  SYSTEM_ANNOUNCEMENT
}
