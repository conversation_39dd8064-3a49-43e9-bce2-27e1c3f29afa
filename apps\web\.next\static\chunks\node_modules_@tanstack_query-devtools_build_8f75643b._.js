(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@tanstack/query-devtools/build/DevtoolsComponent/EDEL3XIZ.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_681bd419._.js",
  "static/chunks/node_modules_@tanstack_query-devtools_build_DevtoolsComponent_EDEL3XIZ_d954d53f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@tanstack/query-devtools/build/DevtoolsComponent/EDEL3XIZ.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/@tanstack/query-devtools/build/DevtoolsPanelComponent/RN252AT2.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_7c264e54._.js",
  "static/chunks/dd92d_modules_@tanstack_query-devtools_build_DevtoolsPanelComponent_RN252AT2_d954d53f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@tanstack/query-devtools/build/DevtoolsPanelComponent/RN252AT2.js [app-client] (ecmascript)");
    });
});
}),
}]);