
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `NewsArticle` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model NewsArticle
 * 
 */
export type NewsArticleModel = runtime.Types.Result.DefaultSelection<Prisma.$NewsArticlePayload>

export type AggregateNewsArticle = {
  _count: NewsArticleCountAggregateOutputType | null
  _min: NewsArticleMinAggregateOutputType | null
  _max: NewsArticleMaxAggregateOutputType | null
}

export type NewsArticleMinAggregateOutputType = {
  id: string | null
  title: string | null
  content: string | null
  excerpt: string | null
  imageUrl: string | null
  authorName: string | null
  category: $Enums.NewsCategory | null
  isPublished: boolean | null
  publishedAt: Date | null
  organizationId: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type NewsArticleMaxAggregateOutputType = {
  id: string | null
  title: string | null
  content: string | null
  excerpt: string | null
  imageUrl: string | null
  authorName: string | null
  category: $Enums.NewsCategory | null
  isPublished: boolean | null
  publishedAt: Date | null
  organizationId: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type NewsArticleCountAggregateOutputType = {
  id: number
  title: number
  content: number
  excerpt: number
  imageUrl: number
  authorName: number
  category: number
  isPublished: number
  publishedAt: number
  organizationId: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type NewsArticleMinAggregateInputType = {
  id?: true
  title?: true
  content?: true
  excerpt?: true
  imageUrl?: true
  authorName?: true
  category?: true
  isPublished?: true
  publishedAt?: true
  organizationId?: true
  createdAt?: true
  updatedAt?: true
}

export type NewsArticleMaxAggregateInputType = {
  id?: true
  title?: true
  content?: true
  excerpt?: true
  imageUrl?: true
  authorName?: true
  category?: true
  isPublished?: true
  publishedAt?: true
  organizationId?: true
  createdAt?: true
  updatedAt?: true
}

export type NewsArticleCountAggregateInputType = {
  id?: true
  title?: true
  content?: true
  excerpt?: true
  imageUrl?: true
  authorName?: true
  category?: true
  isPublished?: true
  publishedAt?: true
  organizationId?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type NewsArticleAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which NewsArticle to aggregate.
   */
  where?: Prisma.NewsArticleWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of NewsArticles to fetch.
   */
  orderBy?: Prisma.NewsArticleOrderByWithRelationInput | Prisma.NewsArticleOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.NewsArticleWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` NewsArticles from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` NewsArticles.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned NewsArticles
  **/
  _count?: true | NewsArticleCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: NewsArticleMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: NewsArticleMaxAggregateInputType
}

export type GetNewsArticleAggregateType<T extends NewsArticleAggregateArgs> = {
      [P in keyof T & keyof AggregateNewsArticle]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateNewsArticle[P]>
    : Prisma.GetScalarType<T[P], AggregateNewsArticle[P]>
}




export type NewsArticleGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.NewsArticleWhereInput
  orderBy?: Prisma.NewsArticleOrderByWithAggregationInput | Prisma.NewsArticleOrderByWithAggregationInput[]
  by: Prisma.NewsArticleScalarFieldEnum[] | Prisma.NewsArticleScalarFieldEnum
  having?: Prisma.NewsArticleScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: NewsArticleCountAggregateInputType | true
  _min?: NewsArticleMinAggregateInputType
  _max?: NewsArticleMaxAggregateInputType
}

export type NewsArticleGroupByOutputType = {
  id: string
  title: string
  content: string
  excerpt: string | null
  imageUrl: string | null
  authorName: string
  category: $Enums.NewsCategory
  isPublished: boolean
  publishedAt: Date | null
  organizationId: string | null
  createdAt: Date
  updatedAt: Date
  _count: NewsArticleCountAggregateOutputType | null
  _min: NewsArticleMinAggregateOutputType | null
  _max: NewsArticleMaxAggregateOutputType | null
}

type GetNewsArticleGroupByPayload<T extends NewsArticleGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<NewsArticleGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof NewsArticleGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], NewsArticleGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], NewsArticleGroupByOutputType[P]>
      }
    >
  >



export type NewsArticleWhereInput = {
  AND?: Prisma.NewsArticleWhereInput | Prisma.NewsArticleWhereInput[]
  OR?: Prisma.NewsArticleWhereInput[]
  NOT?: Prisma.NewsArticleWhereInput | Prisma.NewsArticleWhereInput[]
  id?: Prisma.StringFilter<"NewsArticle"> | string
  title?: Prisma.StringFilter<"NewsArticle"> | string
  content?: Prisma.StringFilter<"NewsArticle"> | string
  excerpt?: Prisma.StringNullableFilter<"NewsArticle"> | string | null
  imageUrl?: Prisma.StringNullableFilter<"NewsArticle"> | string | null
  authorName?: Prisma.StringFilter<"NewsArticle"> | string
  category?: Prisma.EnumNewsCategoryFilter<"NewsArticle"> | $Enums.NewsCategory
  isPublished?: Prisma.BoolFilter<"NewsArticle"> | boolean
  publishedAt?: Prisma.DateTimeNullableFilter<"NewsArticle"> | Date | string | null
  organizationId?: Prisma.StringNullableFilter<"NewsArticle"> | string | null
  createdAt?: Prisma.DateTimeFilter<"NewsArticle"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"NewsArticle"> | Date | string
  organization?: Prisma.XOR<Prisma.OrganizationNullableScalarRelationFilter, Prisma.OrganizationWhereInput> | null
}

export type NewsArticleOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  title?: Prisma.SortOrder
  content?: Prisma.SortOrder
  excerpt?: Prisma.SortOrderInput | Prisma.SortOrder
  imageUrl?: Prisma.SortOrderInput | Prisma.SortOrder
  authorName?: Prisma.SortOrder
  category?: Prisma.SortOrder
  isPublished?: Prisma.SortOrder
  publishedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  organizationId?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  organization?: Prisma.OrganizationOrderByWithRelationInput
  _relevance?: Prisma.NewsArticleOrderByRelevanceInput
}

export type NewsArticleWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  AND?: Prisma.NewsArticleWhereInput | Prisma.NewsArticleWhereInput[]
  OR?: Prisma.NewsArticleWhereInput[]
  NOT?: Prisma.NewsArticleWhereInput | Prisma.NewsArticleWhereInput[]
  title?: Prisma.StringFilter<"NewsArticle"> | string
  content?: Prisma.StringFilter<"NewsArticle"> | string
  excerpt?: Prisma.StringNullableFilter<"NewsArticle"> | string | null
  imageUrl?: Prisma.StringNullableFilter<"NewsArticle"> | string | null
  authorName?: Prisma.StringFilter<"NewsArticle"> | string
  category?: Prisma.EnumNewsCategoryFilter<"NewsArticle"> | $Enums.NewsCategory
  isPublished?: Prisma.BoolFilter<"NewsArticle"> | boolean
  publishedAt?: Prisma.DateTimeNullableFilter<"NewsArticle"> | Date | string | null
  organizationId?: Prisma.StringNullableFilter<"NewsArticle"> | string | null
  createdAt?: Prisma.DateTimeFilter<"NewsArticle"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"NewsArticle"> | Date | string
  organization?: Prisma.XOR<Prisma.OrganizationNullableScalarRelationFilter, Prisma.OrganizationWhereInput> | null
}, "id">

export type NewsArticleOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  title?: Prisma.SortOrder
  content?: Prisma.SortOrder
  excerpt?: Prisma.SortOrderInput | Prisma.SortOrder
  imageUrl?: Prisma.SortOrderInput | Prisma.SortOrder
  authorName?: Prisma.SortOrder
  category?: Prisma.SortOrder
  isPublished?: Prisma.SortOrder
  publishedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  organizationId?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.NewsArticleCountOrderByAggregateInput
  _max?: Prisma.NewsArticleMaxOrderByAggregateInput
  _min?: Prisma.NewsArticleMinOrderByAggregateInput
}

export type NewsArticleScalarWhereWithAggregatesInput = {
  AND?: Prisma.NewsArticleScalarWhereWithAggregatesInput | Prisma.NewsArticleScalarWhereWithAggregatesInput[]
  OR?: Prisma.NewsArticleScalarWhereWithAggregatesInput[]
  NOT?: Prisma.NewsArticleScalarWhereWithAggregatesInput | Prisma.NewsArticleScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"NewsArticle"> | string
  title?: Prisma.StringWithAggregatesFilter<"NewsArticle"> | string
  content?: Prisma.StringWithAggregatesFilter<"NewsArticle"> | string
  excerpt?: Prisma.StringNullableWithAggregatesFilter<"NewsArticle"> | string | null
  imageUrl?: Prisma.StringNullableWithAggregatesFilter<"NewsArticle"> | string | null
  authorName?: Prisma.StringWithAggregatesFilter<"NewsArticle"> | string
  category?: Prisma.EnumNewsCategoryWithAggregatesFilter<"NewsArticle"> | $Enums.NewsCategory
  isPublished?: Prisma.BoolWithAggregatesFilter<"NewsArticle"> | boolean
  publishedAt?: Prisma.DateTimeNullableWithAggregatesFilter<"NewsArticle"> | Date | string | null
  organizationId?: Prisma.StringNullableWithAggregatesFilter<"NewsArticle"> | string | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"NewsArticle"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"NewsArticle"> | Date | string
}

export type NewsArticleCreateInput = {
  id?: string
  title: string
  content: string
  excerpt?: string | null
  imageUrl?: string | null
  authorName: string
  category?: $Enums.NewsCategory
  isPublished?: boolean
  publishedAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  organization?: Prisma.OrganizationCreateNestedOneWithoutNewsArticlesInput
}

export type NewsArticleUncheckedCreateInput = {
  id?: string
  title: string
  content: string
  excerpt?: string | null
  imageUrl?: string | null
  authorName: string
  category?: $Enums.NewsCategory
  isPublished?: boolean
  publishedAt?: Date | string | null
  organizationId?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type NewsArticleUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  content?: Prisma.StringFieldUpdateOperationsInput | string
  excerpt?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  imageUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  authorName?: Prisma.StringFieldUpdateOperationsInput | string
  category?: Prisma.EnumNewsCategoryFieldUpdateOperationsInput | $Enums.NewsCategory
  isPublished?: Prisma.BoolFieldUpdateOperationsInput | boolean
  publishedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  organization?: Prisma.OrganizationUpdateOneWithoutNewsArticlesNestedInput
}

export type NewsArticleUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  content?: Prisma.StringFieldUpdateOperationsInput | string
  excerpt?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  imageUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  authorName?: Prisma.StringFieldUpdateOperationsInput | string
  category?: Prisma.EnumNewsCategoryFieldUpdateOperationsInput | $Enums.NewsCategory
  isPublished?: Prisma.BoolFieldUpdateOperationsInput | boolean
  publishedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  organizationId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type NewsArticleCreateManyInput = {
  id?: string
  title: string
  content: string
  excerpt?: string | null
  imageUrl?: string | null
  authorName: string
  category?: $Enums.NewsCategory
  isPublished?: boolean
  publishedAt?: Date | string | null
  organizationId?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type NewsArticleUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  content?: Prisma.StringFieldUpdateOperationsInput | string
  excerpt?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  imageUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  authorName?: Prisma.StringFieldUpdateOperationsInput | string
  category?: Prisma.EnumNewsCategoryFieldUpdateOperationsInput | $Enums.NewsCategory
  isPublished?: Prisma.BoolFieldUpdateOperationsInput | boolean
  publishedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type NewsArticleUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  content?: Prisma.StringFieldUpdateOperationsInput | string
  excerpt?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  imageUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  authorName?: Prisma.StringFieldUpdateOperationsInput | string
  category?: Prisma.EnumNewsCategoryFieldUpdateOperationsInput | $Enums.NewsCategory
  isPublished?: Prisma.BoolFieldUpdateOperationsInput | boolean
  publishedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  organizationId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type NewsArticleListRelationFilter = {
  every?: Prisma.NewsArticleWhereInput
  some?: Prisma.NewsArticleWhereInput
  none?: Prisma.NewsArticleWhereInput
}

export type NewsArticleOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type NewsArticleOrderByRelevanceInput = {
  fields: Prisma.NewsArticleOrderByRelevanceFieldEnum | Prisma.NewsArticleOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type NewsArticleCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  title?: Prisma.SortOrder
  content?: Prisma.SortOrder
  excerpt?: Prisma.SortOrder
  imageUrl?: Prisma.SortOrder
  authorName?: Prisma.SortOrder
  category?: Prisma.SortOrder
  isPublished?: Prisma.SortOrder
  publishedAt?: Prisma.SortOrder
  organizationId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type NewsArticleMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  title?: Prisma.SortOrder
  content?: Prisma.SortOrder
  excerpt?: Prisma.SortOrder
  imageUrl?: Prisma.SortOrder
  authorName?: Prisma.SortOrder
  category?: Prisma.SortOrder
  isPublished?: Prisma.SortOrder
  publishedAt?: Prisma.SortOrder
  organizationId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type NewsArticleMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  title?: Prisma.SortOrder
  content?: Prisma.SortOrder
  excerpt?: Prisma.SortOrder
  imageUrl?: Prisma.SortOrder
  authorName?: Prisma.SortOrder
  category?: Prisma.SortOrder
  isPublished?: Prisma.SortOrder
  publishedAt?: Prisma.SortOrder
  organizationId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type NewsArticleCreateNestedManyWithoutOrganizationInput = {
  create?: Prisma.XOR<Prisma.NewsArticleCreateWithoutOrganizationInput, Prisma.NewsArticleUncheckedCreateWithoutOrganizationInput> | Prisma.NewsArticleCreateWithoutOrganizationInput[] | Prisma.NewsArticleUncheckedCreateWithoutOrganizationInput[]
  connectOrCreate?: Prisma.NewsArticleCreateOrConnectWithoutOrganizationInput | Prisma.NewsArticleCreateOrConnectWithoutOrganizationInput[]
  createMany?: Prisma.NewsArticleCreateManyOrganizationInputEnvelope
  connect?: Prisma.NewsArticleWhereUniqueInput | Prisma.NewsArticleWhereUniqueInput[]
}

export type NewsArticleUncheckedCreateNestedManyWithoutOrganizationInput = {
  create?: Prisma.XOR<Prisma.NewsArticleCreateWithoutOrganizationInput, Prisma.NewsArticleUncheckedCreateWithoutOrganizationInput> | Prisma.NewsArticleCreateWithoutOrganizationInput[] | Prisma.NewsArticleUncheckedCreateWithoutOrganizationInput[]
  connectOrCreate?: Prisma.NewsArticleCreateOrConnectWithoutOrganizationInput | Prisma.NewsArticleCreateOrConnectWithoutOrganizationInput[]
  createMany?: Prisma.NewsArticleCreateManyOrganizationInputEnvelope
  connect?: Prisma.NewsArticleWhereUniqueInput | Prisma.NewsArticleWhereUniqueInput[]
}

export type NewsArticleUpdateManyWithoutOrganizationNestedInput = {
  create?: Prisma.XOR<Prisma.NewsArticleCreateWithoutOrganizationInput, Prisma.NewsArticleUncheckedCreateWithoutOrganizationInput> | Prisma.NewsArticleCreateWithoutOrganizationInput[] | Prisma.NewsArticleUncheckedCreateWithoutOrganizationInput[]
  connectOrCreate?: Prisma.NewsArticleCreateOrConnectWithoutOrganizationInput | Prisma.NewsArticleCreateOrConnectWithoutOrganizationInput[]
  upsert?: Prisma.NewsArticleUpsertWithWhereUniqueWithoutOrganizationInput | Prisma.NewsArticleUpsertWithWhereUniqueWithoutOrganizationInput[]
  createMany?: Prisma.NewsArticleCreateManyOrganizationInputEnvelope
  set?: Prisma.NewsArticleWhereUniqueInput | Prisma.NewsArticleWhereUniqueInput[]
  disconnect?: Prisma.NewsArticleWhereUniqueInput | Prisma.NewsArticleWhereUniqueInput[]
  delete?: Prisma.NewsArticleWhereUniqueInput | Prisma.NewsArticleWhereUniqueInput[]
  connect?: Prisma.NewsArticleWhereUniqueInput | Prisma.NewsArticleWhereUniqueInput[]
  update?: Prisma.NewsArticleUpdateWithWhereUniqueWithoutOrganizationInput | Prisma.NewsArticleUpdateWithWhereUniqueWithoutOrganizationInput[]
  updateMany?: Prisma.NewsArticleUpdateManyWithWhereWithoutOrganizationInput | Prisma.NewsArticleUpdateManyWithWhereWithoutOrganizationInput[]
  deleteMany?: Prisma.NewsArticleScalarWhereInput | Prisma.NewsArticleScalarWhereInput[]
}

export type NewsArticleUncheckedUpdateManyWithoutOrganizationNestedInput = {
  create?: Prisma.XOR<Prisma.NewsArticleCreateWithoutOrganizationInput, Prisma.NewsArticleUncheckedCreateWithoutOrganizationInput> | Prisma.NewsArticleCreateWithoutOrganizationInput[] | Prisma.NewsArticleUncheckedCreateWithoutOrganizationInput[]
  connectOrCreate?: Prisma.NewsArticleCreateOrConnectWithoutOrganizationInput | Prisma.NewsArticleCreateOrConnectWithoutOrganizationInput[]
  upsert?: Prisma.NewsArticleUpsertWithWhereUniqueWithoutOrganizationInput | Prisma.NewsArticleUpsertWithWhereUniqueWithoutOrganizationInput[]
  createMany?: Prisma.NewsArticleCreateManyOrganizationInputEnvelope
  set?: Prisma.NewsArticleWhereUniqueInput | Prisma.NewsArticleWhereUniqueInput[]
  disconnect?: Prisma.NewsArticleWhereUniqueInput | Prisma.NewsArticleWhereUniqueInput[]
  delete?: Prisma.NewsArticleWhereUniqueInput | Prisma.NewsArticleWhereUniqueInput[]
  connect?: Prisma.NewsArticleWhereUniqueInput | Prisma.NewsArticleWhereUniqueInput[]
  update?: Prisma.NewsArticleUpdateWithWhereUniqueWithoutOrganizationInput | Prisma.NewsArticleUpdateWithWhereUniqueWithoutOrganizationInput[]
  updateMany?: Prisma.NewsArticleUpdateManyWithWhereWithoutOrganizationInput | Prisma.NewsArticleUpdateManyWithWhereWithoutOrganizationInput[]
  deleteMany?: Prisma.NewsArticleScalarWhereInput | Prisma.NewsArticleScalarWhereInput[]
}

export type EnumNewsCategoryFieldUpdateOperationsInput = {
  set?: $Enums.NewsCategory
}

export type NewsArticleCreateWithoutOrganizationInput = {
  id?: string
  title: string
  content: string
  excerpt?: string | null
  imageUrl?: string | null
  authorName: string
  category?: $Enums.NewsCategory
  isPublished?: boolean
  publishedAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type NewsArticleUncheckedCreateWithoutOrganizationInput = {
  id?: string
  title: string
  content: string
  excerpt?: string | null
  imageUrl?: string | null
  authorName: string
  category?: $Enums.NewsCategory
  isPublished?: boolean
  publishedAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type NewsArticleCreateOrConnectWithoutOrganizationInput = {
  where: Prisma.NewsArticleWhereUniqueInput
  create: Prisma.XOR<Prisma.NewsArticleCreateWithoutOrganizationInput, Prisma.NewsArticleUncheckedCreateWithoutOrganizationInput>
}

export type NewsArticleCreateManyOrganizationInputEnvelope = {
  data: Prisma.NewsArticleCreateManyOrganizationInput | Prisma.NewsArticleCreateManyOrganizationInput[]
  skipDuplicates?: boolean
}

export type NewsArticleUpsertWithWhereUniqueWithoutOrganizationInput = {
  where: Prisma.NewsArticleWhereUniqueInput
  update: Prisma.XOR<Prisma.NewsArticleUpdateWithoutOrganizationInput, Prisma.NewsArticleUncheckedUpdateWithoutOrganizationInput>
  create: Prisma.XOR<Prisma.NewsArticleCreateWithoutOrganizationInput, Prisma.NewsArticleUncheckedCreateWithoutOrganizationInput>
}

export type NewsArticleUpdateWithWhereUniqueWithoutOrganizationInput = {
  where: Prisma.NewsArticleWhereUniqueInput
  data: Prisma.XOR<Prisma.NewsArticleUpdateWithoutOrganizationInput, Prisma.NewsArticleUncheckedUpdateWithoutOrganizationInput>
}

export type NewsArticleUpdateManyWithWhereWithoutOrganizationInput = {
  where: Prisma.NewsArticleScalarWhereInput
  data: Prisma.XOR<Prisma.NewsArticleUpdateManyMutationInput, Prisma.NewsArticleUncheckedUpdateManyWithoutOrganizationInput>
}

export type NewsArticleScalarWhereInput = {
  AND?: Prisma.NewsArticleScalarWhereInput | Prisma.NewsArticleScalarWhereInput[]
  OR?: Prisma.NewsArticleScalarWhereInput[]
  NOT?: Prisma.NewsArticleScalarWhereInput | Prisma.NewsArticleScalarWhereInput[]
  id?: Prisma.StringFilter<"NewsArticle"> | string
  title?: Prisma.StringFilter<"NewsArticle"> | string
  content?: Prisma.StringFilter<"NewsArticle"> | string
  excerpt?: Prisma.StringNullableFilter<"NewsArticle"> | string | null
  imageUrl?: Prisma.StringNullableFilter<"NewsArticle"> | string | null
  authorName?: Prisma.StringFilter<"NewsArticle"> | string
  category?: Prisma.EnumNewsCategoryFilter<"NewsArticle"> | $Enums.NewsCategory
  isPublished?: Prisma.BoolFilter<"NewsArticle"> | boolean
  publishedAt?: Prisma.DateTimeNullableFilter<"NewsArticle"> | Date | string | null
  organizationId?: Prisma.StringNullableFilter<"NewsArticle"> | string | null
  createdAt?: Prisma.DateTimeFilter<"NewsArticle"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"NewsArticle"> | Date | string
}

export type NewsArticleCreateManyOrganizationInput = {
  id?: string
  title: string
  content: string
  excerpt?: string | null
  imageUrl?: string | null
  authorName: string
  category?: $Enums.NewsCategory
  isPublished?: boolean
  publishedAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type NewsArticleUpdateWithoutOrganizationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  content?: Prisma.StringFieldUpdateOperationsInput | string
  excerpt?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  imageUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  authorName?: Prisma.StringFieldUpdateOperationsInput | string
  category?: Prisma.EnumNewsCategoryFieldUpdateOperationsInput | $Enums.NewsCategory
  isPublished?: Prisma.BoolFieldUpdateOperationsInput | boolean
  publishedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type NewsArticleUncheckedUpdateWithoutOrganizationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  content?: Prisma.StringFieldUpdateOperationsInput | string
  excerpt?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  imageUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  authorName?: Prisma.StringFieldUpdateOperationsInput | string
  category?: Prisma.EnumNewsCategoryFieldUpdateOperationsInput | $Enums.NewsCategory
  isPublished?: Prisma.BoolFieldUpdateOperationsInput | boolean
  publishedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type NewsArticleUncheckedUpdateManyWithoutOrganizationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  content?: Prisma.StringFieldUpdateOperationsInput | string
  excerpt?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  imageUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  authorName?: Prisma.StringFieldUpdateOperationsInput | string
  category?: Prisma.EnumNewsCategoryFieldUpdateOperationsInput | $Enums.NewsCategory
  isPublished?: Prisma.BoolFieldUpdateOperationsInput | boolean
  publishedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}



export type NewsArticleSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  title?: boolean
  content?: boolean
  excerpt?: boolean
  imageUrl?: boolean
  authorName?: boolean
  category?: boolean
  isPublished?: boolean
  publishedAt?: boolean
  organizationId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  organization?: boolean | Prisma.NewsArticle$organizationArgs<ExtArgs>
}, ExtArgs["result"]["newsArticle"]>



export type NewsArticleSelectScalar = {
  id?: boolean
  title?: boolean
  content?: boolean
  excerpt?: boolean
  imageUrl?: boolean
  authorName?: boolean
  category?: boolean
  isPublished?: boolean
  publishedAt?: boolean
  organizationId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type NewsArticleOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "title" | "content" | "excerpt" | "imageUrl" | "authorName" | "category" | "isPublished" | "publishedAt" | "organizationId" | "createdAt" | "updatedAt", ExtArgs["result"]["newsArticle"]>
export type NewsArticleInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  organization?: boolean | Prisma.NewsArticle$organizationArgs<ExtArgs>
}

export type $NewsArticlePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "NewsArticle"
  objects: {
    organization: Prisma.$OrganizationPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    title: string
    content: string
    excerpt: string | null
    imageUrl: string | null
    authorName: string
    category: $Enums.NewsCategory
    isPublished: boolean
    publishedAt: Date | null
    organizationId: string | null
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["newsArticle"]>
  composites: {}
}

export type NewsArticleGetPayload<S extends boolean | null | undefined | NewsArticleDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$NewsArticlePayload, S>

export type NewsArticleCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<NewsArticleFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: NewsArticleCountAggregateInputType | true
  }

export interface NewsArticleDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['NewsArticle'], meta: { name: 'NewsArticle' } }
  /**
   * Find zero or one NewsArticle that matches the filter.
   * @param {NewsArticleFindUniqueArgs} args - Arguments to find a NewsArticle
   * @example
   * // Get one NewsArticle
   * const newsArticle = await prisma.newsArticle.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends NewsArticleFindUniqueArgs>(args: Prisma.SelectSubset<T, NewsArticleFindUniqueArgs<ExtArgs>>): Prisma.Prisma__NewsArticleClient<runtime.Types.Result.GetResult<Prisma.$NewsArticlePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one NewsArticle that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {NewsArticleFindUniqueOrThrowArgs} args - Arguments to find a NewsArticle
   * @example
   * // Get one NewsArticle
   * const newsArticle = await prisma.newsArticle.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends NewsArticleFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, NewsArticleFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__NewsArticleClient<runtime.Types.Result.GetResult<Prisma.$NewsArticlePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first NewsArticle that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {NewsArticleFindFirstArgs} args - Arguments to find a NewsArticle
   * @example
   * // Get one NewsArticle
   * const newsArticle = await prisma.newsArticle.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends NewsArticleFindFirstArgs>(args?: Prisma.SelectSubset<T, NewsArticleFindFirstArgs<ExtArgs>>): Prisma.Prisma__NewsArticleClient<runtime.Types.Result.GetResult<Prisma.$NewsArticlePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first NewsArticle that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {NewsArticleFindFirstOrThrowArgs} args - Arguments to find a NewsArticle
   * @example
   * // Get one NewsArticle
   * const newsArticle = await prisma.newsArticle.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends NewsArticleFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, NewsArticleFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__NewsArticleClient<runtime.Types.Result.GetResult<Prisma.$NewsArticlePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more NewsArticles that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {NewsArticleFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all NewsArticles
   * const newsArticles = await prisma.newsArticle.findMany()
   * 
   * // Get first 10 NewsArticles
   * const newsArticles = await prisma.newsArticle.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const newsArticleWithIdOnly = await prisma.newsArticle.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends NewsArticleFindManyArgs>(args?: Prisma.SelectSubset<T, NewsArticleFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$NewsArticlePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a NewsArticle.
   * @param {NewsArticleCreateArgs} args - Arguments to create a NewsArticle.
   * @example
   * // Create one NewsArticle
   * const NewsArticle = await prisma.newsArticle.create({
   *   data: {
   *     // ... data to create a NewsArticle
   *   }
   * })
   * 
   */
  create<T extends NewsArticleCreateArgs>(args: Prisma.SelectSubset<T, NewsArticleCreateArgs<ExtArgs>>): Prisma.Prisma__NewsArticleClient<runtime.Types.Result.GetResult<Prisma.$NewsArticlePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many NewsArticles.
   * @param {NewsArticleCreateManyArgs} args - Arguments to create many NewsArticles.
   * @example
   * // Create many NewsArticles
   * const newsArticle = await prisma.newsArticle.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends NewsArticleCreateManyArgs>(args?: Prisma.SelectSubset<T, NewsArticleCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a NewsArticle.
   * @param {NewsArticleDeleteArgs} args - Arguments to delete one NewsArticle.
   * @example
   * // Delete one NewsArticle
   * const NewsArticle = await prisma.newsArticle.delete({
   *   where: {
   *     // ... filter to delete one NewsArticle
   *   }
   * })
   * 
   */
  delete<T extends NewsArticleDeleteArgs>(args: Prisma.SelectSubset<T, NewsArticleDeleteArgs<ExtArgs>>): Prisma.Prisma__NewsArticleClient<runtime.Types.Result.GetResult<Prisma.$NewsArticlePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one NewsArticle.
   * @param {NewsArticleUpdateArgs} args - Arguments to update one NewsArticle.
   * @example
   * // Update one NewsArticle
   * const newsArticle = await prisma.newsArticle.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends NewsArticleUpdateArgs>(args: Prisma.SelectSubset<T, NewsArticleUpdateArgs<ExtArgs>>): Prisma.Prisma__NewsArticleClient<runtime.Types.Result.GetResult<Prisma.$NewsArticlePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more NewsArticles.
   * @param {NewsArticleDeleteManyArgs} args - Arguments to filter NewsArticles to delete.
   * @example
   * // Delete a few NewsArticles
   * const { count } = await prisma.newsArticle.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends NewsArticleDeleteManyArgs>(args?: Prisma.SelectSubset<T, NewsArticleDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more NewsArticles.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {NewsArticleUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many NewsArticles
   * const newsArticle = await prisma.newsArticle.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends NewsArticleUpdateManyArgs>(args: Prisma.SelectSubset<T, NewsArticleUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one NewsArticle.
   * @param {NewsArticleUpsertArgs} args - Arguments to update or create a NewsArticle.
   * @example
   * // Update or create a NewsArticle
   * const newsArticle = await prisma.newsArticle.upsert({
   *   create: {
   *     // ... data to create a NewsArticle
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the NewsArticle we want to update
   *   }
   * })
   */
  upsert<T extends NewsArticleUpsertArgs>(args: Prisma.SelectSubset<T, NewsArticleUpsertArgs<ExtArgs>>): Prisma.Prisma__NewsArticleClient<runtime.Types.Result.GetResult<Prisma.$NewsArticlePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of NewsArticles.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {NewsArticleCountArgs} args - Arguments to filter NewsArticles to count.
   * @example
   * // Count the number of NewsArticles
   * const count = await prisma.newsArticle.count({
   *   where: {
   *     // ... the filter for the NewsArticles we want to count
   *   }
   * })
  **/
  count<T extends NewsArticleCountArgs>(
    args?: Prisma.Subset<T, NewsArticleCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], NewsArticleCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a NewsArticle.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {NewsArticleAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends NewsArticleAggregateArgs>(args: Prisma.Subset<T, NewsArticleAggregateArgs>): Prisma.PrismaPromise<GetNewsArticleAggregateType<T>>

  /**
   * Group by NewsArticle.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {NewsArticleGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends NewsArticleGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: NewsArticleGroupByArgs['orderBy'] }
      : { orderBy?: NewsArticleGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, NewsArticleGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetNewsArticleGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the NewsArticle model
 */
readonly fields: NewsArticleFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for NewsArticle.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__NewsArticleClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  organization<T extends Prisma.NewsArticle$organizationArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.NewsArticle$organizationArgs<ExtArgs>>): Prisma.Prisma__OrganizationClient<runtime.Types.Result.GetResult<Prisma.$OrganizationPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the NewsArticle model
 */
export interface NewsArticleFieldRefs {
  readonly id: Prisma.FieldRef<"NewsArticle", 'String'>
  readonly title: Prisma.FieldRef<"NewsArticle", 'String'>
  readonly content: Prisma.FieldRef<"NewsArticle", 'String'>
  readonly excerpt: Prisma.FieldRef<"NewsArticle", 'String'>
  readonly imageUrl: Prisma.FieldRef<"NewsArticle", 'String'>
  readonly authorName: Prisma.FieldRef<"NewsArticle", 'String'>
  readonly category: Prisma.FieldRef<"NewsArticle", 'NewsCategory'>
  readonly isPublished: Prisma.FieldRef<"NewsArticle", 'Boolean'>
  readonly publishedAt: Prisma.FieldRef<"NewsArticle", 'DateTime'>
  readonly organizationId: Prisma.FieldRef<"NewsArticle", 'String'>
  readonly createdAt: Prisma.FieldRef<"NewsArticle", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"NewsArticle", 'DateTime'>
}
    

// Custom InputTypes
/**
 * NewsArticle findUnique
 */
export type NewsArticleFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the NewsArticle
   */
  select?: Prisma.NewsArticleSelect<ExtArgs> | null
  /**
   * Omit specific fields from the NewsArticle
   */
  omit?: Prisma.NewsArticleOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.NewsArticleInclude<ExtArgs> | null
  /**
   * Filter, which NewsArticle to fetch.
   */
  where: Prisma.NewsArticleWhereUniqueInput
}

/**
 * NewsArticle findUniqueOrThrow
 */
export type NewsArticleFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the NewsArticle
   */
  select?: Prisma.NewsArticleSelect<ExtArgs> | null
  /**
   * Omit specific fields from the NewsArticle
   */
  omit?: Prisma.NewsArticleOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.NewsArticleInclude<ExtArgs> | null
  /**
   * Filter, which NewsArticle to fetch.
   */
  where: Prisma.NewsArticleWhereUniqueInput
}

/**
 * NewsArticle findFirst
 */
export type NewsArticleFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the NewsArticle
   */
  select?: Prisma.NewsArticleSelect<ExtArgs> | null
  /**
   * Omit specific fields from the NewsArticle
   */
  omit?: Prisma.NewsArticleOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.NewsArticleInclude<ExtArgs> | null
  /**
   * Filter, which NewsArticle to fetch.
   */
  where?: Prisma.NewsArticleWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of NewsArticles to fetch.
   */
  orderBy?: Prisma.NewsArticleOrderByWithRelationInput | Prisma.NewsArticleOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for NewsArticles.
   */
  cursor?: Prisma.NewsArticleWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` NewsArticles from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` NewsArticles.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of NewsArticles.
   */
  distinct?: Prisma.NewsArticleScalarFieldEnum | Prisma.NewsArticleScalarFieldEnum[]
}

/**
 * NewsArticle findFirstOrThrow
 */
export type NewsArticleFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the NewsArticle
   */
  select?: Prisma.NewsArticleSelect<ExtArgs> | null
  /**
   * Omit specific fields from the NewsArticle
   */
  omit?: Prisma.NewsArticleOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.NewsArticleInclude<ExtArgs> | null
  /**
   * Filter, which NewsArticle to fetch.
   */
  where?: Prisma.NewsArticleWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of NewsArticles to fetch.
   */
  orderBy?: Prisma.NewsArticleOrderByWithRelationInput | Prisma.NewsArticleOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for NewsArticles.
   */
  cursor?: Prisma.NewsArticleWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` NewsArticles from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` NewsArticles.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of NewsArticles.
   */
  distinct?: Prisma.NewsArticleScalarFieldEnum | Prisma.NewsArticleScalarFieldEnum[]
}

/**
 * NewsArticle findMany
 */
export type NewsArticleFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the NewsArticle
   */
  select?: Prisma.NewsArticleSelect<ExtArgs> | null
  /**
   * Omit specific fields from the NewsArticle
   */
  omit?: Prisma.NewsArticleOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.NewsArticleInclude<ExtArgs> | null
  /**
   * Filter, which NewsArticles to fetch.
   */
  where?: Prisma.NewsArticleWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of NewsArticles to fetch.
   */
  orderBy?: Prisma.NewsArticleOrderByWithRelationInput | Prisma.NewsArticleOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing NewsArticles.
   */
  cursor?: Prisma.NewsArticleWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` NewsArticles from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` NewsArticles.
   */
  skip?: number
  distinct?: Prisma.NewsArticleScalarFieldEnum | Prisma.NewsArticleScalarFieldEnum[]
}

/**
 * NewsArticle create
 */
export type NewsArticleCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the NewsArticle
   */
  select?: Prisma.NewsArticleSelect<ExtArgs> | null
  /**
   * Omit specific fields from the NewsArticle
   */
  omit?: Prisma.NewsArticleOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.NewsArticleInclude<ExtArgs> | null
  /**
   * The data needed to create a NewsArticle.
   */
  data: Prisma.XOR<Prisma.NewsArticleCreateInput, Prisma.NewsArticleUncheckedCreateInput>
}

/**
 * NewsArticle createMany
 */
export type NewsArticleCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many NewsArticles.
   */
  data: Prisma.NewsArticleCreateManyInput | Prisma.NewsArticleCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * NewsArticle update
 */
export type NewsArticleUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the NewsArticle
   */
  select?: Prisma.NewsArticleSelect<ExtArgs> | null
  /**
   * Omit specific fields from the NewsArticle
   */
  omit?: Prisma.NewsArticleOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.NewsArticleInclude<ExtArgs> | null
  /**
   * The data needed to update a NewsArticle.
   */
  data: Prisma.XOR<Prisma.NewsArticleUpdateInput, Prisma.NewsArticleUncheckedUpdateInput>
  /**
   * Choose, which NewsArticle to update.
   */
  where: Prisma.NewsArticleWhereUniqueInput
}

/**
 * NewsArticle updateMany
 */
export type NewsArticleUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update NewsArticles.
   */
  data: Prisma.XOR<Prisma.NewsArticleUpdateManyMutationInput, Prisma.NewsArticleUncheckedUpdateManyInput>
  /**
   * Filter which NewsArticles to update
   */
  where?: Prisma.NewsArticleWhereInput
  /**
   * Limit how many NewsArticles to update.
   */
  limit?: number
}

/**
 * NewsArticle upsert
 */
export type NewsArticleUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the NewsArticle
   */
  select?: Prisma.NewsArticleSelect<ExtArgs> | null
  /**
   * Omit specific fields from the NewsArticle
   */
  omit?: Prisma.NewsArticleOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.NewsArticleInclude<ExtArgs> | null
  /**
   * The filter to search for the NewsArticle to update in case it exists.
   */
  where: Prisma.NewsArticleWhereUniqueInput
  /**
   * In case the NewsArticle found by the `where` argument doesn't exist, create a new NewsArticle with this data.
   */
  create: Prisma.XOR<Prisma.NewsArticleCreateInput, Prisma.NewsArticleUncheckedCreateInput>
  /**
   * In case the NewsArticle was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.NewsArticleUpdateInput, Prisma.NewsArticleUncheckedUpdateInput>
}

/**
 * NewsArticle delete
 */
export type NewsArticleDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the NewsArticle
   */
  select?: Prisma.NewsArticleSelect<ExtArgs> | null
  /**
   * Omit specific fields from the NewsArticle
   */
  omit?: Prisma.NewsArticleOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.NewsArticleInclude<ExtArgs> | null
  /**
   * Filter which NewsArticle to delete.
   */
  where: Prisma.NewsArticleWhereUniqueInput
}

/**
 * NewsArticle deleteMany
 */
export type NewsArticleDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which NewsArticles to delete
   */
  where?: Prisma.NewsArticleWhereInput
  /**
   * Limit how many NewsArticles to delete.
   */
  limit?: number
}

/**
 * NewsArticle.organization
 */
export type NewsArticle$organizationArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Organization
   */
  select?: Prisma.OrganizationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Organization
   */
  omit?: Prisma.OrganizationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.OrganizationInclude<ExtArgs> | null
  where?: Prisma.OrganizationWhereInput
}

/**
 * NewsArticle without action
 */
export type NewsArticleDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the NewsArticle
   */
  select?: Prisma.NewsArticleSelect<ExtArgs> | null
  /**
   * Omit specific fields from the NewsArticle
   */
  omit?: Prisma.NewsArticleOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.NewsArticleInclude<ExtArgs> | null
}
