
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file should be your main import to use Prisma. Through it you get access to all the models, enums, and input types.
 *
 * 🟢 You can import this file directly.
 */

import * as process from 'node:process'
import * as path from 'node:path'
import { fileURLToPath } from 'node:url'
const __dirname = path.dirname(fileURLToPath(import.meta.url))

import * as runtime from "@prisma/client/runtime/library"
import * as $Enums from "./enums"
import * as $Class from "./internal/class"
import * as Prisma from "./internal/prismaNamespace"

export * as $Enums from './enums'
/**
 * ## Prisma Client
 * 
 * Type-safe database client for TypeScript
 * @example
 * ```
 * const prisma = new PrismaClient()
 * // Fetch zero or more Users
 * const users = await prisma.user.findMany()
 * ```
 * 
 * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
 */
export const PrismaClient = $Class.getPrismaClientClass(__dirname)
export type PrismaClient<ClientOptions extends Prisma.PrismaClientOptions = Prisma.PrismaClientOptions, Log = $Class.LogOptions<ClientOptions>, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = $Class.PrismaClient<ClientOptions, Log, ExtArgs>
export { Prisma }


// file annotations for bundling tools to include these files
path.join(__dirname, "query_engine-windows.dll.node")
path.join(process.cwd(), "prisma/generated/query_engine-windows.dll.node")

/**
 * Model User
 * 
 */
export type User = Prisma.UserModel
/**
 * Model Session
 * 
 */
export type Session = Prisma.SessionModel
/**
 * Model Account
 * 
 */
export type Account = Prisma.AccountModel
/**
 * Model Verification
 * 
 */
export type Verification = Prisma.VerificationModel
/**
 * Model Organization
 * 
 */
export type Organization = Prisma.OrganizationModel
/**
 * Model Member
 * 
 */
export type Member = Prisma.MemberModel
/**
 * Model Invitation
 * 
 */
export type Invitation = Prisma.InvitationModel
/**
 * Model TwoFactor
 * 
 */
export type TwoFactor = Prisma.TwoFactorModel
/**
 * Model Passkey
 * 
 */
export type Passkey = Prisma.PasskeyModel
/**
 * Model AlumniProfile
 * 
 */
export type AlumniProfile = Prisma.AlumniProfileModel
/**
 * Model Connection
 * 
 */
export type Connection = Prisma.ConnectionModel
/**
 * Model Message
 * 
 */
export type Message = Prisma.MessageModel
/**
 * Model Post
 * 
 */
export type Post = Prisma.PostModel
/**
 * Model PostLike
 * 
 */
export type PostLike = Prisma.PostLikeModel
/**
 * Model Comment
 * 
 */
export type Comment = Prisma.CommentModel
/**
 * Model Event
 * 
 */
export type Event = Prisma.EventModel
/**
 * Model EventRegistration
 * 
 */
export type EventRegistration = Prisma.EventRegistrationModel
/**
 * Model Donation
 * 
 */
export type Donation = Prisma.DonationModel
/**
 * Model NewsArticle
 * 
 */
export type NewsArticle = Prisma.NewsArticleModel
/**
 * Model Notification
 * 
 */
export type Notification = Prisma.NotificationModel

export type ProfileVisibility = $Enums.ProfileVisibility
export const ProfileVisibility = $Enums.ProfileVisibility

export type ConnectionStatus = $Enums.ConnectionStatus
export const ConnectionStatus = $Enums.ConnectionStatus

export type PostType = $Enums.PostType
export const PostType = $Enums.PostType

export type EventType = $Enums.EventType
export const EventType = $Enums.EventType

export type RegistrationStatus = $Enums.RegistrationStatus
export const RegistrationStatus = $Enums.RegistrationStatus

export type DonationType = $Enums.DonationType
export const DonationType = $Enums.DonationType

export type PaymentStatus = $Enums.PaymentStatus
export const PaymentStatus = $Enums.PaymentStatus

export type NewsCategory = $Enums.NewsCategory
export const NewsCategory = $Enums.NewsCategory

export type NotificationType = $Enums.NotificationType
export const NotificationType = $Enums.NotificationType
