"use client"

import { useQuery } from "@tanstack/react-query"
import { orpc } from "@/utils/orpc"

export default function AdminAnalytics() {
  const stats = useQuery(orpc.donations.admin.getAnalytics.queryOptions())

  return (
    <div className="rounded-lg border bg-card p-4">
      <div className="text-sm font-medium text-muted-foreground">Admin: Donation Analytics</div>
      <div className="mt-2 grid gap-4 md:grid-cols-3">
        <div>
          <div className="text-2xl font-semibold">{(stats.data as any)?.total ?? 0}</div>
          <div className="text-sm text-muted-foreground">Total Donations</div>
        </div>
        <div>
          <div className="text-2xl font-semibold">{(stats.data as any)?.totalCompletedAmount ?? 0}</div>
          <div className="text-sm text-muted-foreground">Sum Completed (amount)</div>
        </div>
        <div>
          <div className="text-2xl font-semibold">{(stats.data as any)?.recurring ?? 0}</div>
          <div className="text-sm text-muted-foreground">Recurring Count</div>
        </div>
      </div>
    </div>
  )
}


