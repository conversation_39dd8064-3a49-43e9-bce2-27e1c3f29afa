"use client"

import { useParams } from "next/navigation"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { orpc, client } from "@/utils/orpc"
import { Button } from "@/components/ui/button"

export default function NewsDetail() {
  const params = useParams()
  const id = params?.id as string
  const queryClient = useQueryClient()

  const article = useQuery(orpc.news.getById.queryOptions({ input: { id } }))

  const deleteMutation = useMutation({
    mutationFn: async () => client.news.admin.delete({ id }),
    onSuccess: async () => queryClient.invalidateQueries(),
  })

  if (article.isLoading) return <div className="text-sm text-muted-foreground">Loading...</div>
  if (article.isError) return <div className="text-sm text-destructive">Failed to load article.</div>
  const a = article.data as any

  return (
    <div className="space-y-4">
      <div className="rounded-lg border bg-card p-4">
        <div className="flex items-center justify-between">
          <div className="text-lg font-semibold">{a.title}</div>
          <div className="text-xs text-muted-foreground">{new Date(a.createdAt).toLocaleDateString()}</div>
        </div>
        <div className="text-sm text-muted-foreground">{a.authorName} • {a.category}</div>
        {a.imageUrl && (
          <div className="mt-3">
            <img src={a.imageUrl} alt="" className="w-full rounded-md" />
          </div>
        )}
        <div className="mt-3 whitespace-pre-wrap text-sm">{a.content}</div>
        <div className="mt-3 flex items-center gap-2">
          <Button size="sm" variant="destructive" onClick={() => deleteMutation.mutate()} disabled={deleteMutation.isPending}>Delete</Button>
        </div>
      </div>
    </div>
  )
}


