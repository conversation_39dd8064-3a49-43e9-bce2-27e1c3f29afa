"use client";
import React, { useTransition } from "react";
import <PERSON><PERSON><PERSON><PERSON> from "./card-wrapper";
import { useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../ui/form";
import { z } from "zod";

import { zodResolver } from "@hookform/resolvers/zod";
import { Input } from "../ui/input";
import { Button } from "../ui/button";
import { toast } from "react-toastify";
import { authClient } from "@/lib/auth-client";
import { useSearchParams } from "next/navigation";

const emailSchema = z.object({
  email: z.string().email(),
});

const ResetPasswordForm = () => {
  //
  const [isPending, startTransition] = useTransition();
  const params = useSearchParams();
  const redirectURL = params.get("redirectUrl") || "/dashboard";

  const form = useForm<z.infer<typeof emailSchema>>({
    resolver: zodResolver(emailSchema),
    defaultValues: {
      email: "",
    },
  });

  const onSubmit = async (values: z.infer<typeof emailSchema>) => {
    startTransition(async () => {
      const { error } = await authClient.forgetPassword({
        email: values.email,
        redirectTo: `/auth/new-password?redirectUrl=${redirectURL}`,
      });

      if (error) {
        toast.error(error.message);
      } else {
        toast.success("Check your email for password reset link");
      }
    });
  };

  return (
    <CardWrapper
      backButtonHref={`/auth/sign-in?redirectUrl=${redirectURL}`}
      backButtonLabel="Sign in instead"
      headerLabel="Reset Password"
    >
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="space-y-4">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email address</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      disabled={isPending}
                      placeholder="<EMAIL>"
                      type="email"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <Button disabled={isPending} className="w-full" type="submit">
            {isPending ? "Sending email..." : "Reset Password"}
          </Button>
        </form>
      </Form>
    </CardWrapper>
  );
};

export default ResetPasswordForm;
