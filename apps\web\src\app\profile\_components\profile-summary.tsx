"use client"

import { useQuery } from "@tanstack/react-query"
import { orpc } from "@/utils/orpc"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { UserRound, Briefcase, MapPin, GraduationCap, Landmark } from "lucide-react"

export function ProfileSummary() {
  const me = useQuery(orpc.profiles.getMe.queryOptions())
  const stats = useQuery(orpc.profiles.getStats.queryOptions(), { enabled: !!me.data })

  if (me.isLoading) {
    return <div className="text-sm text-muted-foreground">Loading profile...</div>
  }

  if (me.isError) {
    return <div className="text-sm text-destructive">Failed to load profile.</div>
  }

  const profile = me.data!

  const initials = `${profile.firstName?.[0] ?? ""}${profile.lastName?.[0] ?? ""}`.toUpperCase()

  return (
    <div className="space-y-6">
      <div className="flex items-start justify-between">
        <div className="flex items-center gap-3">
          <Avatar className="h-12 w-12">
            <AvatarImage src={profile.profilePicture ?? undefined} alt={profile.displayName ?? profile.firstName} />
            <AvatarFallback>{initials || "AL"}</AvatarFallback>
          </Avatar>
          <div>
            <div className="text-lg font-semibold">{profile.displayName || `${profile.firstName} ${profile.lastName}`}</div>
            <div className="text-sm text-muted-foreground">{profile.bio || ""}</div>
          </div>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-3">
        <div className="rounded-lg border bg-card p-4">
          <div className="flex items-center gap-2 text-sm text-muted-foreground"><Briefcase className="size-4" /> Career</div>
          <div className="mt-2 text-sm">
            <div>{profile.currentPosition || "—"}</div>
            <div className="text-muted-foreground">{profile.currentCompany || "—"}</div>
          </div>
        </div>
        <div className="rounded-lg border bg-card p-4">
          <div className="flex items-center gap-2 text-sm text-muted-foreground"><MapPin className="size-4" /> Location</div>
          <div className="mt-2 text-sm">{profile.location || "—"}</div>
        </div>
        <div className="rounded-lg border bg-card p-4">
          <div className="flex items-center gap-2 text-sm text-muted-foreground"><GraduationCap className="size-4" /> PROTEC</div>
          <div className="mt-2 text-sm">
            <div>Graduation: {profile.graduationYear}</div>
            <div className="text-muted-foreground">{profile.programType} • {profile.centerLocation}</div>
          </div>
        </div>
      </div>

      <div className="rounded-lg border bg-card p-4">
        <div className="flex items-center gap-2 text-sm text-muted-foreground"><UserRound className="size-4" /> Mentorship</div>
        <div className="mt-2 flex flex-wrap gap-2">
          {profile.mentorshipOffered && <Badge variant="secondary">Offers mentorship</Badge>}
          {profile.mentorshipSought && <Badge variant="secondary">Seeks mentorship</Badge>}
        </div>
        <div className="mt-2 grid gap-2 md:grid-cols-2">
          <div>
            <div className="text-sm font-medium">Skills offered</div>
            <div className="text-sm text-muted-foreground whitespace-pre-wrap">{profile.skillsOffered || "—"}</div>
          </div>
          <div>
            <div className="text-sm font-medium">Skills wanted</div>
            <div className="text-sm text-muted-foreground whitespace-pre-wrap">{profile.skillsWanted || "—"}</div>
          </div>
        </div>
      </div>

      <div className="rounded-lg border bg-card p-4">
        <div className="text-sm text-muted-foreground">Stats</div>
        <div className="mt-2 grid gap-4 md:grid-cols-3">
          <div>
            <div className="text-2xl font-semibold">{stats.data?.posts ?? 0}</div>
            <div className="text-sm text-muted-foreground">Posts</div>
          </div>
          <div>
            <div className="text-2xl font-semibold">{stats.data?.connections ?? 0}</div>
            <div className="text-sm text-muted-foreground">Connections</div>
          </div>
          <div>
            <div className="text-2xl font-semibold">{stats.data?.donations ?? 0}</div>
            <div className="text-sm text-muted-foreground">Donations</div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ProfileSummary


