"use client"

import { use<PERSON>emo, useState } from "react"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { orpc, client } from "@/utils/orpc"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Pagination, PaginationContent, PaginationItem, PaginationNext, PaginationPrevious } from "@/components/ui/pagination"

function ConnectionRow({ c, selfId }: { c: any; selfId: string }) {
  const other = c.requesterId === selfId ? c.requested : c.requester
  const initials = `${other?.firstName?.[0] ?? ""}${other?.lastName?.[0] ?? ""}`.toUpperCase()
  const queryClient = useQueryClient()

  const accept = useMutation({
    mutationFn: (id: string) => client.connections.accept({ connectionId: id }),
    onSuccess: () => queryClient.invalidateQueries(),
  })
  const decline = useMutation({
    mutationFn: (id: string) => client.connections.decline({ connectionId: id }),
    onSuccess: () => queryClient.invalidateQueries(),
  })
  const remove = useMutation({
    mutationFn: (id: string) => client.connections.remove({ connectionId: id }),
    onSuccess: () => queryClient.invalidateQueries(),
  })
  const block = useMutation({
    mutationFn: (id: string) => client.connections.block({ connectionId: id }),
    onSuccess: () => queryClient.invalidateQueries(),
  })

  return (
    <div className="rounded-lg border bg-card p-4 flex items-center justify-between">
      <div className="flex items-center gap-3">
        <Avatar className="h-10 w-10">
          <AvatarImage src={other?.profilePicture ?? undefined} alt={other?.displayName ?? other?.firstName} />
          <AvatarFallback>{initials || "AL"}</AvatarFallback>
        </Avatar>
        <div>
          <div className="font-medium">{other?.displayName || `${other?.firstName ?? ""} ${other?.lastName ?? ""}`}</div>
          <div className="text-sm text-muted-foreground">{other?.currentPosition || "—"}{other?.currentCompany ? ` • ${other.currentCompany}` : ""}</div>
        </div>
      </div>
      <div className="flex items-center gap-2">
        {c.status === "PENDING" && c.requestedId === selfId && (
          <>
            <Button size="sm" onClick={() => accept.mutate(c.id)} disabled={accept.isPending}>Accept</Button>
            <Button size="sm" variant="outline" onClick={() => decline.mutate(c.id)} disabled={decline.isPending}>Decline</Button>
            <Button size="sm" variant="destructive" onClick={() => block.mutate(c.id)} disabled={block.isPending}>Block</Button>
          </>
        )}
        {c.status === "ACCEPTED" && (
          <div className="flex items-center gap-2">
            <Button size="sm" variant="outline" onClick={() => remove.mutate(c.id)} disabled={remove.isPending}>Remove</Button>
            <Button size="sm" variant="destructive" onClick={() => block.mutate(c.id)} disabled={block.isPending}>Block</Button>
          </div>
        )}
        {c.status === "PENDING" && c.requesterId === selfId && (
          <div className="text-xs text-muted-foreground">Pending</div>
        )}
        {c.status === "BLOCKED" && (
          <Button size="sm" variant="outline" onClick={() => remove.mutate(c.id)} disabled={remove.isPending}>Remove</Button>
        )}
      </div>
    </div>
  )
}

export default function ConnectionsPage() {
  const [tab, setTab] = useState("pending")
  const [page, setPage] = useState(1)

  const me = useQuery(orpc.profiles.getMe.queryOptions())
  const status: "PENDING" | "ACCEPTED" | "DECLINED" | "BLOCKED" =
    tab === "accepted" ? "ACCEPTED" : tab === "blocked" ? "BLOCKED" : "PENDING"
  const list = useQuery(
    orpc.connections.list.queryOptions({ input: { status, page, limit: 20 } })
  )

  const totalPages = useMemo(() => {
    const total = (list.data as any)?.total ?? 0
    const limit = (list.data as any)?.limit ?? 20
    return Math.max(1, Math.ceil(total / limit))
  }, [list.data])

  const myProfileId = (me.data as any)?.id

  return (
    <div className="container mx-auto px-4 py-4 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold">Connections</h1>
        <div className="flex items-center gap-2"></div>
      </div>

      <Tabs value={tab} onValueChange={(v) => { setTab(v); setPage(1) }}>
        <TabsList>
          <TabsTrigger value="pending">Pending</TabsTrigger>
          <TabsTrigger value="accepted">Accepted</TabsTrigger>
          <TabsTrigger value="blocked">Blocked</TabsTrigger>
        </TabsList>
        <TabsContent value="pending" className="grid gap-3">
          {list.isLoading && <div className="text-sm text-muted-foreground">Loading...</div>}
          {list.data?.data?.map((c: any) => (
            <ConnectionRow key={c.id} c={c} selfId={myProfileId} />
          ))}
        </TabsContent>
        <TabsContent value="accepted" className="grid gap-3">
          {list.isLoading && <div className="text-sm text-muted-foreground">Loading...</div>}
          {list.data?.data?.map((c: any) => (
            <ConnectionRow key={c.id} c={c} selfId={myProfileId} />
          ))}
        </TabsContent>
        <TabsContent value="blocked" className="grid gap-3">
          {list.isLoading && <div className="text-sm text-muted-foreground">Loading...</div>}
          {list.data?.data?.map((c: any) => (
            <ConnectionRow key={c.id} c={c} selfId={myProfileId} />
          ))}
        </TabsContent>
      </Tabs>

      <div className="flex items-center justify-end">
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious href="#" onClick={(e) => { e.preventDefault(); setPage((p) => Math.max(1, p - 1)) }} />
            </PaginationItem>
            <PaginationItem>
              <span className="text-sm text-muted-foreground px-3 py-2">Page {page} of {totalPages}</span>
            </PaginationItem>
            <PaginationItem>
              <PaginationNext href="#" onClick={(e) => { e.preventDefault(); setPage((p) => Math.min(totalPages, p + 1)) }} />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </div>
    </div>
  )
}

