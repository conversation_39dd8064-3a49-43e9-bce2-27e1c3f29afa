
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `PostLike` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model PostLike
 * 
 */
export type PostLikeModel = runtime.Types.Result.DefaultSelection<Prisma.$PostLikePayload>

export type AggregatePostLike = {
  _count: PostLikeCountAggregateOutputType | null
  _min: PostLikeMinAggregateOutputType | null
  _max: PostLikeMaxAggregateOutputType | null
}

export type PostLikeMinAggregateOutputType = {
  id: string | null
  postId: string | null
  userId: string | null
  createdAt: Date | null
}

export type PostLikeMaxAggregateOutputType = {
  id: string | null
  postId: string | null
  userId: string | null
  createdAt: Date | null
}

export type PostLikeCountAggregateOutputType = {
  id: number
  postId: number
  userId: number
  createdAt: number
  _all: number
}


export type PostLikeMinAggregateInputType = {
  id?: true
  postId?: true
  userId?: true
  createdAt?: true
}

export type PostLikeMaxAggregateInputType = {
  id?: true
  postId?: true
  userId?: true
  createdAt?: true
}

export type PostLikeCountAggregateInputType = {
  id?: true
  postId?: true
  userId?: true
  createdAt?: true
  _all?: true
}

export type PostLikeAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which PostLike to aggregate.
   */
  where?: Prisma.PostLikeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of PostLikes to fetch.
   */
  orderBy?: Prisma.PostLikeOrderByWithRelationInput | Prisma.PostLikeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.PostLikeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` PostLikes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` PostLikes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned PostLikes
  **/
  _count?: true | PostLikeCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: PostLikeMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: PostLikeMaxAggregateInputType
}

export type GetPostLikeAggregateType<T extends PostLikeAggregateArgs> = {
      [P in keyof T & keyof AggregatePostLike]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregatePostLike[P]>
    : Prisma.GetScalarType<T[P], AggregatePostLike[P]>
}




export type PostLikeGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.PostLikeWhereInput
  orderBy?: Prisma.PostLikeOrderByWithAggregationInput | Prisma.PostLikeOrderByWithAggregationInput[]
  by: Prisma.PostLikeScalarFieldEnum[] | Prisma.PostLikeScalarFieldEnum
  having?: Prisma.PostLikeScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: PostLikeCountAggregateInputType | true
  _min?: PostLikeMinAggregateInputType
  _max?: PostLikeMaxAggregateInputType
}

export type PostLikeGroupByOutputType = {
  id: string
  postId: string
  userId: string
  createdAt: Date
  _count: PostLikeCountAggregateOutputType | null
  _min: PostLikeMinAggregateOutputType | null
  _max: PostLikeMaxAggregateOutputType | null
}

type GetPostLikeGroupByPayload<T extends PostLikeGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<PostLikeGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof PostLikeGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], PostLikeGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], PostLikeGroupByOutputType[P]>
      }
    >
  >



export type PostLikeWhereInput = {
  AND?: Prisma.PostLikeWhereInput | Prisma.PostLikeWhereInput[]
  OR?: Prisma.PostLikeWhereInput[]
  NOT?: Prisma.PostLikeWhereInput | Prisma.PostLikeWhereInput[]
  id?: Prisma.StringFilter<"PostLike"> | string
  postId?: Prisma.StringFilter<"PostLike"> | string
  userId?: Prisma.StringFilter<"PostLike"> | string
  createdAt?: Prisma.DateTimeFilter<"PostLike"> | Date | string
  post?: Prisma.XOR<Prisma.PostScalarRelationFilter, Prisma.PostWhereInput>
  user?: Prisma.XOR<Prisma.AlumniProfileScalarRelationFilter, Prisma.AlumniProfileWhereInput>
}

export type PostLikeOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  postId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  post?: Prisma.PostOrderByWithRelationInput
  user?: Prisma.AlumniProfileOrderByWithRelationInput
  _relevance?: Prisma.PostLikeOrderByRelevanceInput
}

export type PostLikeWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  postId_userId?: Prisma.PostLikePostIdUserIdCompoundUniqueInput
  AND?: Prisma.PostLikeWhereInput | Prisma.PostLikeWhereInput[]
  OR?: Prisma.PostLikeWhereInput[]
  NOT?: Prisma.PostLikeWhereInput | Prisma.PostLikeWhereInput[]
  postId?: Prisma.StringFilter<"PostLike"> | string
  userId?: Prisma.StringFilter<"PostLike"> | string
  createdAt?: Prisma.DateTimeFilter<"PostLike"> | Date | string
  post?: Prisma.XOR<Prisma.PostScalarRelationFilter, Prisma.PostWhereInput>
  user?: Prisma.XOR<Prisma.AlumniProfileScalarRelationFilter, Prisma.AlumniProfileWhereInput>
}, "id" | "postId_userId">

export type PostLikeOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  postId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  _count?: Prisma.PostLikeCountOrderByAggregateInput
  _max?: Prisma.PostLikeMaxOrderByAggregateInput
  _min?: Prisma.PostLikeMinOrderByAggregateInput
}

export type PostLikeScalarWhereWithAggregatesInput = {
  AND?: Prisma.PostLikeScalarWhereWithAggregatesInput | Prisma.PostLikeScalarWhereWithAggregatesInput[]
  OR?: Prisma.PostLikeScalarWhereWithAggregatesInput[]
  NOT?: Prisma.PostLikeScalarWhereWithAggregatesInput | Prisma.PostLikeScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"PostLike"> | string
  postId?: Prisma.StringWithAggregatesFilter<"PostLike"> | string
  userId?: Prisma.StringWithAggregatesFilter<"PostLike"> | string
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"PostLike"> | Date | string
}

export type PostLikeCreateInput = {
  id?: string
  createdAt?: Date | string
  post: Prisma.PostCreateNestedOneWithoutLikesInput
  user: Prisma.AlumniProfileCreateNestedOneWithoutPostLikesInput
}

export type PostLikeUncheckedCreateInput = {
  id?: string
  postId: string
  userId: string
  createdAt?: Date | string
}

export type PostLikeUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  post?: Prisma.PostUpdateOneRequiredWithoutLikesNestedInput
  user?: Prisma.AlumniProfileUpdateOneRequiredWithoutPostLikesNestedInput
}

export type PostLikeUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  postId?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type PostLikeCreateManyInput = {
  id?: string
  postId: string
  userId: string
  createdAt?: Date | string
}

export type PostLikeUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type PostLikeUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  postId?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type PostLikeListRelationFilter = {
  every?: Prisma.PostLikeWhereInput
  some?: Prisma.PostLikeWhereInput
  none?: Prisma.PostLikeWhereInput
}

export type PostLikeOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type PostLikeOrderByRelevanceInput = {
  fields: Prisma.PostLikeOrderByRelevanceFieldEnum | Prisma.PostLikeOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type PostLikePostIdUserIdCompoundUniqueInput = {
  postId: string
  userId: string
}

export type PostLikeCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  postId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
}

export type PostLikeMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  postId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
}

export type PostLikeMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  postId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
}

export type PostLikeCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.PostLikeCreateWithoutUserInput, Prisma.PostLikeUncheckedCreateWithoutUserInput> | Prisma.PostLikeCreateWithoutUserInput[] | Prisma.PostLikeUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.PostLikeCreateOrConnectWithoutUserInput | Prisma.PostLikeCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.PostLikeCreateManyUserInputEnvelope
  connect?: Prisma.PostLikeWhereUniqueInput | Prisma.PostLikeWhereUniqueInput[]
}

export type PostLikeUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.PostLikeCreateWithoutUserInput, Prisma.PostLikeUncheckedCreateWithoutUserInput> | Prisma.PostLikeCreateWithoutUserInput[] | Prisma.PostLikeUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.PostLikeCreateOrConnectWithoutUserInput | Prisma.PostLikeCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.PostLikeCreateManyUserInputEnvelope
  connect?: Prisma.PostLikeWhereUniqueInput | Prisma.PostLikeWhereUniqueInput[]
}

export type PostLikeUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.PostLikeCreateWithoutUserInput, Prisma.PostLikeUncheckedCreateWithoutUserInput> | Prisma.PostLikeCreateWithoutUserInput[] | Prisma.PostLikeUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.PostLikeCreateOrConnectWithoutUserInput | Prisma.PostLikeCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.PostLikeUpsertWithWhereUniqueWithoutUserInput | Prisma.PostLikeUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.PostLikeCreateManyUserInputEnvelope
  set?: Prisma.PostLikeWhereUniqueInput | Prisma.PostLikeWhereUniqueInput[]
  disconnect?: Prisma.PostLikeWhereUniqueInput | Prisma.PostLikeWhereUniqueInput[]
  delete?: Prisma.PostLikeWhereUniqueInput | Prisma.PostLikeWhereUniqueInput[]
  connect?: Prisma.PostLikeWhereUniqueInput | Prisma.PostLikeWhereUniqueInput[]
  update?: Prisma.PostLikeUpdateWithWhereUniqueWithoutUserInput | Prisma.PostLikeUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.PostLikeUpdateManyWithWhereWithoutUserInput | Prisma.PostLikeUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.PostLikeScalarWhereInput | Prisma.PostLikeScalarWhereInput[]
}

export type PostLikeUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.PostLikeCreateWithoutUserInput, Prisma.PostLikeUncheckedCreateWithoutUserInput> | Prisma.PostLikeCreateWithoutUserInput[] | Prisma.PostLikeUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.PostLikeCreateOrConnectWithoutUserInput | Prisma.PostLikeCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.PostLikeUpsertWithWhereUniqueWithoutUserInput | Prisma.PostLikeUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.PostLikeCreateManyUserInputEnvelope
  set?: Prisma.PostLikeWhereUniqueInput | Prisma.PostLikeWhereUniqueInput[]
  disconnect?: Prisma.PostLikeWhereUniqueInput | Prisma.PostLikeWhereUniqueInput[]
  delete?: Prisma.PostLikeWhereUniqueInput | Prisma.PostLikeWhereUniqueInput[]
  connect?: Prisma.PostLikeWhereUniqueInput | Prisma.PostLikeWhereUniqueInput[]
  update?: Prisma.PostLikeUpdateWithWhereUniqueWithoutUserInput | Prisma.PostLikeUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.PostLikeUpdateManyWithWhereWithoutUserInput | Prisma.PostLikeUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.PostLikeScalarWhereInput | Prisma.PostLikeScalarWhereInput[]
}

export type PostLikeCreateNestedManyWithoutPostInput = {
  create?: Prisma.XOR<Prisma.PostLikeCreateWithoutPostInput, Prisma.PostLikeUncheckedCreateWithoutPostInput> | Prisma.PostLikeCreateWithoutPostInput[] | Prisma.PostLikeUncheckedCreateWithoutPostInput[]
  connectOrCreate?: Prisma.PostLikeCreateOrConnectWithoutPostInput | Prisma.PostLikeCreateOrConnectWithoutPostInput[]
  createMany?: Prisma.PostLikeCreateManyPostInputEnvelope
  connect?: Prisma.PostLikeWhereUniqueInput | Prisma.PostLikeWhereUniqueInput[]
}

export type PostLikeUncheckedCreateNestedManyWithoutPostInput = {
  create?: Prisma.XOR<Prisma.PostLikeCreateWithoutPostInput, Prisma.PostLikeUncheckedCreateWithoutPostInput> | Prisma.PostLikeCreateWithoutPostInput[] | Prisma.PostLikeUncheckedCreateWithoutPostInput[]
  connectOrCreate?: Prisma.PostLikeCreateOrConnectWithoutPostInput | Prisma.PostLikeCreateOrConnectWithoutPostInput[]
  createMany?: Prisma.PostLikeCreateManyPostInputEnvelope
  connect?: Prisma.PostLikeWhereUniqueInput | Prisma.PostLikeWhereUniqueInput[]
}

export type PostLikeUpdateManyWithoutPostNestedInput = {
  create?: Prisma.XOR<Prisma.PostLikeCreateWithoutPostInput, Prisma.PostLikeUncheckedCreateWithoutPostInput> | Prisma.PostLikeCreateWithoutPostInput[] | Prisma.PostLikeUncheckedCreateWithoutPostInput[]
  connectOrCreate?: Prisma.PostLikeCreateOrConnectWithoutPostInput | Prisma.PostLikeCreateOrConnectWithoutPostInput[]
  upsert?: Prisma.PostLikeUpsertWithWhereUniqueWithoutPostInput | Prisma.PostLikeUpsertWithWhereUniqueWithoutPostInput[]
  createMany?: Prisma.PostLikeCreateManyPostInputEnvelope
  set?: Prisma.PostLikeWhereUniqueInput | Prisma.PostLikeWhereUniqueInput[]
  disconnect?: Prisma.PostLikeWhereUniqueInput | Prisma.PostLikeWhereUniqueInput[]
  delete?: Prisma.PostLikeWhereUniqueInput | Prisma.PostLikeWhereUniqueInput[]
  connect?: Prisma.PostLikeWhereUniqueInput | Prisma.PostLikeWhereUniqueInput[]
  update?: Prisma.PostLikeUpdateWithWhereUniqueWithoutPostInput | Prisma.PostLikeUpdateWithWhereUniqueWithoutPostInput[]
  updateMany?: Prisma.PostLikeUpdateManyWithWhereWithoutPostInput | Prisma.PostLikeUpdateManyWithWhereWithoutPostInput[]
  deleteMany?: Prisma.PostLikeScalarWhereInput | Prisma.PostLikeScalarWhereInput[]
}

export type PostLikeUncheckedUpdateManyWithoutPostNestedInput = {
  create?: Prisma.XOR<Prisma.PostLikeCreateWithoutPostInput, Prisma.PostLikeUncheckedCreateWithoutPostInput> | Prisma.PostLikeCreateWithoutPostInput[] | Prisma.PostLikeUncheckedCreateWithoutPostInput[]
  connectOrCreate?: Prisma.PostLikeCreateOrConnectWithoutPostInput | Prisma.PostLikeCreateOrConnectWithoutPostInput[]
  upsert?: Prisma.PostLikeUpsertWithWhereUniqueWithoutPostInput | Prisma.PostLikeUpsertWithWhereUniqueWithoutPostInput[]
  createMany?: Prisma.PostLikeCreateManyPostInputEnvelope
  set?: Prisma.PostLikeWhereUniqueInput | Prisma.PostLikeWhereUniqueInput[]
  disconnect?: Prisma.PostLikeWhereUniqueInput | Prisma.PostLikeWhereUniqueInput[]
  delete?: Prisma.PostLikeWhereUniqueInput | Prisma.PostLikeWhereUniqueInput[]
  connect?: Prisma.PostLikeWhereUniqueInput | Prisma.PostLikeWhereUniqueInput[]
  update?: Prisma.PostLikeUpdateWithWhereUniqueWithoutPostInput | Prisma.PostLikeUpdateWithWhereUniqueWithoutPostInput[]
  updateMany?: Prisma.PostLikeUpdateManyWithWhereWithoutPostInput | Prisma.PostLikeUpdateManyWithWhereWithoutPostInput[]
  deleteMany?: Prisma.PostLikeScalarWhereInput | Prisma.PostLikeScalarWhereInput[]
}

export type PostLikeCreateWithoutUserInput = {
  id?: string
  createdAt?: Date | string
  post: Prisma.PostCreateNestedOneWithoutLikesInput
}

export type PostLikeUncheckedCreateWithoutUserInput = {
  id?: string
  postId: string
  createdAt?: Date | string
}

export type PostLikeCreateOrConnectWithoutUserInput = {
  where: Prisma.PostLikeWhereUniqueInput
  create: Prisma.XOR<Prisma.PostLikeCreateWithoutUserInput, Prisma.PostLikeUncheckedCreateWithoutUserInput>
}

export type PostLikeCreateManyUserInputEnvelope = {
  data: Prisma.PostLikeCreateManyUserInput | Prisma.PostLikeCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type PostLikeUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.PostLikeWhereUniqueInput
  update: Prisma.XOR<Prisma.PostLikeUpdateWithoutUserInput, Prisma.PostLikeUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.PostLikeCreateWithoutUserInput, Prisma.PostLikeUncheckedCreateWithoutUserInput>
}

export type PostLikeUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.PostLikeWhereUniqueInput
  data: Prisma.XOR<Prisma.PostLikeUpdateWithoutUserInput, Prisma.PostLikeUncheckedUpdateWithoutUserInput>
}

export type PostLikeUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.PostLikeScalarWhereInput
  data: Prisma.XOR<Prisma.PostLikeUpdateManyMutationInput, Prisma.PostLikeUncheckedUpdateManyWithoutUserInput>
}

export type PostLikeScalarWhereInput = {
  AND?: Prisma.PostLikeScalarWhereInput | Prisma.PostLikeScalarWhereInput[]
  OR?: Prisma.PostLikeScalarWhereInput[]
  NOT?: Prisma.PostLikeScalarWhereInput | Prisma.PostLikeScalarWhereInput[]
  id?: Prisma.StringFilter<"PostLike"> | string
  postId?: Prisma.StringFilter<"PostLike"> | string
  userId?: Prisma.StringFilter<"PostLike"> | string
  createdAt?: Prisma.DateTimeFilter<"PostLike"> | Date | string
}

export type PostLikeCreateWithoutPostInput = {
  id?: string
  createdAt?: Date | string
  user: Prisma.AlumniProfileCreateNestedOneWithoutPostLikesInput
}

export type PostLikeUncheckedCreateWithoutPostInput = {
  id?: string
  userId: string
  createdAt?: Date | string
}

export type PostLikeCreateOrConnectWithoutPostInput = {
  where: Prisma.PostLikeWhereUniqueInput
  create: Prisma.XOR<Prisma.PostLikeCreateWithoutPostInput, Prisma.PostLikeUncheckedCreateWithoutPostInput>
}

export type PostLikeCreateManyPostInputEnvelope = {
  data: Prisma.PostLikeCreateManyPostInput | Prisma.PostLikeCreateManyPostInput[]
  skipDuplicates?: boolean
}

export type PostLikeUpsertWithWhereUniqueWithoutPostInput = {
  where: Prisma.PostLikeWhereUniqueInput
  update: Prisma.XOR<Prisma.PostLikeUpdateWithoutPostInput, Prisma.PostLikeUncheckedUpdateWithoutPostInput>
  create: Prisma.XOR<Prisma.PostLikeCreateWithoutPostInput, Prisma.PostLikeUncheckedCreateWithoutPostInput>
}

export type PostLikeUpdateWithWhereUniqueWithoutPostInput = {
  where: Prisma.PostLikeWhereUniqueInput
  data: Prisma.XOR<Prisma.PostLikeUpdateWithoutPostInput, Prisma.PostLikeUncheckedUpdateWithoutPostInput>
}

export type PostLikeUpdateManyWithWhereWithoutPostInput = {
  where: Prisma.PostLikeScalarWhereInput
  data: Prisma.XOR<Prisma.PostLikeUpdateManyMutationInput, Prisma.PostLikeUncheckedUpdateManyWithoutPostInput>
}

export type PostLikeCreateManyUserInput = {
  id?: string
  postId: string
  createdAt?: Date | string
}

export type PostLikeUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  post?: Prisma.PostUpdateOneRequiredWithoutLikesNestedInput
}

export type PostLikeUncheckedUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  postId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type PostLikeUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  postId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type PostLikeCreateManyPostInput = {
  id?: string
  userId: string
  createdAt?: Date | string
}

export type PostLikeUpdateWithoutPostInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.AlumniProfileUpdateOneRequiredWithoutPostLikesNestedInput
}

export type PostLikeUncheckedUpdateWithoutPostInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type PostLikeUncheckedUpdateManyWithoutPostInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}



export type PostLikeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  postId?: boolean
  userId?: boolean
  createdAt?: boolean
  post?: boolean | Prisma.PostDefaultArgs<ExtArgs>
  user?: boolean | Prisma.AlumniProfileDefaultArgs<ExtArgs>
}, ExtArgs["result"]["postLike"]>



export type PostLikeSelectScalar = {
  id?: boolean
  postId?: boolean
  userId?: boolean
  createdAt?: boolean
}

export type PostLikeOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "postId" | "userId" | "createdAt", ExtArgs["result"]["postLike"]>
export type PostLikeInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  post?: boolean | Prisma.PostDefaultArgs<ExtArgs>
  user?: boolean | Prisma.AlumniProfileDefaultArgs<ExtArgs>
}

export type $PostLikePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "PostLike"
  objects: {
    post: Prisma.$PostPayload<ExtArgs>
    user: Prisma.$AlumniProfilePayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    postId: string
    userId: string
    createdAt: Date
  }, ExtArgs["result"]["postLike"]>
  composites: {}
}

export type PostLikeGetPayload<S extends boolean | null | undefined | PostLikeDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$PostLikePayload, S>

export type PostLikeCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<PostLikeFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: PostLikeCountAggregateInputType | true
  }

export interface PostLikeDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['PostLike'], meta: { name: 'PostLike' } }
  /**
   * Find zero or one PostLike that matches the filter.
   * @param {PostLikeFindUniqueArgs} args - Arguments to find a PostLike
   * @example
   * // Get one PostLike
   * const postLike = await prisma.postLike.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends PostLikeFindUniqueArgs>(args: Prisma.SelectSubset<T, PostLikeFindUniqueArgs<ExtArgs>>): Prisma.Prisma__PostLikeClient<runtime.Types.Result.GetResult<Prisma.$PostLikePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one PostLike that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {PostLikeFindUniqueOrThrowArgs} args - Arguments to find a PostLike
   * @example
   * // Get one PostLike
   * const postLike = await prisma.postLike.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends PostLikeFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, PostLikeFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__PostLikeClient<runtime.Types.Result.GetResult<Prisma.$PostLikePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first PostLike that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PostLikeFindFirstArgs} args - Arguments to find a PostLike
   * @example
   * // Get one PostLike
   * const postLike = await prisma.postLike.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends PostLikeFindFirstArgs>(args?: Prisma.SelectSubset<T, PostLikeFindFirstArgs<ExtArgs>>): Prisma.Prisma__PostLikeClient<runtime.Types.Result.GetResult<Prisma.$PostLikePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first PostLike that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PostLikeFindFirstOrThrowArgs} args - Arguments to find a PostLike
   * @example
   * // Get one PostLike
   * const postLike = await prisma.postLike.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends PostLikeFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, PostLikeFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__PostLikeClient<runtime.Types.Result.GetResult<Prisma.$PostLikePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more PostLikes that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PostLikeFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all PostLikes
   * const postLikes = await prisma.postLike.findMany()
   * 
   * // Get first 10 PostLikes
   * const postLikes = await prisma.postLike.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const postLikeWithIdOnly = await prisma.postLike.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends PostLikeFindManyArgs>(args?: Prisma.SelectSubset<T, PostLikeFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$PostLikePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a PostLike.
   * @param {PostLikeCreateArgs} args - Arguments to create a PostLike.
   * @example
   * // Create one PostLike
   * const PostLike = await prisma.postLike.create({
   *   data: {
   *     // ... data to create a PostLike
   *   }
   * })
   * 
   */
  create<T extends PostLikeCreateArgs>(args: Prisma.SelectSubset<T, PostLikeCreateArgs<ExtArgs>>): Prisma.Prisma__PostLikeClient<runtime.Types.Result.GetResult<Prisma.$PostLikePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many PostLikes.
   * @param {PostLikeCreateManyArgs} args - Arguments to create many PostLikes.
   * @example
   * // Create many PostLikes
   * const postLike = await prisma.postLike.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends PostLikeCreateManyArgs>(args?: Prisma.SelectSubset<T, PostLikeCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a PostLike.
   * @param {PostLikeDeleteArgs} args - Arguments to delete one PostLike.
   * @example
   * // Delete one PostLike
   * const PostLike = await prisma.postLike.delete({
   *   where: {
   *     // ... filter to delete one PostLike
   *   }
   * })
   * 
   */
  delete<T extends PostLikeDeleteArgs>(args: Prisma.SelectSubset<T, PostLikeDeleteArgs<ExtArgs>>): Prisma.Prisma__PostLikeClient<runtime.Types.Result.GetResult<Prisma.$PostLikePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one PostLike.
   * @param {PostLikeUpdateArgs} args - Arguments to update one PostLike.
   * @example
   * // Update one PostLike
   * const postLike = await prisma.postLike.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends PostLikeUpdateArgs>(args: Prisma.SelectSubset<T, PostLikeUpdateArgs<ExtArgs>>): Prisma.Prisma__PostLikeClient<runtime.Types.Result.GetResult<Prisma.$PostLikePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more PostLikes.
   * @param {PostLikeDeleteManyArgs} args - Arguments to filter PostLikes to delete.
   * @example
   * // Delete a few PostLikes
   * const { count } = await prisma.postLike.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends PostLikeDeleteManyArgs>(args?: Prisma.SelectSubset<T, PostLikeDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more PostLikes.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PostLikeUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many PostLikes
   * const postLike = await prisma.postLike.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends PostLikeUpdateManyArgs>(args: Prisma.SelectSubset<T, PostLikeUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one PostLike.
   * @param {PostLikeUpsertArgs} args - Arguments to update or create a PostLike.
   * @example
   * // Update or create a PostLike
   * const postLike = await prisma.postLike.upsert({
   *   create: {
   *     // ... data to create a PostLike
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the PostLike we want to update
   *   }
   * })
   */
  upsert<T extends PostLikeUpsertArgs>(args: Prisma.SelectSubset<T, PostLikeUpsertArgs<ExtArgs>>): Prisma.Prisma__PostLikeClient<runtime.Types.Result.GetResult<Prisma.$PostLikePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of PostLikes.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PostLikeCountArgs} args - Arguments to filter PostLikes to count.
   * @example
   * // Count the number of PostLikes
   * const count = await prisma.postLike.count({
   *   where: {
   *     // ... the filter for the PostLikes we want to count
   *   }
   * })
  **/
  count<T extends PostLikeCountArgs>(
    args?: Prisma.Subset<T, PostLikeCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], PostLikeCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a PostLike.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PostLikeAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends PostLikeAggregateArgs>(args: Prisma.Subset<T, PostLikeAggregateArgs>): Prisma.PrismaPromise<GetPostLikeAggregateType<T>>

  /**
   * Group by PostLike.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PostLikeGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends PostLikeGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: PostLikeGroupByArgs['orderBy'] }
      : { orderBy?: PostLikeGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, PostLikeGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetPostLikeGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the PostLike model
 */
readonly fields: PostLikeFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for PostLike.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__PostLikeClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  post<T extends Prisma.PostDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.PostDefaultArgs<ExtArgs>>): Prisma.Prisma__PostClient<runtime.Types.Result.GetResult<Prisma.$PostPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  user<T extends Prisma.AlumniProfileDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.AlumniProfileDefaultArgs<ExtArgs>>): Prisma.Prisma__AlumniProfileClient<runtime.Types.Result.GetResult<Prisma.$AlumniProfilePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the PostLike model
 */
export interface PostLikeFieldRefs {
  readonly id: Prisma.FieldRef<"PostLike", 'String'>
  readonly postId: Prisma.FieldRef<"PostLike", 'String'>
  readonly userId: Prisma.FieldRef<"PostLike", 'String'>
  readonly createdAt: Prisma.FieldRef<"PostLike", 'DateTime'>
}
    

// Custom InputTypes
/**
 * PostLike findUnique
 */
export type PostLikeFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the PostLike
   */
  select?: Prisma.PostLikeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the PostLike
   */
  omit?: Prisma.PostLikeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PostLikeInclude<ExtArgs> | null
  /**
   * Filter, which PostLike to fetch.
   */
  where: Prisma.PostLikeWhereUniqueInput
}

/**
 * PostLike findUniqueOrThrow
 */
export type PostLikeFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the PostLike
   */
  select?: Prisma.PostLikeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the PostLike
   */
  omit?: Prisma.PostLikeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PostLikeInclude<ExtArgs> | null
  /**
   * Filter, which PostLike to fetch.
   */
  where: Prisma.PostLikeWhereUniqueInput
}

/**
 * PostLike findFirst
 */
export type PostLikeFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the PostLike
   */
  select?: Prisma.PostLikeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the PostLike
   */
  omit?: Prisma.PostLikeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PostLikeInclude<ExtArgs> | null
  /**
   * Filter, which PostLike to fetch.
   */
  where?: Prisma.PostLikeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of PostLikes to fetch.
   */
  orderBy?: Prisma.PostLikeOrderByWithRelationInput | Prisma.PostLikeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for PostLikes.
   */
  cursor?: Prisma.PostLikeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` PostLikes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` PostLikes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of PostLikes.
   */
  distinct?: Prisma.PostLikeScalarFieldEnum | Prisma.PostLikeScalarFieldEnum[]
}

/**
 * PostLike findFirstOrThrow
 */
export type PostLikeFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the PostLike
   */
  select?: Prisma.PostLikeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the PostLike
   */
  omit?: Prisma.PostLikeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PostLikeInclude<ExtArgs> | null
  /**
   * Filter, which PostLike to fetch.
   */
  where?: Prisma.PostLikeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of PostLikes to fetch.
   */
  orderBy?: Prisma.PostLikeOrderByWithRelationInput | Prisma.PostLikeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for PostLikes.
   */
  cursor?: Prisma.PostLikeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` PostLikes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` PostLikes.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of PostLikes.
   */
  distinct?: Prisma.PostLikeScalarFieldEnum | Prisma.PostLikeScalarFieldEnum[]
}

/**
 * PostLike findMany
 */
export type PostLikeFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the PostLike
   */
  select?: Prisma.PostLikeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the PostLike
   */
  omit?: Prisma.PostLikeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PostLikeInclude<ExtArgs> | null
  /**
   * Filter, which PostLikes to fetch.
   */
  where?: Prisma.PostLikeWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of PostLikes to fetch.
   */
  orderBy?: Prisma.PostLikeOrderByWithRelationInput | Prisma.PostLikeOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing PostLikes.
   */
  cursor?: Prisma.PostLikeWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` PostLikes from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` PostLikes.
   */
  skip?: number
  distinct?: Prisma.PostLikeScalarFieldEnum | Prisma.PostLikeScalarFieldEnum[]
}

/**
 * PostLike create
 */
export type PostLikeCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the PostLike
   */
  select?: Prisma.PostLikeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the PostLike
   */
  omit?: Prisma.PostLikeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PostLikeInclude<ExtArgs> | null
  /**
   * The data needed to create a PostLike.
   */
  data: Prisma.XOR<Prisma.PostLikeCreateInput, Prisma.PostLikeUncheckedCreateInput>
}

/**
 * PostLike createMany
 */
export type PostLikeCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many PostLikes.
   */
  data: Prisma.PostLikeCreateManyInput | Prisma.PostLikeCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * PostLike update
 */
export type PostLikeUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the PostLike
   */
  select?: Prisma.PostLikeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the PostLike
   */
  omit?: Prisma.PostLikeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PostLikeInclude<ExtArgs> | null
  /**
   * The data needed to update a PostLike.
   */
  data: Prisma.XOR<Prisma.PostLikeUpdateInput, Prisma.PostLikeUncheckedUpdateInput>
  /**
   * Choose, which PostLike to update.
   */
  where: Prisma.PostLikeWhereUniqueInput
}

/**
 * PostLike updateMany
 */
export type PostLikeUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update PostLikes.
   */
  data: Prisma.XOR<Prisma.PostLikeUpdateManyMutationInput, Prisma.PostLikeUncheckedUpdateManyInput>
  /**
   * Filter which PostLikes to update
   */
  where?: Prisma.PostLikeWhereInput
  /**
   * Limit how many PostLikes to update.
   */
  limit?: number
}

/**
 * PostLike upsert
 */
export type PostLikeUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the PostLike
   */
  select?: Prisma.PostLikeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the PostLike
   */
  omit?: Prisma.PostLikeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PostLikeInclude<ExtArgs> | null
  /**
   * The filter to search for the PostLike to update in case it exists.
   */
  where: Prisma.PostLikeWhereUniqueInput
  /**
   * In case the PostLike found by the `where` argument doesn't exist, create a new PostLike with this data.
   */
  create: Prisma.XOR<Prisma.PostLikeCreateInput, Prisma.PostLikeUncheckedCreateInput>
  /**
   * In case the PostLike was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.PostLikeUpdateInput, Prisma.PostLikeUncheckedUpdateInput>
}

/**
 * PostLike delete
 */
export type PostLikeDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the PostLike
   */
  select?: Prisma.PostLikeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the PostLike
   */
  omit?: Prisma.PostLikeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PostLikeInclude<ExtArgs> | null
  /**
   * Filter which PostLike to delete.
   */
  where: Prisma.PostLikeWhereUniqueInput
}

/**
 * PostLike deleteMany
 */
export type PostLikeDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which PostLikes to delete
   */
  where?: Prisma.PostLikeWhereInput
  /**
   * Limit how many PostLikes to delete.
   */
  limit?: number
}

/**
 * PostLike without action
 */
export type PostLikeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the PostLike
   */
  select?: Prisma.PostLikeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the PostLike
   */
  omit?: Prisma.PostLikeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PostLikeInclude<ExtArgs> | null
}
