import { protectedProcedure, publicProcedure } from "../lib/orpc";
import { profilesRouter } from "./profiles.router";
import { connectionsRouter } from "./connections.router";
import { postsRouter } from "./posts.router";
import { eventsRouter } from "./events.router";
import { donationsRouter } from "./donations.router";
import { newsRouter } from "./news.router";
import { notificationsRouter } from "./notifications.router";

export const appRouter = {
  // Health check and basic endpoints
  healthCheck: publicProcedure.handler(() => {
    return "OK";
  }),
  getUser: protectedProcedure.handler(({ context }) => {
    return context.session?.user;
  }),
  privateData: protectedProcedure.handler(({ context }) => {
    return {
      message: "This is private",
      user: context.session?.user,
    };
  }),

  // Main feature routers
  profiles: profilesRouter,
  connections: connectionsRouter,
  posts: postsRouter,
  events: eventsRouter,
  donations: donationsRouter,
  news: newsRouter,
  notifications: notificationsRouter,
};

export type AppRouter = typeof appRouter;
