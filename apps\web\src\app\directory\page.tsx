"use client"

import { useMemo, useState, useEffect } from "react"
import { useQuery } from "@tanstack/react-query"
import { orpc } from "@/utils/orpc"
import SearchFilters, { type DirectoryFilter } from "./_components/search-filters"
import { ProfileCard } from "./_components/profile-card"
import { Pagination, PaginationContent, PaginationItem, PaginationNext, PaginationPrevious } from "@/components/ui/pagination"
import { useSearchParams, useRouter } from "next/navigation"

export default function DirectoryPage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const [page, setPage] = useState<number>(() => Number(searchParams.get("page") || 1))
  const [filters, setFilters] = useState<DirectoryFilter>(() => ({
    search: searchParams.get("search") || undefined,
    graduationYear: searchParams.get("graduationYear") ? Number(searchParams.get("graduationYear")) : undefined,
    programType: searchParams.get("programType") || undefined,
    industry: searchParams.get("industry") || undefined,
    location: searchParams.get("location") || undefined,
    centerLocation: searchParams.get("centerLocation") || undefined,
    mentorshipOffered: searchParams.get("mentorshipOffered") === "true" ? true : undefined,
    mentorshipSought: searchParams.get("mentorshipSought") === "true" ? true : undefined,
  }))

  // Sync URL when page/filters change
  useEffect(() => {
    const params = new URLSearchParams()
    params.set("page", String(page))
    if (filters.search) params.set("search", filters.search)
    if (filters.graduationYear) params.set("graduationYear", String(filters.graduationYear))
    if (filters.programType) params.set("programType", filters.programType)
    if (filters.industry) params.set("industry", filters.industry)
    if (filters.location) params.set("location", filters.location)
    if (filters.centerLocation) params.set("centerLocation", filters.centerLocation)
    if (typeof filters.mentorshipOffered === "boolean") params.set("mentorshipOffered", String(filters.mentorshipOffered))
    if (typeof filters.mentorshipSought === "boolean") params.set("mentorshipSought", String(filters.mentorshipSought))
    const query = params.toString()
    router.replace(`/directory${query ? `?${query}` : ""}`)
  }, [page, filters])

  const query = useQuery(
    orpc.profiles.search.queryOptions({
      input: {
        page,
        limit: 20,
        ...filters,
        graduationYear: filters.graduationYear ? Number(filters.graduationYear) : undefined,
      },
    })
  )

  const totalPages = useMemo(() => {
    const total = (query.data as any)?.total ?? 0
    const limit = (query.data as any)?.limit ?? 20
    return Math.max(1, Math.ceil(total / limit))
  }, [query.data])

  return (
    <div className="container mx-auto px-4 py-4 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold">Alumni Directory</h1>
        <div className="flex items-center gap-2"></div>
      </div>

      <SearchFilters value={filters} onChange={(v) => { setPage(1); setFilters(v) }} />

      <div className="grid gap-3">
        {query.isLoading && <div className="text-sm text-muted-foreground">Loading...</div>}
        {!query.isLoading && (query.data?.data?.length ?? 0) === 0 && (
          <div className="rounded-lg border bg-card p-4 text-sm text-muted-foreground">No profiles found.</div>
        )}
        {query.data?.data?.map((p: any) => (
          <ProfileCard key={p.id} profile={p} />
        ))}
      </div>

      <div className="flex items-center justify-end">
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                onClick={(e) => { e.preventDefault(); setPage((p) => Math.max(1, p - 1)) }}
                href="#"
              />
            </PaginationItem>
            <PaginationItem>
              <span className="text-sm text-muted-foreground px-3 py-2">Page {page} of {totalPages}</span>
            </PaginationItem>
            <PaginationItem>
              <PaginationNext
                onClick={(e) => { e.preventDefault(); setPage((p) => Math.min(totalPages, p + 1)) }}
                href="#"
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </div>
    </div>
  )
}

