{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Work/protec/apps/web/src/app/dashboard/page.tsx"], "sourcesContent": ["\"use client\"\nimport { authClient } from \"@/lib/auth-client\";\nimport { useQuery } from \"@tanstack/react-query\";\nimport { orpc } from \"@/utils/orpc\";\nimport { useRouter } from \"next/navigation\";\nimport { useEffect } from \"react\";\n\nexport default function Dashboard() {\n  const router = useRouter();\n  const { data: session, isPending } = authClient.useSession();\n\n  const privateData = useQuery(orpc.privateData.queryOptions());\n\n  useEffect(() => {\n    if (!session && !isPending) {\n      router.push(\"/login\");\n    }\n  }, [session, isPending]);\n\n  if (isPending) {\n    return <div>Loading...</div>;\n  }\n\n  return (\n    <div>\n      <h1>Dashboard</h1>\n      <p>Welcome {session?.user.name}</p>\n      <p>privateData: {privateData.data?.message}</p>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;;;AALA;;;;;;AAOe,SAAS;QAoBD;;IAnBrB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,MAAM,OAAO,EAAE,SAAS,EAAE,GAAG,8IAAA,CAAA,aAAU,CAAC,UAAU;IAE1D,MAAM,cAAc,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE,sIAAA,CAAA,OAAI,CAAC,WAAW,CAAC,YAAY;IAE1D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,CAAC,WAAW,CAAC,WAAW;gBAC1B,OAAO,IAAI,CAAC;YACd;QACF;8BAAG;QAAC;QAAS;KAAU;IAEvB,IAAI,WAAW;QACb,qBAAO,6LAAC;sBAAI;;;;;;IACd;IAEA,qBACE,6LAAC;;0BACC,6LAAC;0BAAG;;;;;;0BACJ,6LAAC;;oBAAE;oBAAS,oBAAA,8BAAA,QAAS,IAAI,CAAC,IAAI;;;;;;;0BAC9B,6LAAC;;oBAAE;qBAAc,oBAAA,YAAY,IAAI,cAAhB,wCAAA,kBAAkB,OAAO;;;;;;;;;;;;;AAGhD;GAvBwB;;QACP,qIAAA,CAAA,YAAS;QACa,8IAAA,CAAA,aAAU,CAAC;QAE5B,8KAAA,CAAA,WAAQ;;;KAJN", "debugId": null}}]}