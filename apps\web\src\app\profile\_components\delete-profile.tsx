"use client"

import { useState } from "react"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import { client } from "@/utils/orpc"
import { Button } from "@/components/ui/button"
import { useRouter } from "next/navigation"
import { useConfirm } from "@/components/ui/confirm-dialog"

export function DeleteProfile() {
  const router = useRouter()
  const queryClient = useQueryClient()
  const confirm = useConfirm()

  const deleteMutation = useMutation({
    mutationFn: async () => client.profiles.deleteMe(),
    onSuccess: async () => {
      await queryClient.invalidateQueries()
      router.push("/")
    },
  })

  async function handleDelete() {
    const ok = await confirm({
      title: "Delete profile?",
      description: "This action cannot be undone.",
      confirmText: "Delete",
      confirmButton: { className: "bg-destructive text-destructive-foreground hover:bg-destructive/90" },
    })
    if (ok) deleteMutation.mutate()
  }

  return (
    <div className="rounded-lg border bg-card p-4">
      <div className="flex items-center justify-between">
        <div>
          <div className="font-medium">Danger zone</div>
          <div className="text-sm text-muted-foreground">Deleting your profile removes your data from the platform.</div>
        </div>
        <Button variant="destructive" onClick={handleDelete}>Delete profile</Button>
      </div>
    </div>
  )
}

export default DeleteProfile


