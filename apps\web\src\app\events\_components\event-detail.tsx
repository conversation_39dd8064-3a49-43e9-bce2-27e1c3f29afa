"use client"

import { useParams } from "next/navigation"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { orpc, client } from "@/utils/orpc"
import { Button } from "@/components/ui/button"

export default function EventDetail() {
  const params = useParams()
  const id = params?.id as string
  const queryClient = useQueryClient()

  const event = useQuery(orpc.events.getById.queryOptions({ input: { id } }))

  const register = useMutation({
    mutationFn: async () => client.events.register({ eventId: id }),
    onSuccess: async () => queryClient.invalidateQueries(),
  })
  const cancel = useMutation({
    mutationFn: async () => client.events.cancelRegistration({ eventId: id }),
    onSuccess: async () => queryClient.invalidateQueries(),
  })

  if (event.isLoading) return <div className="text-sm text-muted-foreground">Loading...</div>
  if (event.isError) return <div className="text-sm text-destructive">Failed to load event.</div>
  const e = event.data as any

  return (
    <div className="space-y-4">
      <div className="rounded-lg border bg-card p-4">
        <div className="flex items-center justify-between">
          <div className="text-lg font-semibold">{e.title}</div>
          <div className="text-xs text-muted-foreground">{new Date(e.startDateTime).toLocaleString()}</div>
        </div>
        <div className="text-sm text-muted-foreground">{e.location || (e.isVirtual ? "Virtual" : "")}</div>
        <div className="mt-2 text-sm whitespace-pre-wrap">{e.description}</div>
        <div className="mt-3 flex items-center gap-2">
          <Button size="sm" onClick={() => register.mutate()} disabled={register.isPending}>RSVP</Button>
          <Button size="sm" variant="outline" onClick={() => cancel.mutate()} disabled={cancel.isPending}>Cancel</Button>
        </div>
      </div>
    </div>
  )
}


