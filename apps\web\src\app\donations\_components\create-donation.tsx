"use client"

import { useState } from "react"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import { client } from "@/utils/orpc"
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Button } from "@/components/ui/button"
import { useForm } from "react-hook-form"

type DonationInput = {
  amount: number
  currency?: string
  donationType?: "ONE_TIME" | "MONTHLY" | "QUARTERLY" | "ANNUAL"
  organizationId: string
  purpose?: string
  isAnonymous?: boolean
  paymentMethod?: string
}

export default function CreateDonation() {
  const queryClient = useQueryClient()
  const [submitting, setSubmitting] = useState(false)
  const form = useForm<DonationInput>({
    defaultValues: {
      amount: 100,
      currency: "ZAR",
      donationType: "ONE_TIME",
      organizationId: "default-org",
      purpose: "",
      isAnonymous: false,
      paymentMethod: "PayFast",
    },
  })

  const create = useMutation({
    mutationFn: async (v: DonationInput) => client.donations.create({
      amount: Number(v.amount),
      currency: v.currency || "ZAR",
      donationType: v.donationType || "ONE_TIME",
      organizationId: v.organizationId,
      purpose: v.purpose || undefined,
      isAnonymous: !!v.isAnonymous,
      paymentMethod: v.paymentMethod || undefined,
    }),
    onSuccess: async () => queryClient.invalidateQueries(),
  })

  return (
    <div className="rounded-lg border bg-card p-4">
      <div className="text-sm font-medium text-muted-foreground mb-3">Make a donation</div>
      <Form {...form}>
        <form className="grid gap-4 md:grid-cols-3" onSubmit={form.handleSubmit((v) => create.mutate(v))}>
          <FormField name="amount" control={form.control} render={({ field }) => (
            <FormItem>
              <FormLabel>Amount</FormLabel>
              <FormControl><Input type="number" step="10" min="10" {...field} /></FormControl>
            </FormItem>
          )} />
          <FormField name="currency" control={form.control} render={({ field }) => (
            <FormItem>
              <FormLabel>Currency</FormLabel>
              <FormControl>
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger><SelectValue /></SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ZAR">ZAR</SelectItem>
                    <SelectItem value="USD">USD</SelectItem>
                  </SelectContent>
                </Select>
              </FormControl>
            </FormItem>
          )} />
          <FormField name="donationType" control={form.control} render={({ field }) => (
            <FormItem>
              <FormLabel>Type</FormLabel>
              <FormControl>
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger><SelectValue /></SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ONE_TIME">One-time</SelectItem>
                    <SelectItem value="MONTHLY">Monthly</SelectItem>
                    <SelectItem value="QUARTERLY">Quarterly</SelectItem>
                    <SelectItem value="ANNUAL">Annual</SelectItem>
                  </SelectContent>
                </Select>
              </FormControl>
            </FormItem>
          )} />
          <FormField name="organizationId" control={form.control} render={({ field }) => (
            <FormItem>
              <FormLabel>Organization ID</FormLabel>
              <FormControl><Input {...field} placeholder="org-..." /></FormControl>
            </FormItem>
          )} />
          <FormField name="purpose" control={form.control} render={({ field }) => (
            <FormItem>
              <FormLabel>Purpose (optional)</FormLabel>
              <FormControl><Input {...field} placeholder="Support for ..." /></FormControl>
            </FormItem>
          )} />
          <FormField name="paymentMethod" control={form.control} render={({ field }) => (
            <FormItem>
              <FormLabel>Payment method</FormLabel>
              <FormControl>
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger><SelectValue /></SelectTrigger>
                  <SelectContent>
                    <SelectItem value="PayFast">PayFast</SelectItem>
                    <SelectItem value="PayPal">PayPal</SelectItem>
                    <SelectItem value="Card">Card</SelectItem>
                  </SelectContent>
                </Select>
              </FormControl>
            </FormItem>
          )} />
          <FormField name="isAnonymous" control={form.control} render={({ field }) => (
            <FormItem>
              <FormLabel>Anonymous</FormLabel>
              <FormControl><div className="flex items-center gap-3"><Switch checked={!!field.value} onCheckedChange={field.onChange} /></div></FormControl>
            </FormItem>
          )} />
          <div className="md:col-span-3 flex items-center justify-end">
            <Button type="submit" disabled={submitting || create.isPending}>{create.isPending ? "Processing..." : "Donate"}</Button>
          </div>
        </form>
      </Form>
    </div>
  )
}


