import { z } from "zod";
import { publicProcedure, protectedProcedure } from "../lib/orpc";
import { prisma } from "../lib/database";
import { getOrganizationContext } from "../lib/context";
import {
  canViewProfile,
  createPaginationResult,
  getPaginationParams,
  buildSearchFilter,
} from "../lib/utils";
import {
  createAlumniProfileSchema,
  updateAlumniProfileSchema,
  profilePrivacySchema,
  profileSearchSchema,
  idSchema,
} from "../lib/validation";

export const profilesRouter = {
  /**
   * Search alumni profiles (public endpoint with privacy filtering)
   */
  search: publicProcedure
    .input(profileSearchSchema)
    .handler(async ({ input, context }) => {
      const { page, limit, skip } = getPaginationParams(input);

      const orgCtx = context.user
        ? await getOrganizationContext(context.user.id, input.organizationId)
        : {};

      const where: any = {
        ...buildSearchFilter(input.search, [
          "firstName",
          "lastName",
          "displayName",
          "industry",
          "location",
          "centerLocation",
          "skillsOffered",
          "skillsWanted",
        ]),
        graduationYear: input.graduationYear ?? undefined,
        programType: input.programType ?? undefined,
        industry: input.industry ?? undefined,
        location: input.location ?? undefined,
        centerLocation: input.centerLocation ?? undefined,
        mentorshipOffered: input.mentorshipOffered ?? undefined,
        mentorshipSought: input.mentorshipSought ?? undefined,
        ...(input.organizationId
          ? { primaryOrganizationId: input.organizationId }
          : {}),
      };

      // Basic privacy filter: if unauthenticated or not alumni, only PUBLIC
      if (!context.user || !orgCtx.alumniProfile) {
        where.profileVisibility = "PUBLIC";
      }

      const [data, total] = await Promise.all([
        prisma.alumniProfile.findMany({
          where,
          orderBy: { createdAt: "desc" },
          skip,
          take: limit,
        }),
        prisma.alumniProfile.count({ where }),
      ]);

      return createPaginationResult(data, total, page, limit);
    }),

  /**
   * Get specific profile by ID (public endpoint with privacy filtering)
   */
  getById: publicProcedure
    .input(z.object({ id: idSchema }))
    .handler(async ({ input, context }) => {
      const profile = await prisma.alumniProfile.findUnique({
        where: { id: input.id },
      });

      if (!profile) {
        throw new Error("Alumni profile not found");
      }

      const orgCtx = context.user
        ? await getOrganizationContext(context.user.id)
        : { user: null };

      if (!canViewProfile(profile.profileVisibility, orgCtx as any, profile.userId)) {
        throw new Error("Not allowed to view this profile");
      }

      return profile;
    }),

  /**
   * Get current user's profile
   */
  getMe: protectedProcedure.handler(async ({ context }) => {
    const profile = await prisma.alumniProfile.findUnique({
      where: { userId: context.user!.id },
    });

    if (!profile) {
      throw new Error("Alumni profile not found");
    }

    return profile;
  }),

  /**
   * Create alumni profile
   */
  create: protectedProcedure
    .input(createAlumniProfileSchema)
    .handler(async ({ input, context }) => {
      const existing = await prisma.alumniProfile.findUnique({
        where: { userId: context.user!.id },
        select: { id: true },
      });
      if (existing) {
        throw new Error("Alumni profile already exists");
      }

      return prisma.alumniProfile.create({
        data: {
          userId: context.user!.id,
          ...input,
        },
      });
    }),

  /**
   * Update current user's profile
   */
  updateMe: protectedProcedure
    .input(updateAlumniProfileSchema)
    .handler(async ({ input, context }) => {
      return prisma.alumniProfile.update({
        where: { userId: context.user!.id },
        data: input,
      });
    }),

  /**
   * Update privacy settings
   */
  updatePrivacy: protectedProcedure
    .input(profilePrivacySchema)
    .handler(async ({ input, context }) => {
      return prisma.alumniProfile.update({
        where: { userId: context.user!.id },
        data: input,
      });
    }),

  /**
   * Find mentorship opportunities
   */
  mentorship: protectedProcedure
    .input(
      z.object({
        type: z.enum(["offered", "sought"]).default("offered"),
        skills: z.string().optional(),
        page: z.number().int().min(1).default(1),
        limit: z.number().int().min(1).max(100).default(20),
      })
    )
    .handler(async ({ input }) => {
      const { page, limit, skip } = getPaginationParams(input);
      const where: any = {
        ...(input.type === "offered"
          ? { mentorshipOffered: true }
          : { mentorshipSought: true }),
      };
      if (input.skills) {
        where.OR = [
          { skillsOffered: { contains: input.skills, mode: "insensitive" } },
          { skillsWanted: { contains: input.skills, mode: "insensitive" } },
        ];
      }

      const [data, total] = await Promise.all([
        prisma.alumniProfile.findMany({ where, skip, take: limit }),
        prisma.alumniProfile.count({ where }),
      ]);
      return createPaginationResult(data, total, page, limit);
    }),

  /**
   * Get profile statistics
   */
  getStats: protectedProcedure.handler(async ({ context }) => {
    const userId = context.user!.id;
    const [posts, connections, donations] = await Promise.all([
      prisma.post.count({ where: { author: { userId } } }),
      prisma.connection.count({
        where: {
          OR: [
            { requester: { userId } },
            { requested: { userId } },
          ],
          status: "ACCEPTED",
        },
      }),
      prisma.donation.count({ where: { donor: { userId } } }),
    ]);
    return { posts, connections, donations };
  }),

  /**
   * Check if current user has a profile
   */
  checkExists: protectedProcedure.handler(async ({ context }) => {
    const exists = !!(await prisma.alumniProfile.findUnique({
      where: { userId: context.user!.id },
      select: { id: true },
    }));
    return { exists };
  }),

  /**
   * Get profiles by organization (for organization members)
   */
  getByOrganization: protectedProcedure
    .input(
      z.object({
        organizationId: idSchema,
        page: z.number().int().min(1).default(1),
        limit: z.number().int().min(1).max(100).default(20),
      })
    )
    .handler(async ({ input }) => {
      const { page, limit, skip } = getPaginationParams(input);
      const where = { primaryOrganizationId: input.organizationId } as const;
      const [data, total] = await Promise.all([
        prisma.alumniProfile.findMany({ where, skip, take: limit }),
        prisma.alumniProfile.count({ where }),
      ]);
      return createPaginationResult(data, total, page, limit);
    }),

  /**
   * Delete current user's profile
   */
  deleteMe: protectedProcedure.handler(async ({ context }) => {
    await prisma.alumniProfile.delete({ where: { userId: context.user!.id } });
    return { success: true };
  }),
};
