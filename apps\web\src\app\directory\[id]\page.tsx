"use client"

import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { orpc, client } from "@/utils/orpc"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { useConfirm } from "@/components/ui/confirm-dialog"
import { UserPlus } from "lucide-react"

export default function DirectoryProfileDetailPage() {
  const params = useParams()
  const id = params?.id as string
  const router = useRouter()
  const queryClient = useQueryClient()
  const confirm = useConfirm()

  const profileQuery = useQuery(orpc.profiles.getById.queryOptions({ input: { id } }))

  const connectMutation = useMutation({
    mutationFn: async () => client.connections.sendRequest({ requestedId: id }),
    onSuccess: async () => {
      await queryClient.invalidateQueries()
    },
  })

  if (profileQuery.isLoading) return <div className="container mx-auto px-4 py-4 text-sm text-muted-foreground">Loading...</div>
  if (profileQuery.isError) return <div className="container mx-auto px-4 py-4 text-sm text-destructive">Unable to load profile.</div>
  const p = profileQuery.data!

  const initials = `${p.firstName?.[0] ?? ""}${p.lastName?.[0] ?? ""}`.toUpperCase()

  return (
    <div className="container mx-auto px-4 py-4 space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Avatar className="h-12 w-12">
            <AvatarImage src={p.profilePicture ?? undefined} alt={p.displayName ?? p.firstName} />
            <AvatarFallback>{initials || "AL"}</AvatarFallback>
          </Avatar>
          <div>
            <div className="text-xl font-semibold">{p.displayName || `${p.firstName} ${p.lastName}`}</div>
            <div className="text-sm text-muted-foreground">{p.currentPosition || "—"}{p.currentCompany ? ` • ${p.currentCompany}` : ""}</div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button onClick={() => connectMutation.mutate()} disabled={connectMutation.isPending}>
            <UserPlus className="size-4" /> Connect
          </Button>
        </div>
      </div>

      {p.bio && (
        <div className="rounded-lg border bg-card p-4">
          <div className="text-sm text-muted-foreground">About</div>
          <div className="mt-2 whitespace-pre-wrap text-sm">{p.bio}</div>
        </div>
      )}

      <div className="grid gap-4 md:grid-cols-3">
        <div className="rounded-lg border bg-card p-4">
          <div className="text-sm text-muted-foreground">Industry</div>
          <div className="mt-1 text-sm">{p.industry || "—"}</div>
        </div>
        <div className="rounded-lg border bg-card p-4">
          <div className="text-sm text-muted-foreground">Location</div>
          <div className="mt-1 text-sm">{p.location || "—"}</div>
        </div>
        <div className="rounded-lg border bg-card p-4">
          <div className="text-sm text-muted-foreground">PROTEC</div>
          <div className="mt-1 text-sm">{p.programType} • {p.centerLocation} • {p.graduationYear}</div>
        </div>
      </div>

      <div className="rounded-lg border bg-card p-4">
        <div className="text-sm text-muted-foreground">Mentorship</div>
        <div className="mt-2 flex flex-wrap gap-2">
          {p.mentorshipOffered && <Badge variant="secondary">Offers mentorship</Badge>}
          {p.mentorshipSought && <Badge variant="secondary">Seeks mentorship</Badge>}
        </div>
        <div className="mt-2 grid gap-2 md:grid-cols-2">
          <div>
            <div className="text-sm font-medium">Skills offered</div>
            <div className="text-sm text-muted-foreground whitespace-pre-wrap">{p.skillsOffered || "—"}</div>
          </div>
          <div>
            <div className="text-sm font-medium">Skills wanted</div>
            <div className="text-sm text-muted-foreground whitespace-pre-wrap">{p.skillsWanted || "—"}</div>
          </div>
        </div>
      </div>
    </div>
  )
}


