import CreateDonation from "./_components/create-donation";
import Donation<PERSON>istory from "./_components/donation-history";
import RecurringManager from "./_components/recurring-manager";
import AdminDonations from "./_components/admin-donations";
import AdminAnalytics from "./_components/admin-analytics";

export default function DonationsPage() {
  return (
    <div className="container mx-auto px-4 py-4 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold">Donations</h1>
        <div className="flex items-center gap-2"></div>
      </div>

      <CreateDonation />

      <DonationHistory />

      <RecurringManager />

      <AdminAnalytics />
      <AdminDonations />
    </div>
  );
}
