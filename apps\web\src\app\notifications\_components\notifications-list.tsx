"use client"

import { useMemo, useState } from "react"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { orpc, client } from "@/utils/orpc"
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Pagination, PaginationContent, PaginationItem, PaginationNext, PaginationPrevious } from "@/components/ui/pagination"
import { useForm } from "react-hook-form"

type FilterInput = {
  type?: "CONNECTION_REQUEST" | "CONNECTION_ACCEPTED" | "EVENT_REMINDER" | "EVENT_REGISTRATION" | "NEW_MESSAGE" | "POST_LIKE" | "POST_COMMENT" | "DONATION_CONFIRMATION" | "NEWS_ARTICLE" | "SYSTEM_ANNOUNCEMENT"
  isRead?: boolean
}

export default function NotificationsList() {
  const [page, setPage] = useState(1)
  const [filters, setFilters] = useState<FilterInput>({})
  const queryClient = useQueryClient()
  const form = useForm<FilterInput>({ defaultValues: filters })

  const list = useQuery(orpc.notifications.list.queryOptions({ input: { page, limit: 10, ...filters } }))

  const totalPages = useMemo(() => {
    const total = (list.data as any)?.total ?? 0
    const limit = (list.data as any)?.limit ?? 10
    return Math.max(1, Math.ceil(total / limit))
  }, [list.data])

  const markRead = useMutation({
    mutationFn: async (id: string) => client.notifications.markRead({ notificationId: id }),
    onSuccess: async () => queryClient.invalidateQueries(),
  })
  const deleteMutation = useMutation({
    mutationFn: async (id: string) => client.notifications.delete({ id }),
    onSuccess: async () => queryClient.invalidateQueries(),
  })
  const markAll = useMutation({
    mutationFn: async () => client.notifications.markAllRead({}),
    onSuccess: async () => queryClient.invalidateQueries(),
  })

  return (
    <div className="space-y-4">
      <div className="rounded-lg border bg-card p-4">
        <Form {...form}>
          <form className="grid gap-4 md:grid-cols-4" onSubmit={form.handleSubmit((v) => { setPage(1); setFilters(v) })}>
            <FormField name="type" control={form.control} render={({ field }) => (
              <FormItem>
                <FormLabel>Type</FormLabel>
                <FormControl>
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger><SelectValue placeholder="All" /></SelectTrigger>
                    <SelectContent>
                      <SelectItem value="CONNECTION_REQUEST">Connection request</SelectItem>
                      <SelectItem value="CONNECTION_ACCEPTED">Connection accepted</SelectItem>
                      <SelectItem value="EVENT_REMINDER">Event reminder</SelectItem>
                      <SelectItem value="EVENT_REGISTRATION">Event registration</SelectItem>
                      <SelectItem value="NEW_MESSAGE">New message</SelectItem>
                      <SelectItem value="POST_LIKE">Post like</SelectItem>
                      <SelectItem value="POST_COMMENT">Post comment</SelectItem>
                      <SelectItem value="DONATION_CONFIRMATION">Donation confirmation</SelectItem>
                      <SelectItem value="NEWS_ARTICLE">News article</SelectItem>
                      <SelectItem value="SYSTEM_ANNOUNCEMENT">System announcement</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
              </FormItem>
            )} />
            <FormField name="isRead" control={form.control} render={({ field }) => (
              <FormItem>
                <FormLabel>Status</FormLabel>
                <FormControl>
                  <Select value={field.value as any} onValueChange={(v) => field.onChange(v === "true" ? true : v === "false" ? false : undefined)}>
                    <SelectTrigger><SelectValue placeholder="All" /></SelectTrigger>
                    <SelectContent>
                      <SelectItem value="true">Read</SelectItem>
                      <SelectItem value="false">Unread</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
              </FormItem>
            )} />
            <div className="md:col-span-2 flex items-end justify-end gap-2">
              <Button type="submit">Apply</Button>
              <Button type="button" variant="outline" onClick={() => markAll.mutate()} disabled={markAll.isPending}>Mark all read</Button>
            </div>
          </form>
        </Form>
      </div>

      <div className="grid gap-3">
        {list.isLoading && (<div className="text-sm text-muted-foreground">Loading...</div>)}
        {(list.data as any)?.data?.map((n: any) => (
          <div key={n.id} className="rounded-lg border bg-card p-4 flex items-start justify-between gap-3">
            <div className="min-w-0">
              <div className="font-medium truncate">{n.type}</div>
              <div className="text-sm text-muted-foreground whitespace-pre-wrap">{n.message || ""}</div>
              <div className="text-xs text-muted-foreground mt-1">{new Date(n.createdAt).toLocaleString()}</div>
            </div>
            <div className="flex items-center gap-2">
              {!n.isRead && <Button size="sm" variant="outline" onClick={() => markRead.mutate(n.id)} disabled={markRead.isPending}>Mark read</Button>}
              <Button size="sm" variant="destructive" onClick={() => deleteMutation.mutate(n.id)} disabled={deleteMutation.isPending}>Delete</Button>
            </div>
          </div>
        ))}
      </div>

      <div className="flex items-center justify-end">
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious href="#" onClick={(e) => { e.preventDefault(); setPage((p) => Math.max(1, p - 1)) }} />
            </PaginationItem>
            <PaginationItem>
              <span className="text-sm text-muted-foreground px-3 py-2">Page {page} of {totalPages}</span>
            </PaginationItem>
            <PaginationItem>
              <PaginationNext href="#" onClick={(e) => { e.preventDefault(); setPage((p) => Math.min(totalPages, p + 1)) }} />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </div>
    </div>
  )
}


