module.exports = {

"[project]/node_modules/@tanstack/query-devtools/build/DevtoolsComponent/EDEL3XIZ.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@tanstack_query-devtools_build_8164cb69._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@tanstack/query-devtools/build/DevtoolsComponent/EDEL3XIZ.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@tanstack/query-devtools/build/DevtoolsPanelComponent/RN252AT2.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@tanstack_query-devtools_build_26ded6e3._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@tanstack/query-devtools/build/DevtoolsPanelComponent/RN252AT2.js [app-ssr] (ecmascript)");
    });
});
}),
"[turbopack]/browser/dev/hmr-client/hmr-client.ts [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/[turbopack]_browser_dev_hmr-client_hmr-client_ts_59fa4ecd._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[turbopack]/browser/dev/hmr-client/hmr-client.ts [app-ssr] (ecmascript)");
    });
});
}),

};