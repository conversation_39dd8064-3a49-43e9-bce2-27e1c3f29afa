
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * WARNING: This is an internal file that is subject to change!
 *
 * 🛑 Under no circumstances should you import this file directly! 🛑
 *
 * All exports from this file are wrapped under a `Prisma` namespace object in the client.ts file.
 * While this enables partial backward compatibility, it is not part of the stable public API.
 *
 * If you are looking for your Models, Enums, and Input Types, please import them from the respective
 * model files in the `model` directory!
 */

import * as runtime from "@prisma/client/runtime/library"
import type * as Prisma from "../models"
import { type PrismaClient } from "./class"

export type * from '../models'

export type DMMF = typeof runtime.DMMF

export type PrismaPromise<T> = runtime.Types.Public.PrismaPromise<T>

/**
 * Validator
 */
export const validator = runtime.Public.validator

/**
 * Prisma Errors
 */

export const PrismaClientKnownRequestError = runtime.PrismaClientKnownRequestError
export type PrismaClientKnownRequestError = runtime.PrismaClientKnownRequestError

export const PrismaClientUnknownRequestError = runtime.PrismaClientUnknownRequestError
export type PrismaClientUnknownRequestError = runtime.PrismaClientUnknownRequestError

export const PrismaClientRustPanicError = runtime.PrismaClientRustPanicError
export type PrismaClientRustPanicError = runtime.PrismaClientRustPanicError

export const PrismaClientInitializationError = runtime.PrismaClientInitializationError
export type PrismaClientInitializationError = runtime.PrismaClientInitializationError

export const PrismaClientValidationError = runtime.PrismaClientValidationError
export type PrismaClientValidationError = runtime.PrismaClientValidationError

/**
 * Re-export of sql-template-tag
 */
export const sql = runtime.sqltag
export const empty = runtime.empty
export const join = runtime.join
export const raw = runtime.raw
export const Sql = runtime.Sql
export type Sql = runtime.Sql



/**
 * Decimal.js
 */
export const Decimal = runtime.Decimal
export type Decimal = runtime.Decimal

export type DecimalJsLike = runtime.DecimalJsLike

/**
 * Metrics
 */
export type Metrics = runtime.Metrics
export type Metric<T> = runtime.Metric<T>
export type MetricHistogram = runtime.MetricHistogram
export type MetricHistogramBucket = runtime.MetricHistogramBucket

/**
* Extensions
*/
export type Extension = runtime.Types.Extensions.UserArgs
export const getExtensionContext = runtime.Extensions.getExtensionContext
export type Args<T, F extends runtime.Operation> = runtime.Types.Public.Args<T, F>
export type Payload<T, F extends runtime.Operation = never> = runtime.Types.Public.Payload<T, F>
export type Result<T, A, F extends runtime.Operation> = runtime.Types.Public.Result<T, A, F>
export type Exact<A, W> = runtime.Types.Public.Exact<A, W>

export type PrismaVersion = {
  client: string
  engine: string
}

/**
 * Prisma Client JS version: 6.13.0
 * Query Engine version: 361e86d0ea4987e9f53a565309b3eed797a6bcbd
 */
export const prismaVersion: PrismaVersion = {
  client: "6.13.0",
  engine: "361e86d0ea4987e9f53a565309b3eed797a6bcbd"
}

/**
 * Utility Types
 */

export type JsonObject = runtime.JsonObject
export type JsonArray = runtime.JsonArray
export type JsonValue = runtime.JsonValue
export type InputJsonObject = runtime.InputJsonObject
export type InputJsonArray = runtime.InputJsonArray
export type InputJsonValue = runtime.InputJsonValue

export const NullTypes = {
  DbNull: runtime.objectEnumValues.classes.DbNull as (new (secret: never) => typeof runtime.objectEnumValues.instances.DbNull),
  JsonNull: runtime.objectEnumValues.classes.JsonNull as (new (secret: never) => typeof runtime.objectEnumValues.instances.JsonNull),
  AnyNull: runtime.objectEnumValues.classes.AnyNull as (new (secret: never) => typeof runtime.objectEnumValues.instances.AnyNull),
}

/**
 * Helper for filtering JSON entries that have `null` on the database (empty on the db)
 *
 * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
 */
export const DbNull = runtime.objectEnumValues.instances.DbNull

/**
 * Helper for filtering JSON entries that have JSON `null` values (not empty on the db)
 *
 * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
 */
export const JsonNull = runtime.objectEnumValues.instances.JsonNull

/**
 * Helper for filtering JSON entries that are `Prisma.DbNull` or `Prisma.JsonNull`
 *
 * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
 */
export const AnyNull = runtime.objectEnumValues.instances.AnyNull

type SelectAndInclude = {
  select: any
  include: any
}

type SelectAndOmit = {
  select: any
  omit: any
}

/**
 * From T, pick a set of properties whose keys are in the union K
 */
type Prisma__Pick<T, K extends keyof T> = {
    [P in K]: T[P];
};

export type Enumerable<T> = T | Array<T>;

/**
 * Subset
 * @desc From `T` pick properties that exist in `U`. Simple version of Intersection
 */
export type Subset<T, U> = {
  [key in keyof T]: key extends keyof U ? T[key] : never;
};

/**
 * SelectSubset
 * @desc From `T` pick properties that exist in `U`. Simple version of Intersection.
 * Additionally, it validates, if both select and include are present. If the case, it errors.
 */
export type SelectSubset<T, U> = {
  [key in keyof T]: key extends keyof U ? T[key] : never
} &
  (T extends SelectAndInclude
    ? 'Please either choose `select` or `include`.'
    : T extends SelectAndOmit
      ? 'Please either choose `select` or `omit`.'
      : {})

/**
 * Subset + Intersection
 * @desc From `T` pick properties that exist in `U` and intersect `K`
 */
export type SubsetIntersection<T, U, K> = {
  [key in keyof T]: key extends keyof U ? T[key] : never
} &
  K

type Without<T, U> = { [P in Exclude<keyof T, keyof U>]?: never };

/**
 * XOR is needed to have a real mutually exclusive union type
 * https://stackoverflow.com/questions/42123407/does-typescript-support-mutually-exclusive-types
 */
export type XOR<T, U> =
  T extends object ?
  U extends object ?
    (Without<T, U> & U) | (Without<U, T> & T)
  : U : T


/**
 * Is T a Record?
 */
type IsObject<T extends any> = T extends Array<any>
? False
: T extends Date
? False
: T extends Uint8Array
? False
: T extends BigInt
? False
: T extends object
? True
: False


/**
 * If it's T[], return T
 */
export type UnEnumerate<T extends unknown> = T extends Array<infer U> ? U : T

/**
 * From ts-toolbelt
 */

type __Either<O extends object, K extends Key> = Omit<O, K> &
  {
    // Merge all but K
    [P in K]: Prisma__Pick<O, P & keyof O> // With K possibilities
  }[K]

type EitherStrict<O extends object, K extends Key> = Strict<__Either<O, K>>

type EitherLoose<O extends object, K extends Key> = ComputeRaw<__Either<O, K>>

type _Either<
  O extends object,
  K extends Key,
  strict extends Boolean
> = {
  1: EitherStrict<O, K>
  0: EitherLoose<O, K>
}[strict]

export type Either<
  O extends object,
  K extends Key,
  strict extends Boolean = 1
> = O extends unknown ? _Either<O, K, strict> : never

export type Union = any

export type PatchUndefined<O extends object, O1 extends object> = {
  [K in keyof O]: O[K] extends undefined ? At<O1, K> : O[K]
} & {}

/** Helper Types for "Merge" **/
export type IntersectOf<U extends Union> = (
  U extends unknown ? (k: U) => void : never
) extends (k: infer I) => void
  ? I
  : never

export type Overwrite<O extends object, O1 extends object> = {
    [K in keyof O]: K extends keyof O1 ? O1[K] : O[K];
} & {};

type _Merge<U extends object> = IntersectOf<Overwrite<U, {
    [K in keyof U]-?: At<U, K>;
}>>;

type Key = string | number | symbol;
type AtStrict<O extends object, K extends Key> = O[K & keyof O];
type AtLoose<O extends object, K extends Key> = O extends unknown ? AtStrict<O, K> : never;
export type At<O extends object, K extends Key, strict extends Boolean = 1> = {
    1: AtStrict<O, K>;
    0: AtLoose<O, K>;
}[strict];

export type ComputeRaw<A extends any> = A extends Function ? A : {
  [K in keyof A]: A[K];
} & {};

export type OptionalFlat<O> = {
  [K in keyof O]?: O[K];
} & {};

type _Record<K extends keyof any, T> = {
  [P in K]: T;
};

// cause typescript not to expand types and preserve names
type NoExpand<T> = T extends unknown ? T : never;

// this type assumes the passed object is entirely optional
export type AtLeast<O extends object, K extends string> = NoExpand<
  O extends unknown
  ? | (K extends keyof O ? { [P in K]: O[P] } & O : O)
    | {[P in keyof O as P extends K ? P : never]-?: O[P]} & O
  : never>;

type _Strict<U, _U = U> = U extends unknown ? U & OptionalFlat<_Record<Exclude<Keys<_U>, keyof U>, never>> : never;

export type Strict<U extends object> = ComputeRaw<_Strict<U>>;
/** End Helper Types for "Merge" **/

export type Merge<U extends object> = ComputeRaw<_Merge<Strict<U>>>;

export type Boolean = True | False

export type True = 1

export type False = 0

export type Not<B extends Boolean> = {
  0: 1
  1: 0
}[B]

export type Extends<A1 extends any, A2 extends any> = [A1] extends [never]
  ? 0 // anything `never` is false
  : A1 extends A2
  ? 1
  : 0

export type Has<U extends Union, U1 extends Union> = Not<
  Extends<Exclude<U1, U>, U1>
>

export type Or<B1 extends Boolean, B2 extends Boolean> = {
  0: {
    0: 0
    1: 1
  }
  1: {
    0: 1
    1: 1
  }
}[B1][B2]

export type Keys<U extends Union> = U extends unknown ? keyof U : never

export type GetScalarType<T, O> = O extends object ? {
  [P in keyof T]: P extends keyof O
    ? O[P]
    : never
} : never

type FieldPaths<
  T,
  U = Omit<T, '_avg' | '_sum' | '_count' | '_min' | '_max'>
> = IsObject<T> extends True ? U : T

export type GetHavingFields<T> = {
  [K in keyof T]: Or<
    Or<Extends<'OR', K>, Extends<'AND', K>>,
    Extends<'NOT', K>
  > extends True
    ? // infer is only needed to not hit TS limit
      // based on the brilliant idea of Pierre-Antoine Mills
      // https://github.com/microsoft/TypeScript/issues/30188#issuecomment-478938437
      T[K] extends infer TK
      ? GetHavingFields<UnEnumerate<TK> extends object ? Merge<UnEnumerate<TK>> : never>
      : never
    : {} extends FieldPaths<T[K]>
    ? never
    : K
}[keyof T]

/**
 * Convert tuple to union
 */
type _TupleToUnion<T> = T extends (infer E)[] ? E : never
type TupleToUnion<K extends readonly any[]> = _TupleToUnion<K>
export type MaybeTupleToUnion<T> = T extends any[] ? TupleToUnion<T> : T

/**
 * Like `Pick`, but additionally can also accept an array of keys
 */
export type PickEnumerable<T, K extends Enumerable<keyof T> | keyof T> = Prisma__Pick<T, MaybeTupleToUnion<K>>

/**
 * Exclude all keys with underscores
 */
export type ExcludeUnderscoreKeys<T extends string> = T extends `_${string}` ? never : T


export type FieldRef<Model, FieldType> = runtime.FieldRef<Model, FieldType>

type FieldRefInputType<Model, FieldType> = Model extends never ? never : FieldRef<Model, FieldType>


export const ModelName = {
  User: 'User',
  Session: 'Session',
  Account: 'Account',
  Verification: 'Verification',
  Organization: 'Organization',
  Member: 'Member',
  Invitation: 'Invitation',
  TwoFactor: 'TwoFactor',
  Passkey: 'Passkey',
  AlumniProfile: 'AlumniProfile',
  Connection: 'Connection',
  Message: 'Message',
  Post: 'Post',
  PostLike: 'PostLike',
  Comment: 'Comment',
  Event: 'Event',
  EventRegistration: 'EventRegistration',
  Donation: 'Donation',
  NewsArticle: 'NewsArticle',
  Notification: 'Notification'
} as const

export type ModelName = (typeof ModelName)[keyof typeof ModelName]



export interface TypeMapCb<ClientOptions = {}> extends runtime.Types.Utils.Fn<{extArgs: runtime.Types.Extensions.InternalArgs }, runtime.Types.Utils.Record<string, any>> {
  returns: TypeMap<this['params']['extArgs'], ClientOptions extends { omit: infer OmitOptions } ? OmitOptions : {}>
}

export type TypeMap<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> = {
  globalOmitOptions: {
    omit: GlobalOmitOptions
  }
  meta: {
    modelProps: "user" | "session" | "account" | "verification" | "organization" | "member" | "invitation" | "twoFactor" | "passkey" | "alumniProfile" | "connection" | "message" | "post" | "postLike" | "comment" | "event" | "eventRegistration" | "donation" | "newsArticle" | "notification"
    txIsolationLevel: TransactionIsolationLevel
  }
  model: {
    User: {
      payload: Prisma.$UserPayload<ExtArgs>
      fields: Prisma.UserFieldRefs
      operations: {
        findUnique: {
          args: Prisma.UserFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UserPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.UserFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UserPayload>
        }
        findFirst: {
          args: Prisma.UserFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UserPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.UserFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UserPayload>
        }
        findMany: {
          args: Prisma.UserFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UserPayload>[]
        }
        create: {
          args: Prisma.UserCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UserPayload>
        }
        createMany: {
          args: Prisma.UserCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.UserDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UserPayload>
        }
        update: {
          args: Prisma.UserUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UserPayload>
        }
        deleteMany: {
          args: Prisma.UserDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.UserUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.UserUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$UserPayload>
        }
        aggregate: {
          args: Prisma.UserAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateUser>
        }
        groupBy: {
          args: Prisma.UserGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.UserGroupByOutputType>[]
        }
        count: {
          args: Prisma.UserCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.UserCountAggregateOutputType> | number
        }
      }
    }
    Session: {
      payload: Prisma.$SessionPayload<ExtArgs>
      fields: Prisma.SessionFieldRefs
      operations: {
        findUnique: {
          args: Prisma.SessionFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SessionPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.SessionFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SessionPayload>
        }
        findFirst: {
          args: Prisma.SessionFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SessionPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.SessionFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SessionPayload>
        }
        findMany: {
          args: Prisma.SessionFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SessionPayload>[]
        }
        create: {
          args: Prisma.SessionCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SessionPayload>
        }
        createMany: {
          args: Prisma.SessionCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.SessionDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SessionPayload>
        }
        update: {
          args: Prisma.SessionUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SessionPayload>
        }
        deleteMany: {
          args: Prisma.SessionDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.SessionUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.SessionUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$SessionPayload>
        }
        aggregate: {
          args: Prisma.SessionAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateSession>
        }
        groupBy: {
          args: Prisma.SessionGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.SessionGroupByOutputType>[]
        }
        count: {
          args: Prisma.SessionCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.SessionCountAggregateOutputType> | number
        }
      }
    }
    Account: {
      payload: Prisma.$AccountPayload<ExtArgs>
      fields: Prisma.AccountFieldRefs
      operations: {
        findUnique: {
          args: Prisma.AccountFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AccountPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.AccountFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AccountPayload>
        }
        findFirst: {
          args: Prisma.AccountFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AccountPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.AccountFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AccountPayload>
        }
        findMany: {
          args: Prisma.AccountFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AccountPayload>[]
        }
        create: {
          args: Prisma.AccountCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AccountPayload>
        }
        createMany: {
          args: Prisma.AccountCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.AccountDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AccountPayload>
        }
        update: {
          args: Prisma.AccountUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AccountPayload>
        }
        deleteMany: {
          args: Prisma.AccountDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.AccountUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.AccountUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AccountPayload>
        }
        aggregate: {
          args: Prisma.AccountAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateAccount>
        }
        groupBy: {
          args: Prisma.AccountGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AccountGroupByOutputType>[]
        }
        count: {
          args: Prisma.AccountCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AccountCountAggregateOutputType> | number
        }
      }
    }
    Verification: {
      payload: Prisma.$VerificationPayload<ExtArgs>
      fields: Prisma.VerificationFieldRefs
      operations: {
        findUnique: {
          args: Prisma.VerificationFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$VerificationPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.VerificationFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$VerificationPayload>
        }
        findFirst: {
          args: Prisma.VerificationFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$VerificationPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.VerificationFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$VerificationPayload>
        }
        findMany: {
          args: Prisma.VerificationFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$VerificationPayload>[]
        }
        create: {
          args: Prisma.VerificationCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$VerificationPayload>
        }
        createMany: {
          args: Prisma.VerificationCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.VerificationDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$VerificationPayload>
        }
        update: {
          args: Prisma.VerificationUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$VerificationPayload>
        }
        deleteMany: {
          args: Prisma.VerificationDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.VerificationUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.VerificationUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$VerificationPayload>
        }
        aggregate: {
          args: Prisma.VerificationAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateVerification>
        }
        groupBy: {
          args: Prisma.VerificationGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.VerificationGroupByOutputType>[]
        }
        count: {
          args: Prisma.VerificationCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.VerificationCountAggregateOutputType> | number
        }
      }
    }
    Organization: {
      payload: Prisma.$OrganizationPayload<ExtArgs>
      fields: Prisma.OrganizationFieldRefs
      operations: {
        findUnique: {
          args: Prisma.OrganizationFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$OrganizationPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.OrganizationFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$OrganizationPayload>
        }
        findFirst: {
          args: Prisma.OrganizationFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$OrganizationPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.OrganizationFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$OrganizationPayload>
        }
        findMany: {
          args: Prisma.OrganizationFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$OrganizationPayload>[]
        }
        create: {
          args: Prisma.OrganizationCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$OrganizationPayload>
        }
        createMany: {
          args: Prisma.OrganizationCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.OrganizationDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$OrganizationPayload>
        }
        update: {
          args: Prisma.OrganizationUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$OrganizationPayload>
        }
        deleteMany: {
          args: Prisma.OrganizationDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.OrganizationUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.OrganizationUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$OrganizationPayload>
        }
        aggregate: {
          args: Prisma.OrganizationAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateOrganization>
        }
        groupBy: {
          args: Prisma.OrganizationGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.OrganizationGroupByOutputType>[]
        }
        count: {
          args: Prisma.OrganizationCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.OrganizationCountAggregateOutputType> | number
        }
      }
    }
    Member: {
      payload: Prisma.$MemberPayload<ExtArgs>
      fields: Prisma.MemberFieldRefs
      operations: {
        findUnique: {
          args: Prisma.MemberFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MemberPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.MemberFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MemberPayload>
        }
        findFirst: {
          args: Prisma.MemberFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MemberPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.MemberFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MemberPayload>
        }
        findMany: {
          args: Prisma.MemberFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MemberPayload>[]
        }
        create: {
          args: Prisma.MemberCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MemberPayload>
        }
        createMany: {
          args: Prisma.MemberCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.MemberDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MemberPayload>
        }
        update: {
          args: Prisma.MemberUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MemberPayload>
        }
        deleteMany: {
          args: Prisma.MemberDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.MemberUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.MemberUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MemberPayload>
        }
        aggregate: {
          args: Prisma.MemberAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateMember>
        }
        groupBy: {
          args: Prisma.MemberGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.MemberGroupByOutputType>[]
        }
        count: {
          args: Prisma.MemberCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.MemberCountAggregateOutputType> | number
        }
      }
    }
    Invitation: {
      payload: Prisma.$InvitationPayload<ExtArgs>
      fields: Prisma.InvitationFieldRefs
      operations: {
        findUnique: {
          args: Prisma.InvitationFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$InvitationPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.InvitationFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$InvitationPayload>
        }
        findFirst: {
          args: Prisma.InvitationFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$InvitationPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.InvitationFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$InvitationPayload>
        }
        findMany: {
          args: Prisma.InvitationFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$InvitationPayload>[]
        }
        create: {
          args: Prisma.InvitationCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$InvitationPayload>
        }
        createMany: {
          args: Prisma.InvitationCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.InvitationDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$InvitationPayload>
        }
        update: {
          args: Prisma.InvitationUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$InvitationPayload>
        }
        deleteMany: {
          args: Prisma.InvitationDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.InvitationUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.InvitationUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$InvitationPayload>
        }
        aggregate: {
          args: Prisma.InvitationAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateInvitation>
        }
        groupBy: {
          args: Prisma.InvitationGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.InvitationGroupByOutputType>[]
        }
        count: {
          args: Prisma.InvitationCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.InvitationCountAggregateOutputType> | number
        }
      }
    }
    TwoFactor: {
      payload: Prisma.$TwoFactorPayload<ExtArgs>
      fields: Prisma.TwoFactorFieldRefs
      operations: {
        findUnique: {
          args: Prisma.TwoFactorFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TwoFactorPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.TwoFactorFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TwoFactorPayload>
        }
        findFirst: {
          args: Prisma.TwoFactorFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TwoFactorPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.TwoFactorFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TwoFactorPayload>
        }
        findMany: {
          args: Prisma.TwoFactorFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TwoFactorPayload>[]
        }
        create: {
          args: Prisma.TwoFactorCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TwoFactorPayload>
        }
        createMany: {
          args: Prisma.TwoFactorCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.TwoFactorDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TwoFactorPayload>
        }
        update: {
          args: Prisma.TwoFactorUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TwoFactorPayload>
        }
        deleteMany: {
          args: Prisma.TwoFactorDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.TwoFactorUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.TwoFactorUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$TwoFactorPayload>
        }
        aggregate: {
          args: Prisma.TwoFactorAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateTwoFactor>
        }
        groupBy: {
          args: Prisma.TwoFactorGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.TwoFactorGroupByOutputType>[]
        }
        count: {
          args: Prisma.TwoFactorCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.TwoFactorCountAggregateOutputType> | number
        }
      }
    }
    Passkey: {
      payload: Prisma.$PasskeyPayload<ExtArgs>
      fields: Prisma.PasskeyFieldRefs
      operations: {
        findUnique: {
          args: Prisma.PasskeyFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PasskeyPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.PasskeyFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PasskeyPayload>
        }
        findFirst: {
          args: Prisma.PasskeyFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PasskeyPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.PasskeyFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PasskeyPayload>
        }
        findMany: {
          args: Prisma.PasskeyFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PasskeyPayload>[]
        }
        create: {
          args: Prisma.PasskeyCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PasskeyPayload>
        }
        createMany: {
          args: Prisma.PasskeyCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.PasskeyDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PasskeyPayload>
        }
        update: {
          args: Prisma.PasskeyUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PasskeyPayload>
        }
        deleteMany: {
          args: Prisma.PasskeyDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.PasskeyUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.PasskeyUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PasskeyPayload>
        }
        aggregate: {
          args: Prisma.PasskeyAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregatePasskey>
        }
        groupBy: {
          args: Prisma.PasskeyGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.PasskeyGroupByOutputType>[]
        }
        count: {
          args: Prisma.PasskeyCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.PasskeyCountAggregateOutputType> | number
        }
      }
    }
    AlumniProfile: {
      payload: Prisma.$AlumniProfilePayload<ExtArgs>
      fields: Prisma.AlumniProfileFieldRefs
      operations: {
        findUnique: {
          args: Prisma.AlumniProfileFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AlumniProfilePayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.AlumniProfileFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AlumniProfilePayload>
        }
        findFirst: {
          args: Prisma.AlumniProfileFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AlumniProfilePayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.AlumniProfileFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AlumniProfilePayload>
        }
        findMany: {
          args: Prisma.AlumniProfileFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AlumniProfilePayload>[]
        }
        create: {
          args: Prisma.AlumniProfileCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AlumniProfilePayload>
        }
        createMany: {
          args: Prisma.AlumniProfileCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.AlumniProfileDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AlumniProfilePayload>
        }
        update: {
          args: Prisma.AlumniProfileUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AlumniProfilePayload>
        }
        deleteMany: {
          args: Prisma.AlumniProfileDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.AlumniProfileUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.AlumniProfileUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$AlumniProfilePayload>
        }
        aggregate: {
          args: Prisma.AlumniProfileAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateAlumniProfile>
        }
        groupBy: {
          args: Prisma.AlumniProfileGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AlumniProfileGroupByOutputType>[]
        }
        count: {
          args: Prisma.AlumniProfileCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AlumniProfileCountAggregateOutputType> | number
        }
      }
    }
    Connection: {
      payload: Prisma.$ConnectionPayload<ExtArgs>
      fields: Prisma.ConnectionFieldRefs
      operations: {
        findUnique: {
          args: Prisma.ConnectionFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ConnectionPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.ConnectionFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ConnectionPayload>
        }
        findFirst: {
          args: Prisma.ConnectionFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ConnectionPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.ConnectionFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ConnectionPayload>
        }
        findMany: {
          args: Prisma.ConnectionFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ConnectionPayload>[]
        }
        create: {
          args: Prisma.ConnectionCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ConnectionPayload>
        }
        createMany: {
          args: Prisma.ConnectionCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.ConnectionDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ConnectionPayload>
        }
        update: {
          args: Prisma.ConnectionUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ConnectionPayload>
        }
        deleteMany: {
          args: Prisma.ConnectionDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.ConnectionUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.ConnectionUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$ConnectionPayload>
        }
        aggregate: {
          args: Prisma.ConnectionAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateConnection>
        }
        groupBy: {
          args: Prisma.ConnectionGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.ConnectionGroupByOutputType>[]
        }
        count: {
          args: Prisma.ConnectionCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.ConnectionCountAggregateOutputType> | number
        }
      }
    }
    Message: {
      payload: Prisma.$MessagePayload<ExtArgs>
      fields: Prisma.MessageFieldRefs
      operations: {
        findUnique: {
          args: Prisma.MessageFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MessagePayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.MessageFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MessagePayload>
        }
        findFirst: {
          args: Prisma.MessageFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MessagePayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.MessageFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MessagePayload>
        }
        findMany: {
          args: Prisma.MessageFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MessagePayload>[]
        }
        create: {
          args: Prisma.MessageCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MessagePayload>
        }
        createMany: {
          args: Prisma.MessageCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.MessageDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MessagePayload>
        }
        update: {
          args: Prisma.MessageUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MessagePayload>
        }
        deleteMany: {
          args: Prisma.MessageDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.MessageUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.MessageUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$MessagePayload>
        }
        aggregate: {
          args: Prisma.MessageAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateMessage>
        }
        groupBy: {
          args: Prisma.MessageGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.MessageGroupByOutputType>[]
        }
        count: {
          args: Prisma.MessageCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.MessageCountAggregateOutputType> | number
        }
      }
    }
    Post: {
      payload: Prisma.$PostPayload<ExtArgs>
      fields: Prisma.PostFieldRefs
      operations: {
        findUnique: {
          args: Prisma.PostFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PostPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.PostFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PostPayload>
        }
        findFirst: {
          args: Prisma.PostFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PostPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.PostFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PostPayload>
        }
        findMany: {
          args: Prisma.PostFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PostPayload>[]
        }
        create: {
          args: Prisma.PostCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PostPayload>
        }
        createMany: {
          args: Prisma.PostCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.PostDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PostPayload>
        }
        update: {
          args: Prisma.PostUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PostPayload>
        }
        deleteMany: {
          args: Prisma.PostDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.PostUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.PostUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PostPayload>
        }
        aggregate: {
          args: Prisma.PostAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregatePost>
        }
        groupBy: {
          args: Prisma.PostGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.PostGroupByOutputType>[]
        }
        count: {
          args: Prisma.PostCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.PostCountAggregateOutputType> | number
        }
      }
    }
    PostLike: {
      payload: Prisma.$PostLikePayload<ExtArgs>
      fields: Prisma.PostLikeFieldRefs
      operations: {
        findUnique: {
          args: Prisma.PostLikeFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PostLikePayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.PostLikeFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PostLikePayload>
        }
        findFirst: {
          args: Prisma.PostLikeFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PostLikePayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.PostLikeFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PostLikePayload>
        }
        findMany: {
          args: Prisma.PostLikeFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PostLikePayload>[]
        }
        create: {
          args: Prisma.PostLikeCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PostLikePayload>
        }
        createMany: {
          args: Prisma.PostLikeCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.PostLikeDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PostLikePayload>
        }
        update: {
          args: Prisma.PostLikeUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PostLikePayload>
        }
        deleteMany: {
          args: Prisma.PostLikeDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.PostLikeUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.PostLikeUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$PostLikePayload>
        }
        aggregate: {
          args: Prisma.PostLikeAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregatePostLike>
        }
        groupBy: {
          args: Prisma.PostLikeGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.PostLikeGroupByOutputType>[]
        }
        count: {
          args: Prisma.PostLikeCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.PostLikeCountAggregateOutputType> | number
        }
      }
    }
    Comment: {
      payload: Prisma.$CommentPayload<ExtArgs>
      fields: Prisma.CommentFieldRefs
      operations: {
        findUnique: {
          args: Prisma.CommentFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$CommentPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.CommentFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$CommentPayload>
        }
        findFirst: {
          args: Prisma.CommentFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$CommentPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.CommentFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$CommentPayload>
        }
        findMany: {
          args: Prisma.CommentFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$CommentPayload>[]
        }
        create: {
          args: Prisma.CommentCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$CommentPayload>
        }
        createMany: {
          args: Prisma.CommentCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.CommentDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$CommentPayload>
        }
        update: {
          args: Prisma.CommentUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$CommentPayload>
        }
        deleteMany: {
          args: Prisma.CommentDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.CommentUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.CommentUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$CommentPayload>
        }
        aggregate: {
          args: Prisma.CommentAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateComment>
        }
        groupBy: {
          args: Prisma.CommentGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.CommentGroupByOutputType>[]
        }
        count: {
          args: Prisma.CommentCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.CommentCountAggregateOutputType> | number
        }
      }
    }
    Event: {
      payload: Prisma.$EventPayload<ExtArgs>
      fields: Prisma.EventFieldRefs
      operations: {
        findUnique: {
          args: Prisma.EventFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$EventPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.EventFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$EventPayload>
        }
        findFirst: {
          args: Prisma.EventFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$EventPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.EventFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$EventPayload>
        }
        findMany: {
          args: Prisma.EventFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$EventPayload>[]
        }
        create: {
          args: Prisma.EventCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$EventPayload>
        }
        createMany: {
          args: Prisma.EventCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.EventDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$EventPayload>
        }
        update: {
          args: Prisma.EventUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$EventPayload>
        }
        deleteMany: {
          args: Prisma.EventDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.EventUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.EventUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$EventPayload>
        }
        aggregate: {
          args: Prisma.EventAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateEvent>
        }
        groupBy: {
          args: Prisma.EventGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.EventGroupByOutputType>[]
        }
        count: {
          args: Prisma.EventCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.EventCountAggregateOutputType> | number
        }
      }
    }
    EventRegistration: {
      payload: Prisma.$EventRegistrationPayload<ExtArgs>
      fields: Prisma.EventRegistrationFieldRefs
      operations: {
        findUnique: {
          args: Prisma.EventRegistrationFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$EventRegistrationPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.EventRegistrationFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$EventRegistrationPayload>
        }
        findFirst: {
          args: Prisma.EventRegistrationFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$EventRegistrationPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.EventRegistrationFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$EventRegistrationPayload>
        }
        findMany: {
          args: Prisma.EventRegistrationFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$EventRegistrationPayload>[]
        }
        create: {
          args: Prisma.EventRegistrationCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$EventRegistrationPayload>
        }
        createMany: {
          args: Prisma.EventRegistrationCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.EventRegistrationDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$EventRegistrationPayload>
        }
        update: {
          args: Prisma.EventRegistrationUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$EventRegistrationPayload>
        }
        deleteMany: {
          args: Prisma.EventRegistrationDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.EventRegistrationUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.EventRegistrationUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$EventRegistrationPayload>
        }
        aggregate: {
          args: Prisma.EventRegistrationAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateEventRegistration>
        }
        groupBy: {
          args: Prisma.EventRegistrationGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.EventRegistrationGroupByOutputType>[]
        }
        count: {
          args: Prisma.EventRegistrationCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.EventRegistrationCountAggregateOutputType> | number
        }
      }
    }
    Donation: {
      payload: Prisma.$DonationPayload<ExtArgs>
      fields: Prisma.DonationFieldRefs
      operations: {
        findUnique: {
          args: Prisma.DonationFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$DonationPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.DonationFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$DonationPayload>
        }
        findFirst: {
          args: Prisma.DonationFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$DonationPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.DonationFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$DonationPayload>
        }
        findMany: {
          args: Prisma.DonationFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$DonationPayload>[]
        }
        create: {
          args: Prisma.DonationCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$DonationPayload>
        }
        createMany: {
          args: Prisma.DonationCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.DonationDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$DonationPayload>
        }
        update: {
          args: Prisma.DonationUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$DonationPayload>
        }
        deleteMany: {
          args: Prisma.DonationDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.DonationUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.DonationUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$DonationPayload>
        }
        aggregate: {
          args: Prisma.DonationAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateDonation>
        }
        groupBy: {
          args: Prisma.DonationGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.DonationGroupByOutputType>[]
        }
        count: {
          args: Prisma.DonationCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.DonationCountAggregateOutputType> | number
        }
      }
    }
    NewsArticle: {
      payload: Prisma.$NewsArticlePayload<ExtArgs>
      fields: Prisma.NewsArticleFieldRefs
      operations: {
        findUnique: {
          args: Prisma.NewsArticleFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$NewsArticlePayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.NewsArticleFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$NewsArticlePayload>
        }
        findFirst: {
          args: Prisma.NewsArticleFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$NewsArticlePayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.NewsArticleFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$NewsArticlePayload>
        }
        findMany: {
          args: Prisma.NewsArticleFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$NewsArticlePayload>[]
        }
        create: {
          args: Prisma.NewsArticleCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$NewsArticlePayload>
        }
        createMany: {
          args: Prisma.NewsArticleCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.NewsArticleDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$NewsArticlePayload>
        }
        update: {
          args: Prisma.NewsArticleUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$NewsArticlePayload>
        }
        deleteMany: {
          args: Prisma.NewsArticleDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.NewsArticleUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.NewsArticleUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$NewsArticlePayload>
        }
        aggregate: {
          args: Prisma.NewsArticleAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateNewsArticle>
        }
        groupBy: {
          args: Prisma.NewsArticleGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.NewsArticleGroupByOutputType>[]
        }
        count: {
          args: Prisma.NewsArticleCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.NewsArticleCountAggregateOutputType> | number
        }
      }
    }
    Notification: {
      payload: Prisma.$NotificationPayload<ExtArgs>
      fields: Prisma.NotificationFieldRefs
      operations: {
        findUnique: {
          args: Prisma.NotificationFindUniqueArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$NotificationPayload> | null
        }
        findUniqueOrThrow: {
          args: Prisma.NotificationFindUniqueOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$NotificationPayload>
        }
        findFirst: {
          args: Prisma.NotificationFindFirstArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$NotificationPayload> | null
        }
        findFirstOrThrow: {
          args: Prisma.NotificationFindFirstOrThrowArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$NotificationPayload>
        }
        findMany: {
          args: Prisma.NotificationFindManyArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$NotificationPayload>[]
        }
        create: {
          args: Prisma.NotificationCreateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$NotificationPayload>
        }
        createMany: {
          args: Prisma.NotificationCreateManyArgs<ExtArgs>
          result: BatchPayload
        }
        delete: {
          args: Prisma.NotificationDeleteArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$NotificationPayload>
        }
        update: {
          args: Prisma.NotificationUpdateArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$NotificationPayload>
        }
        deleteMany: {
          args: Prisma.NotificationDeleteManyArgs<ExtArgs>
          result: BatchPayload
        }
        updateMany: {
          args: Prisma.NotificationUpdateManyArgs<ExtArgs>
          result: BatchPayload
        }
        upsert: {
          args: Prisma.NotificationUpsertArgs<ExtArgs>
          result: runtime.Types.Utils.PayloadToResult<Prisma.$NotificationPayload>
        }
        aggregate: {
          args: Prisma.NotificationAggregateArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.AggregateNotification>
        }
        groupBy: {
          args: Prisma.NotificationGroupByArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.NotificationGroupByOutputType>[]
        }
        count: {
          args: Prisma.NotificationCountArgs<ExtArgs>
          result: runtime.Types.Utils.Optional<Prisma.NotificationCountAggregateOutputType> | number
        }
      }
    }
  }
} & {
  other: {
    payload: any
    operations: {
      $executeRaw: {
        args: [query: TemplateStringsArray | Sql, ...values: any[]],
        result: any
      }
      $executeRawUnsafe: {
        args: [query: string, ...values: any[]],
        result: any
      }
      $queryRaw: {
        args: [query: TemplateStringsArray | Sql, ...values: any[]],
        result: any
      }
      $queryRawUnsafe: {
        args: [query: string, ...values: any[]],
        result: any
      }
    }
  }
}

/**
 * Enums
 */

export const TransactionIsolationLevel = runtime.makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
} as const)

export type TransactionIsolationLevel = (typeof TransactionIsolationLevel)[keyof typeof TransactionIsolationLevel]


export const UserScalarFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  emailVerified: 'emailVerified',
  image: 'image',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type UserScalarFieldEnum = (typeof UserScalarFieldEnum)[keyof typeof UserScalarFieldEnum]


export const SessionScalarFieldEnum = {
  id: 'id',
  expiresAt: 'expiresAt',
  token: 'token',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  userId: 'userId'
} as const

export type SessionScalarFieldEnum = (typeof SessionScalarFieldEnum)[keyof typeof SessionScalarFieldEnum]


export const AccountScalarFieldEnum = {
  id: 'id',
  accountId: 'accountId',
  providerId: 'providerId',
  userId: 'userId',
  accessToken: 'accessToken',
  refreshToken: 'refreshToken',
  idToken: 'idToken',
  accessTokenExpiresAt: 'accessTokenExpiresAt',
  refreshTokenExpiresAt: 'refreshTokenExpiresAt',
  scope: 'scope',
  password: 'password',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type AccountScalarFieldEnum = (typeof AccountScalarFieldEnum)[keyof typeof AccountScalarFieldEnum]


export const VerificationScalarFieldEnum = {
  id: 'id',
  identifier: 'identifier',
  value: 'value',
  expiresAt: 'expiresAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type VerificationScalarFieldEnum = (typeof VerificationScalarFieldEnum)[keyof typeof VerificationScalarFieldEnum]


export const OrganizationScalarFieldEnum = {
  id: 'id',
  name: 'name',
  slug: 'slug',
  logo: 'logo',
  createdAt: 'createdAt',
  metadata: 'metadata'
} as const

export type OrganizationScalarFieldEnum = (typeof OrganizationScalarFieldEnum)[keyof typeof OrganizationScalarFieldEnum]


export const MemberScalarFieldEnum = {
  id: 'id',
  organizationId: 'organizationId',
  userId: 'userId',
  role: 'role',
  createdAt: 'createdAt'
} as const

export type MemberScalarFieldEnum = (typeof MemberScalarFieldEnum)[keyof typeof MemberScalarFieldEnum]


export const InvitationScalarFieldEnum = {
  id: 'id',
  organizationId: 'organizationId',
  email: 'email',
  role: 'role',
  status: 'status',
  expiresAt: 'expiresAt',
  inviterId: 'inviterId'
} as const

export type InvitationScalarFieldEnum = (typeof InvitationScalarFieldEnum)[keyof typeof InvitationScalarFieldEnum]


export const TwoFactorScalarFieldEnum = {
  id: 'id',
  secret: 'secret',
  backupCodes: 'backupCodes',
  userId: 'userId'
} as const

export type TwoFactorScalarFieldEnum = (typeof TwoFactorScalarFieldEnum)[keyof typeof TwoFactorScalarFieldEnum]


export const PasskeyScalarFieldEnum = {
  id: 'id',
  name: 'name',
  publicKey: 'publicKey',
  userId: 'userId',
  credentialID: 'credentialID',
  counter: 'counter',
  deviceType: 'deviceType',
  backedUp: 'backedUp',
  transports: 'transports',
  createdAt: 'createdAt'
} as const

export type PasskeyScalarFieldEnum = (typeof PasskeyScalarFieldEnum)[keyof typeof PasskeyScalarFieldEnum]


export const AlumniProfileScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  firstName: 'firstName',
  lastName: 'lastName',
  displayName: 'displayName',
  bio: 'bio',
  profilePicture: 'profilePicture',
  primaryOrganizationId: 'primaryOrganizationId',
  graduationYear: 'graduationYear',
  programType: 'programType',
  centerLocation: 'centerLocation',
  achievements: 'achievements',
  currentPosition: 'currentPosition',
  currentCompany: 'currentCompany',
  industry: 'industry',
  location: 'location',
  linkedInUrl: 'linkedInUrl',
  phoneNumber: 'phoneNumber',
  mentorshipOffered: 'mentorshipOffered',
  mentorshipSought: 'mentorshipSought',
  skillsOffered: 'skillsOffered',
  skillsWanted: 'skillsWanted',
  profileVisibility: 'profileVisibility',
  showEmail: 'showEmail',
  showPhone: 'showPhone',
  showLocation: 'showLocation',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type AlumniProfileScalarFieldEnum = (typeof AlumniProfileScalarFieldEnum)[keyof typeof AlumniProfileScalarFieldEnum]


export const ConnectionScalarFieldEnum = {
  id: 'id',
  requesterId: 'requesterId',
  requestedId: 'requestedId',
  status: 'status',
  requestedAt: 'requestedAt',
  acceptedAt: 'acceptedAt'
} as const

export type ConnectionScalarFieldEnum = (typeof ConnectionScalarFieldEnum)[keyof typeof ConnectionScalarFieldEnum]


export const MessageScalarFieldEnum = {
  id: 'id',
  senderId: 'senderId',
  receiverId: 'receiverId',
  content: 'content',
  isRead: 'isRead',
  createdAt: 'createdAt'
} as const

export type MessageScalarFieldEnum = (typeof MessageScalarFieldEnum)[keyof typeof MessageScalarFieldEnum]


export const PostScalarFieldEnum = {
  id: 'id',
  authorId: 'authorId',
  title: 'title',
  content: 'content',
  imageUrl: 'imageUrl',
  postType: 'postType',
  isPublished: 'isPublished',
  organizationId: 'organizationId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type PostScalarFieldEnum = (typeof PostScalarFieldEnum)[keyof typeof PostScalarFieldEnum]


export const PostLikeScalarFieldEnum = {
  id: 'id',
  postId: 'postId',
  userId: 'userId',
  createdAt: 'createdAt'
} as const

export type PostLikeScalarFieldEnum = (typeof PostLikeScalarFieldEnum)[keyof typeof PostLikeScalarFieldEnum]


export const CommentScalarFieldEnum = {
  id: 'id',
  postId: 'postId',
  authorId: 'authorId',
  content: 'content',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type CommentScalarFieldEnum = (typeof CommentScalarFieldEnum)[keyof typeof CommentScalarFieldEnum]


export const EventScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  eventType: 'eventType',
  location: 'location',
  isVirtual: 'isVirtual',
  virtualLink: 'virtualLink',
  startDateTime: 'startDateTime',
  endDateTime: 'endDateTime',
  maxAttendees: 'maxAttendees',
  imageUrl: 'imageUrl',
  organizationId: 'organizationId',
  organizerId: 'organizerId',
  isPublished: 'isPublished',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type EventScalarFieldEnum = (typeof EventScalarFieldEnum)[keyof typeof EventScalarFieldEnum]


export const EventRegistrationScalarFieldEnum = {
  id: 'id',
  eventId: 'eventId',
  attendeeId: 'attendeeId',
  status: 'status',
  registeredAt: 'registeredAt'
} as const

export type EventRegistrationScalarFieldEnum = (typeof EventRegistrationScalarFieldEnum)[keyof typeof EventRegistrationScalarFieldEnum]


export const DonationScalarFieldEnum = {
  id: 'id',
  donorId: 'donorId',
  amount: 'amount',
  currency: 'currency',
  donationType: 'donationType',
  organizationId: 'organizationId',
  paymentMethod: 'paymentMethod',
  transactionId: 'transactionId',
  paymentStatus: 'paymentStatus',
  purpose: 'purpose',
  isAnonymous: 'isAnonymous',
  recurringId: 'recurringId',
  nextDueDate: 'nextDueDate',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type DonationScalarFieldEnum = (typeof DonationScalarFieldEnum)[keyof typeof DonationScalarFieldEnum]


export const NewsArticleScalarFieldEnum = {
  id: 'id',
  title: 'title',
  content: 'content',
  excerpt: 'excerpt',
  imageUrl: 'imageUrl',
  authorName: 'authorName',
  category: 'category',
  isPublished: 'isPublished',
  publishedAt: 'publishedAt',
  organizationId: 'organizationId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type NewsArticleScalarFieldEnum = (typeof NewsArticleScalarFieldEnum)[keyof typeof NewsArticleScalarFieldEnum]


export const NotificationScalarFieldEnum = {
  id: 'id',
  recipientId: 'recipientId',
  type: 'type',
  title: 'title',
  message: 'message',
  isRead: 'isRead',
  actionUrl: 'actionUrl',
  createdAt: 'createdAt',
  organizationId: 'organizationId'
} as const

export type NotificationScalarFieldEnum = (typeof NotificationScalarFieldEnum)[keyof typeof NotificationScalarFieldEnum]


export const SortOrder = {
  asc: 'asc',
  desc: 'desc'
} as const

export type SortOrder = (typeof SortOrder)[keyof typeof SortOrder]


export const NullsOrder = {
  first: 'first',
  last: 'last'
} as const

export type NullsOrder = (typeof NullsOrder)[keyof typeof NullsOrder]


export const UserOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  image: 'image'
} as const

export type UserOrderByRelevanceFieldEnum = (typeof UserOrderByRelevanceFieldEnum)[keyof typeof UserOrderByRelevanceFieldEnum]


export const SessionOrderByRelevanceFieldEnum = {
  id: 'id',
  token: 'token',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  userId: 'userId'
} as const

export type SessionOrderByRelevanceFieldEnum = (typeof SessionOrderByRelevanceFieldEnum)[keyof typeof SessionOrderByRelevanceFieldEnum]


export const AccountOrderByRelevanceFieldEnum = {
  id: 'id',
  accountId: 'accountId',
  providerId: 'providerId',
  userId: 'userId',
  accessToken: 'accessToken',
  refreshToken: 'refreshToken',
  idToken: 'idToken',
  scope: 'scope',
  password: 'password'
} as const

export type AccountOrderByRelevanceFieldEnum = (typeof AccountOrderByRelevanceFieldEnum)[keyof typeof AccountOrderByRelevanceFieldEnum]


export const VerificationOrderByRelevanceFieldEnum = {
  id: 'id',
  identifier: 'identifier',
  value: 'value'
} as const

export type VerificationOrderByRelevanceFieldEnum = (typeof VerificationOrderByRelevanceFieldEnum)[keyof typeof VerificationOrderByRelevanceFieldEnum]


export const OrganizationOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  slug: 'slug',
  logo: 'logo',
  metadata: 'metadata'
} as const

export type OrganizationOrderByRelevanceFieldEnum = (typeof OrganizationOrderByRelevanceFieldEnum)[keyof typeof OrganizationOrderByRelevanceFieldEnum]


export const MemberOrderByRelevanceFieldEnum = {
  id: 'id',
  organizationId: 'organizationId',
  userId: 'userId',
  role: 'role'
} as const

export type MemberOrderByRelevanceFieldEnum = (typeof MemberOrderByRelevanceFieldEnum)[keyof typeof MemberOrderByRelevanceFieldEnum]


export const InvitationOrderByRelevanceFieldEnum = {
  id: 'id',
  organizationId: 'organizationId',
  email: 'email',
  role: 'role',
  status: 'status',
  inviterId: 'inviterId'
} as const

export type InvitationOrderByRelevanceFieldEnum = (typeof InvitationOrderByRelevanceFieldEnum)[keyof typeof InvitationOrderByRelevanceFieldEnum]


export const TwoFactorOrderByRelevanceFieldEnum = {
  id: 'id',
  secret: 'secret',
  backupCodes: 'backupCodes',
  userId: 'userId'
} as const

export type TwoFactorOrderByRelevanceFieldEnum = (typeof TwoFactorOrderByRelevanceFieldEnum)[keyof typeof TwoFactorOrderByRelevanceFieldEnum]


export const PasskeyOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  publicKey: 'publicKey',
  userId: 'userId',
  credentialID: 'credentialID',
  deviceType: 'deviceType',
  transports: 'transports'
} as const

export type PasskeyOrderByRelevanceFieldEnum = (typeof PasskeyOrderByRelevanceFieldEnum)[keyof typeof PasskeyOrderByRelevanceFieldEnum]


export const AlumniProfileOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  firstName: 'firstName',
  lastName: 'lastName',
  displayName: 'displayName',
  bio: 'bio',
  profilePicture: 'profilePicture',
  primaryOrganizationId: 'primaryOrganizationId',
  programType: 'programType',
  centerLocation: 'centerLocation',
  achievements: 'achievements',
  currentPosition: 'currentPosition',
  currentCompany: 'currentCompany',
  industry: 'industry',
  location: 'location',
  linkedInUrl: 'linkedInUrl',
  phoneNumber: 'phoneNumber',
  skillsOffered: 'skillsOffered',
  skillsWanted: 'skillsWanted'
} as const

export type AlumniProfileOrderByRelevanceFieldEnum = (typeof AlumniProfileOrderByRelevanceFieldEnum)[keyof typeof AlumniProfileOrderByRelevanceFieldEnum]


export const ConnectionOrderByRelevanceFieldEnum = {
  id: 'id',
  requesterId: 'requesterId',
  requestedId: 'requestedId'
} as const

export type ConnectionOrderByRelevanceFieldEnum = (typeof ConnectionOrderByRelevanceFieldEnum)[keyof typeof ConnectionOrderByRelevanceFieldEnum]


export const MessageOrderByRelevanceFieldEnum = {
  id: 'id',
  senderId: 'senderId',
  receiverId: 'receiverId',
  content: 'content'
} as const

export type MessageOrderByRelevanceFieldEnum = (typeof MessageOrderByRelevanceFieldEnum)[keyof typeof MessageOrderByRelevanceFieldEnum]


export const PostOrderByRelevanceFieldEnum = {
  id: 'id',
  authorId: 'authorId',
  title: 'title',
  content: 'content',
  imageUrl: 'imageUrl',
  organizationId: 'organizationId'
} as const

export type PostOrderByRelevanceFieldEnum = (typeof PostOrderByRelevanceFieldEnum)[keyof typeof PostOrderByRelevanceFieldEnum]


export const PostLikeOrderByRelevanceFieldEnum = {
  id: 'id',
  postId: 'postId',
  userId: 'userId'
} as const

export type PostLikeOrderByRelevanceFieldEnum = (typeof PostLikeOrderByRelevanceFieldEnum)[keyof typeof PostLikeOrderByRelevanceFieldEnum]


export const CommentOrderByRelevanceFieldEnum = {
  id: 'id',
  postId: 'postId',
  authorId: 'authorId',
  content: 'content'
} as const

export type CommentOrderByRelevanceFieldEnum = (typeof CommentOrderByRelevanceFieldEnum)[keyof typeof CommentOrderByRelevanceFieldEnum]


export const EventOrderByRelevanceFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  location: 'location',
  virtualLink: 'virtualLink',
  imageUrl: 'imageUrl',
  organizationId: 'organizationId',
  organizerId: 'organizerId'
} as const

export type EventOrderByRelevanceFieldEnum = (typeof EventOrderByRelevanceFieldEnum)[keyof typeof EventOrderByRelevanceFieldEnum]


export const EventRegistrationOrderByRelevanceFieldEnum = {
  id: 'id',
  eventId: 'eventId',
  attendeeId: 'attendeeId'
} as const

export type EventRegistrationOrderByRelevanceFieldEnum = (typeof EventRegistrationOrderByRelevanceFieldEnum)[keyof typeof EventRegistrationOrderByRelevanceFieldEnum]


export const DonationOrderByRelevanceFieldEnum = {
  id: 'id',
  donorId: 'donorId',
  currency: 'currency',
  organizationId: 'organizationId',
  paymentMethod: 'paymentMethod',
  transactionId: 'transactionId',
  purpose: 'purpose',
  recurringId: 'recurringId'
} as const

export type DonationOrderByRelevanceFieldEnum = (typeof DonationOrderByRelevanceFieldEnum)[keyof typeof DonationOrderByRelevanceFieldEnum]


export const NewsArticleOrderByRelevanceFieldEnum = {
  id: 'id',
  title: 'title',
  content: 'content',
  excerpt: 'excerpt',
  imageUrl: 'imageUrl',
  authorName: 'authorName',
  organizationId: 'organizationId'
} as const

export type NewsArticleOrderByRelevanceFieldEnum = (typeof NewsArticleOrderByRelevanceFieldEnum)[keyof typeof NewsArticleOrderByRelevanceFieldEnum]


export const NotificationOrderByRelevanceFieldEnum = {
  id: 'id',
  recipientId: 'recipientId',
  title: 'title',
  message: 'message',
  actionUrl: 'actionUrl',
  organizationId: 'organizationId'
} as const

export type NotificationOrderByRelevanceFieldEnum = (typeof NotificationOrderByRelevanceFieldEnum)[keyof typeof NotificationOrderByRelevanceFieldEnum]



/**
 * Field references
 */


/**
 * Reference to a field of type 'String'
 */
export type StringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String'>
    


/**
 * Reference to a field of type 'Boolean'
 */
export type BooleanFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Boolean'>
    


/**
 * Reference to a field of type 'DateTime'
 */
export type DateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime'>
    


/**
 * Reference to a field of type 'Int'
 */
export type IntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int'>
    


/**
 * Reference to a field of type 'ProfileVisibility'
 */
export type EnumProfileVisibilityFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'ProfileVisibility'>
    


/**
 * Reference to a field of type 'ConnectionStatus'
 */
export type EnumConnectionStatusFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'ConnectionStatus'>
    


/**
 * Reference to a field of type 'PostType'
 */
export type EnumPostTypeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'PostType'>
    


/**
 * Reference to a field of type 'EventType'
 */
export type EnumEventTypeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'EventType'>
    


/**
 * Reference to a field of type 'RegistrationStatus'
 */
export type EnumRegistrationStatusFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'RegistrationStatus'>
    


/**
 * Reference to a field of type 'Decimal'
 */
export type DecimalFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Decimal'>
    


/**
 * Reference to a field of type 'DonationType'
 */
export type EnumDonationTypeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DonationType'>
    


/**
 * Reference to a field of type 'PaymentStatus'
 */
export type EnumPaymentStatusFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'PaymentStatus'>
    


/**
 * Reference to a field of type 'NewsCategory'
 */
export type EnumNewsCategoryFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'NewsCategory'>
    


/**
 * Reference to a field of type 'NotificationType'
 */
export type EnumNotificationTypeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'NotificationType'>
    


/**
 * Reference to a field of type 'Float'
 */
export type FloatFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Float'>
    

/**
 * Batch Payload for updateMany & deleteMany & createMany
 */
export type BatchPayload = {
  count: number
}


export type Datasource = {
  url?: string
}
export type Datasources = {
  db?: Datasource
}

export const defineExtension = runtime.Extensions.defineExtension as unknown as runtime.Types.Extensions.ExtendsHook<"define", TypeMapCb, runtime.Types.Extensions.DefaultArgs>
export type DefaultPrismaClient = PrismaClient
export type ErrorFormat = 'pretty' | 'colorless' | 'minimal'
export interface PrismaClientOptions {
  /**
   * Overwrites the datasource url from your schema.prisma file
   */
  datasources?: Datasources
  /**
   * Overwrites the datasource url from your schema.prisma file
   */
  datasourceUrl?: string
  /**
   * @default "colorless"
   */
  errorFormat?: ErrorFormat
  /**
   * @example
   * ```
   * // Shorthand for `emit: 'stdout'`
   * log: ['query', 'info', 'warn', 'error']
   * 
   * // Emit as events only
   * log: [
   *   { emit: 'event', level: 'query' },
   *   { emit: 'event', level: 'info' },
   *   { emit: 'event', level: 'warn' }
   *   { emit: 'event', level: 'error' }
   * ]
   * 
   * / Emit as events and log to stdout
   * og: [
   *  { emit: 'stdout', level: 'query' },
   *  { emit: 'stdout', level: 'info' },
   *  { emit: 'stdout', level: 'warn' }
   *  { emit: 'stdout', level: 'error' }
   * 
   * ```
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/logging#the-log-option).
   */
  log?: (LogLevel | LogDefinition)[]
  /**
   * The default values for transactionOptions
   * maxWait ?= 2000
   * timeout ?= 5000
   */
  transactionOptions?: {
    maxWait?: number
    timeout?: number
    isolationLevel?: TransactionIsolationLevel
  }
  /**
   * Global configuration for omitting model fields by default.
   * 
   * @example
   * ```
   * const prisma = new PrismaClient({
   *   omit: {
   *     user: {
   *       password: true
   *     }
   *   }
   * })
   * ```
   */
  omit?: GlobalOmitConfig
}
export type GlobalOmitConfig = {
  user?: Prisma.UserOmit
  session?: Prisma.SessionOmit
  account?: Prisma.AccountOmit
  verification?: Prisma.VerificationOmit
  organization?: Prisma.OrganizationOmit
  member?: Prisma.MemberOmit
  invitation?: Prisma.InvitationOmit
  twoFactor?: Prisma.TwoFactorOmit
  passkey?: Prisma.PasskeyOmit
  alumniProfile?: Prisma.AlumniProfileOmit
  connection?: Prisma.ConnectionOmit
  message?: Prisma.MessageOmit
  post?: Prisma.PostOmit
  postLike?: Prisma.PostLikeOmit
  comment?: Prisma.CommentOmit
  event?: Prisma.EventOmit
  eventRegistration?: Prisma.EventRegistrationOmit
  donation?: Prisma.DonationOmit
  newsArticle?: Prisma.NewsArticleOmit
  notification?: Prisma.NotificationOmit
}

/* Types for Logging */
export type LogLevel = 'info' | 'query' | 'warn' | 'error'
export type LogDefinition = {
  level: LogLevel
  emit: 'stdout' | 'event'
}

export type CheckIsLogLevel<T> = T extends LogLevel ? T : never;

export type GetLogType<T> = CheckIsLogLevel<
  T extends LogDefinition ? T['level'] : T
>;

export type GetEvents<T extends any[]> = T extends Array<LogLevel | LogDefinition>
  ? GetLogType<T[number]>
  : never;

export type QueryEvent = {
  timestamp: Date
  query: string
  params: string
  duration: number
  target: string
}

export type LogEvent = {
  timestamp: Date
  message: string
  target: string
}
/* End Types for Logging */


export type PrismaAction =
  | 'findUnique'
  | 'findUniqueOrThrow'
  | 'findMany'
  | 'findFirst'
  | 'findFirstOrThrow'
  | 'create'
  | 'createMany'
  | 'createManyAndReturn'
  | 'update'
  | 'updateMany'
  | 'updateManyAndReturn'
  | 'upsert'
  | 'delete'
  | 'deleteMany'
  | 'executeRaw'
  | 'queryRaw'
  | 'aggregate'
  | 'count'
  | 'runCommandRaw'
  | 'findRaw'
  | 'groupBy'

/**
 * These options are being passed into the middleware as "params"
 */
export type MiddlewareParams = {
  model?: ModelName
  action: PrismaAction
  args: any
  dataPath: string[]
  runInTransaction: boolean
}

/**
 * The `T` type makes sure, that the `return proceed` is not forgotten in the middleware implementation
 */
export type Middleware<T = any> = (
  params: MiddlewareParams,
  next: (params: MiddlewareParams) => runtime.Types.Utils.JsPromise<T>,
) => runtime.Types.Utils.JsPromise<T>

/**
 * `PrismaClient` proxy available in interactive transactions.
 */
export type TransactionClient = Omit<DefaultPrismaClient, runtime.ITXClientDenyList>

