{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Work/protec/apps/web/src/utils/orpc.ts"], "sourcesContent": ["import { createORPCClient } from \"@orpc/client\";\nimport { RPCLink } from \"@orpc/client/fetch\";\nimport { createTanstackQueryUtils } from \"@orpc/tanstack-query\";\nimport { QueryCache, QueryClient } from \"@tanstack/react-query\";\nimport { toast } from \"react-toastify\";\nimport type { appRouter } from \"../../../server/src/routers/index\";\nimport type { RouterClient } from \"@orpc/server\";\n\nexport const queryClient = new QueryClient({\n  queryCache: new QueryCache({\n    onError: (error) => {\n      toast.error(`Error: ${error.message}. Click to retry`, {\n        onClick: () => {\n          queryClient.invalidateQueries();\n        },\n        closeOnClick: true,\n      });\n    },\n  }),\n});\n\nexport const link = new RPCLink({\n  url: `${process.env.NEXT_PUBLIC_SERVER_URL}/rpc`,\n  fetch(url, options) {\n    return fetch(url, {\n      ...options,\n      credentials: \"include\",\n    });\n  },\n});\n\nexport const client: RouterClient<typeof appRouter> = createORPCClient(link)\n\nexport const orpc = createTanstackQueryUtils(client)\n"], "names": [], "mappings": ";;;;;;AAsBU;AAtBV;AAAA;AACA;AACA;AACA;AAAA;AACA;;;;;;AAIO,MAAM,cAAc,IAAI,gLAAA,CAAA,cAAW,CAAC;IACzC,YAAY,IAAI,+KAAA,CAAA,aAAU,CAAC;QACzB,SAAS,CAAC;YACR,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,AAAC,UAAuB,OAAd,MAAM,OAAO,EAAC,qBAAmB;gBACrD,SAAS;oBACP,YAAY,iBAAiB;gBAC/B;gBACA,cAAc;YAChB;QACF;IACF;AACF;AAEO,MAAM,OAAO,IAAI,0KAAA,CAAA,UAAO,CAAC;IAC9B,KAAK,AAAC,GAAqC,kEAAA;IAC3C,OAAM,GAAG,EAAE,OAAO;QAChB,OAAO,MAAM,KAAK;YAChB,GAAG,OAAO;YACV,aAAa;QACf;IACF;AACF;AAEO,MAAM,SAAyC,CAAA,GAAA,qKAAA,CAAA,mBAAgB,AAAD,EAAE;AAEhE,MAAM,OAAO,CAAA,GAAA,gKAAA,CAAA,2BAAwB,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Work/protec/apps/web/src/components/theme-provider.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { ThemeProvider as NextThemesProvider } from \"next-themes\"\n\nexport function ThemeProvider({\n  children,\n  ...props\n}: React.ComponentProps<typeof NextThemesProvider>) {\n  return <NextThemesProvider {...props}>{children}</NextThemesProvider>\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKO,SAAS,cAAc,KAGoB;QAHpB,EAC5B,QAAQ,EACR,GAAG,OAC6C,GAHpB;IAI5B,qBAAO,6LAAC,mJAAA,CAAA,gBAAkB;QAAE,GAAG,KAAK;kBAAG;;;;;;AACzC;KALgB", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Work/protec/apps/web/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Work/protec/apps/web/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\r\nimport { Slot, Slottable } from \"@radix-ui/react-slot\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Loader2 } from \"lucide-react\";\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-hidden focus-visible:ring-1 focus-visible:ring-ring focus-visible:ring-offset-1 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\r\n        dark: \"bg-gray-900 text-white hover:bg-gray-800 dark:bg-gray-300 dark:text-gray-900 dark:hover:bg-gray-100\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n        success: \"bg-emerald-600 text-white\",\r\n        warning: \"bg-amber-500 text-white\",\r\n      },\r\n      size: {\r\n        default: \"h-10 px-4 py-2\",\r\n        sm: \"h-9 rounded-md px-3\",\r\n        lg: \"h-11 rounded-md px-8\",\r\n        icon: \"h-10 w-10\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n);\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean;\r\n  loading?: boolean;\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  (\r\n    {\r\n      className,\r\n      variant,\r\n      size,\r\n      loading,\r\n      disabled,\r\n      children,\r\n      asChild = false,\r\n      ...props\r\n    },\r\n    ref\r\n  ) => {\r\n    const Comp = asChild ? Slot : \"button\";\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        disabled={loading || disabled}\r\n        {...props}\r\n      >\r\n        {loading && <Loader2 className=\"mr-2 h-5 w-5 animate-spin\" />}\r\n        <Slottable>{children}</Slottable>\r\n      </Comp>\r\n    );\r\n  }\r\n);\r\nButton.displayName = \"Button\";\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;AACA;;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,8VACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,MAAM;YACN,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;YACN,SAAS;YACT,SAAS;QACX;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAUF,MAAM,uBAAS,6JAAA,CAAA,aAAgB,MAC7B,QAWE;QAVA,EACE,SAAS,EACT,OAAO,EACP,IAAI,EACJ,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,UAAU,KAAK,EACf,GAAG,OACJ;IAGD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACL,UAAU,WAAW;QACpB,GAAG,KAAK;;YAER,yBAAW,6LAAC,oNAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;0BAC/B,6LAAC,mKAAA,CAAA,YAAS;0BAAE;;;;;;;;;;;;AAGlB;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 201, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Work/protec/apps/web/src/components/ui/alert-dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { buttonVariants } from \"@/components/ui/button\"\r\n\r\nfunction AlertDialog({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Root>) {\r\n  return <AlertDialogPrimitive.Root data-slot=\"alert-dialog\" {...props} />\r\n}\r\n\r\nfunction AlertDialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Trigger>) {\r\n  return (\r\n    <AlertDialogPrimitive.Trigger data-slot=\"alert-dialog-trigger\" {...props} />\r\n  )\r\n}\r\n\r\nfunction AlertDialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Portal>) {\r\n  return (\r\n    <AlertDialogPrimitive.Portal data-slot=\"alert-dialog-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction AlertDialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Overlay>) {\r\n  return (\r\n    <AlertDialogPrimitive.Overlay\r\n      data-slot=\"alert-dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Content>) {\r\n  return (\r\n    <AlertDialogPortal>\r\n      <AlertDialogOverlay />\r\n      <AlertDialogPrimitive.Content\r\n        data-slot=\"alert-dialog-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </AlertDialogPortal>\r\n  )\r\n}\r\n\r\nfunction AlertDialogHeader({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogFooter({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-dialog-footer\"\r\n      className={cn(\r\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Title>) {\r\n  return (\r\n    <AlertDialogPrimitive.Title\r\n      data-slot=\"alert-dialog-title\"\r\n      className={cn(\"text-lg font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Description>) {\r\n  return (\r\n    <AlertDialogPrimitive.Description\r\n      data-slot=\"alert-dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogAction({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Action>) {\r\n  return (\r\n    <AlertDialogPrimitive.Action\r\n      className={cn(buttonVariants(), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogCancel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Cancel>) {\r\n  return (\r\n    <AlertDialogPrimitive.Cancel\r\n      className={cn(buttonVariants({ variant: \"outline\" }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  AlertDialog,\r\n  AlertDialogPortal,\r\n  AlertDialogOverlay,\r\n  AlertDialogTrigger,\r\n  AlertDialogContent,\r\n  AlertDialogHeader,\r\n  AlertDialogFooter,\r\n  AlertDialogTitle,\r\n  AlertDialogDescription,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAGA;AAEA;AACA;AANA;;;;;AAQA,SAAS,YAAY,KAEoC;QAFpC,EACnB,GAAG,OACoD,GAFpC;IAGnB,qBAAO,6LAAC,8KAAA,CAAA,OAAyB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AACtE;KAJS;AAMT,SAAS,mBAAmB,KAEgC;QAFhC,EAC1B,GAAG,OACuD,GAFhC;IAG1B,qBACE,6LAAC,8KAAA,CAAA,UAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,kBAAkB,KAEgC;QAFhC,EACzB,GAAG,OACsD,GAFhC;IAGzB,qBACE,6LAAC,8KAAA,CAAA,SAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,mBAAmB,KAGgC;QAHhC,EAC1B,SAAS,EACT,GAAG,OACuD,GAHhC;IAI1B,qBACE,6LAAC,8KAAA,CAAA,UAA4B;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,mBAAmB,KAGgC;QAHhC,EAC1B,SAAS,EACT,GAAG,OACuD,GAHhC;IAI1B,qBACE,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,8KAAA,CAAA,UAA4B;gBAC3B,aAAU;gBACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIjB;MAjBS;AAmBT,SAAS,kBAAkB,KAGG;QAHH,EACzB,SAAS,EACT,GAAG,OACyB,GAHH;IAIzB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,KAGG;QAHH,EACzB,SAAS,EACT,GAAG,OACyB,GAHH;IAIzB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,iBAAiB,KAGgC;QAHhC,EACxB,SAAS,EACT,GAAG,OACqD,GAHhC;IAIxB,qBACE,6LAAC,8KAAA,CAAA,QAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,uBAAuB,KAGgC;QAHhC,EAC9B,SAAS,EACT,GAAG,OAC2D,GAHhC;IAI9B,qBACE,6LAAC,8KAAA,CAAA,cAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,KAGgC;QAHhC,EACzB,SAAS,EACT,GAAG,OACsD,GAHhC;IAIzB,qBACE,6LAAC,8KAAA,CAAA,SAA2B;QAC1B,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,oJAAA,CAAA,iBAAc,AAAD,KAAK;QAC/B,GAAG,KAAK;;;;;;AAGf;MAVS;AAYT,SAAS,kBAAkB,KAGgC;QAHhC,EACzB,SAAS,EACT,GAAG,OACsD,GAHhC;IAIzB,qBACE,6LAAC,8KAAA,CAAA,SAA2B;QAC1B,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,oJAAA,CAAA,iBAAc,AAAD,EAAE;YAAE,SAAS;QAAU,IAAI;QACrD,GAAG,KAAK;;;;;;AAGf;OAVS", "debugId": null}}, {"offset": {"line": 398, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Work/protec/apps/web/src/components/ui/confirm-dialog.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, {\r\n  useState,\r\n  useCallback,\r\n  useMemo,\r\n  useRef,\r\n  useContext,\r\n  createContext,\r\n  memo,\r\n} from \"react\";\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogOverlay,\r\n  AlertDialogPortal,\r\n  AlertDialogTitle,\r\n} from \"@/components/ui/alert-dialog\";\r\n\r\nexport interface ConfirmOptions {\r\n  title?: React.ReactNode;\r\n  description?: React.ReactNode;\r\n  confirmText?: string;\r\n  cancelText?: string;\r\n  icon?: React.ReactNode;\r\n  customActions?: (\r\n    onConfirm: () => void,\r\n    onCancel: () => void\r\n  ) => React.ReactNode;\r\n  confirmButton?: React.ComponentPropsWithRef<typeof AlertDialogAction>;\r\n  cancelButton?: React.ComponentPropsWithRef<typeof AlertDialogCancel> | null;\r\n  alertDialogOverlay?: React.ComponentPropsWithRef<typeof AlertDialogOverlay>;\r\n  alertDialogContent?: React.ComponentPropsWithRef<typeof AlertDialogContent>;\r\n  alertDialogHeader?: React.ComponentPropsWithRef<typeof AlertDialogHeader>;\r\n  alertDialogTitle?: React.ComponentPropsWithRef<typeof AlertDialogTitle>;\r\n  alertDialogDescription?: React.ComponentPropsWithRef<\r\n    typeof AlertDialogDescription\r\n  >;\r\n  alertDialogFooter?: React.ComponentPropsWithRef<typeof AlertDialogFooter>;\r\n}\r\n\r\nexport interface ConfirmContextType {\r\n  confirm: (options: ConfirmOptions) => Promise<boolean>;\r\n}\r\n\r\nexport const ConfirmContext = createContext<ConfirmContextType | undefined>(\r\n  undefined\r\n);\r\n\r\nconst baseDefaultOptions: ConfirmOptions = {\r\n  title: \"\",\r\n  description: \"\",\r\n  confirmText: \"Confirm\",\r\n  cancelText: \"Cancel\",\r\n  confirmButton: {},\r\n  cancelButton: {},\r\n  alertDialogContent: {},\r\n  alertDialogHeader: {},\r\n  alertDialogTitle: {},\r\n  alertDialogDescription: {},\r\n  alertDialogFooter: {},\r\n};\r\n\r\nconst ConfirmDialogContent: React.FC<{\r\n  config: ConfirmOptions;\r\n  onConfirm: () => void;\r\n  onCancel: () => void;\r\n}> = memo(({ config, onConfirm, onCancel }) => {\r\n  const {\r\n    title,\r\n    description,\r\n    cancelButton,\r\n    confirmButton,\r\n    confirmText,\r\n    cancelText,\r\n    icon,\r\n    customActions,\r\n    alertDialogOverlay,\r\n    alertDialogContent,\r\n    alertDialogHeader,\r\n    alertDialogTitle,\r\n    alertDialogDescription,\r\n    alertDialogFooter,\r\n  } = config;\r\n\r\n  return (\r\n    <AlertDialogPortal>\r\n      <AlertDialogOverlay {...alertDialogOverlay} />\r\n      <AlertDialogContent {...alertDialogContent}>\r\n        <AlertDialogHeader {...alertDialogHeader}>\r\n          {(title || icon) && (\r\n            <AlertDialogTitle {...alertDialogTitle}>\r\n              {icon}\r\n              {title}\r\n            </AlertDialogTitle>\r\n          )}\r\n          {description && (\r\n            <AlertDialogDescription {...alertDialogDescription}>\r\n              {description}\r\n            </AlertDialogDescription>\r\n          )}\r\n        </AlertDialogHeader>\r\n        <AlertDialogFooter {...alertDialogFooter}>\r\n          {customActions ? (\r\n            customActions(onConfirm, onCancel)\r\n          ) : (\r\n            <>\r\n              {cancelButton !== null && (\r\n                <AlertDialogCancel onClick={onCancel} {...cancelButton}>\r\n                  {cancelText}\r\n                </AlertDialogCancel>\r\n              )}\r\n              <AlertDialogAction onClick={onConfirm} {...confirmButton}>\r\n                {confirmText}\r\n              </AlertDialogAction>\r\n            </>\r\n          )}\r\n        </AlertDialogFooter>\r\n      </AlertDialogContent>\r\n    </AlertDialogPortal>\r\n  );\r\n});\r\n\r\nConfirmDialogContent.displayName = \"ConfirmDialogContent\";\r\n\r\nconst ConfirmDialog: React.FC<{\r\n  isOpen: boolean;\r\n  onOpenChange: (isOpen: boolean) => void;\r\n  config: ConfirmOptions;\r\n  onConfirm: () => void;\r\n  onCancel: () => void;\r\n}> = memo(({ isOpen, onOpenChange, config, onConfirm, onCancel }) => (\r\n  <AlertDialog open={isOpen} onOpenChange={onOpenChange}>\r\n    <ConfirmDialogContent\r\n      config={config}\r\n      onConfirm={onConfirm}\r\n      onCancel={onCancel}\r\n    />\r\n  </AlertDialog>\r\n));\r\n\r\nConfirmDialog.displayName = \"ConfirmDialog\";\r\n\r\nexport const ConfirmDialogProvider: React.FC<{\r\n  defaultOptions?: ConfirmOptions;\r\n  children: React.ReactNode;\r\n}> = ({ defaultOptions = {}, children }) => {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const [options, setOptions] = useState<ConfirmOptions>(baseDefaultOptions);\r\n  const resolverRef = useRef<((value: boolean) => void) | null>(null);\r\n\r\n  const mergedDefaultOptions = useMemo(\r\n    () => ({\r\n      ...baseDefaultOptions,\r\n      ...defaultOptions,\r\n    }),\r\n    [defaultOptions]\r\n  );\r\n\r\n  const confirm = useCallback(\r\n    (newOptions: ConfirmOptions) => {\r\n      setOptions({ ...mergedDefaultOptions, ...newOptions });\r\n      setIsOpen(true);\r\n      return new Promise<boolean>((resolve) => {\r\n        resolverRef.current = resolve;\r\n      });\r\n    },\r\n    [mergedDefaultOptions]\r\n  );\r\n\r\n  const handleConfirm = useCallback(() => {\r\n    setIsOpen(false);\r\n    if (resolverRef.current) resolverRef.current(true);\r\n  }, []);\r\n\r\n  const handleCancel = useCallback(() => {\r\n    setIsOpen(false);\r\n    if (resolverRef.current) resolverRef.current(false);\r\n  }, []);\r\n\r\n  const contextValue = useMemo(() => ({ confirm }), [confirm]);\r\n\r\n  return (\r\n    <ConfirmContext.Provider value={contextValue}>\r\n      {children}\r\n      <ConfirmDialog\r\n        isOpen={isOpen}\r\n        onOpenChange={setIsOpen}\r\n        config={options}\r\n        onConfirm={handleConfirm}\r\n        onCancel={handleCancel}\r\n      />\r\n    </ConfirmContext.Provider>\r\n  );\r\n};\r\n\r\nexport const useConfirm = (): ((\r\n  options: ConfirmOptions\r\n) => Promise<boolean>) => {\r\n  const context = useContext(ConfirmContext);\r\n  if (!context) {\r\n    throw new Error(\"useConfirm must be used within a ConfirmDialogProvider\");\r\n  }\r\n  return context.confirm;\r\n};\r\n"], "names": [], "mappings": ";;;;;;AACA;AASA;;;AAVA;;;AAiDO,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EACxC;AAGF,MAAM,qBAAqC;IACzC,OAAO;IACP,aAAa;IACb,aAAa;IACb,YAAY;IACZ,eAAe,CAAC;IAChB,cAAc,CAAC;IACf,oBAAoB,CAAC;IACrB,mBAAmB,CAAC;IACpB,kBAAkB,CAAC;IACnB,wBAAwB,CAAC;IACzB,mBAAmB,CAAC;AACtB;AAEA,MAAM,qCAID,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE;QAAC,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE;IACxC,MAAM,EACJ,KAAK,EACL,WAAW,EACX,YAAY,EACZ,aAAa,EACb,WAAW,EACX,UAAU,EACV,IAAI,EACJ,aAAa,EACb,kBAAkB,EAClB,kBAAkB,EAClB,iBAAiB,EACjB,gBAAgB,EAChB,sBAAsB,EACtB,iBAAiB,EAClB,GAAG;IAEJ,qBACE,6LAAC,6JAAA,CAAA,oBAAiB;;0BAChB,6LAAC,6JAAA,CAAA,qBAAkB;gBAAE,GAAG,kBAAkB;;;;;;0BAC1C,6LAAC,6JAAA,CAAA,qBAAkB;gBAAE,GAAG,kBAAkB;;kCACxC,6LAAC,6JAAA,CAAA,oBAAiB;wBAAE,GAAG,iBAAiB;;4BACrC,CAAC,SAAS,IAAI,mBACb,6LAAC,6JAAA,CAAA,mBAAgB;gCAAE,GAAG,gBAAgB;;oCACnC;oCACA;;;;;;;4BAGJ,6BACC,6LAAC,6JAAA,CAAA,yBAAsB;gCAAE,GAAG,sBAAsB;0CAC/C;;;;;;;;;;;;kCAIP,6LAAC,6JAAA,CAAA,oBAAiB;wBAAE,GAAG,iBAAiB;kCACrC,gBACC,cAAc,WAAW,0BAEzB;;gCACG,iBAAiB,sBAChB,6LAAC,6JAAA,CAAA,oBAAiB;oCAAC,SAAS;oCAAW,GAAG,YAAY;8CACnD;;;;;;8CAGL,6LAAC,6JAAA,CAAA,oBAAiB;oCAAC,SAAS;oCAAY,GAAG,aAAa;8CACrD;;;;;;;;;;;;;;;;;;;;;;;;;AAQjB;KA1DM;AA4DN,qBAAqB,WAAW,GAAG;AAEnC,MAAM,8BAMD,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE;QAAC,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE;yBAC9D,6LAAC,6JAAA,CAAA,cAAW;QAAC,MAAM;QAAQ,cAAc;kBACvC,cAAA,6LAAC;YACC,QAAQ;YACR,WAAW;YACX,UAAU;;;;;;;;;;;;MAXV;AAgBN,cAAc,WAAW,GAAG;AAErB,MAAM,wBAGR;QAAC,EAAE,iBAAiB,CAAC,CAAC,EAAE,QAAQ,EAAE;;IACrC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAqC;IAE9D,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;+DACjC,IAAM,CAAC;gBACL,GAAG,kBAAkB;gBACrB,GAAG,cAAc;YACnB,CAAC;8DACD;QAAC;KAAe;IAGlB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDACxB,CAAC;YACC,WAAW;gBAAE,GAAG,oBAAoB;gBAAE,GAAG,UAAU;YAAC;YACpD,UAAU;YACV,OAAO,IAAI;8DAAiB,CAAC;oBAC3B,YAAY,OAAO,GAAG;gBACxB;;QACF;qDACA;QAAC;KAAqB;IAGxB,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE;YAChC,UAAU;YACV,IAAI,YAAY,OAAO,EAAE,YAAY,OAAO,CAAC;QAC/C;2DAAG,EAAE;IAEL,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE;YAC/B,UAAU;YACV,IAAI,YAAY,OAAO,EAAE,YAAY,OAAO,CAAC;QAC/C;0DAAG,EAAE;IAEL,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;uDAAE,IAAM,CAAC;gBAAE;YAAQ,CAAC;sDAAG;QAAC;KAAQ;IAE3D,qBACE,6LAAC,eAAe,QAAQ;QAAC,OAAO;;YAC7B;0BACD,6LAAC;gBACC,QAAQ;gBACR,cAAc;gBACd,QAAQ;gBACR,WAAW;gBACX,UAAU;;;;;;;;;;;;AAIlB;GAnDa;MAAA;AAqDN,MAAM,aAAa;;IAGxB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO,QAAQ,OAAO;AACxB;IARa", "debugId": null}}, {"offset": {"line": 629, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Work/protec/apps/web/src/components/ui/sonner.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useTheme } from \"next-themes\";\nimport { Toaster as Son<PERSON>, type ToasterProps } from \"sonner\";\n\nconst Toaster = ({ ...props }: ToasterProps) => {\n  const { theme = \"system\" } = useTheme();\n\n  return (\n    <Sonner\n      theme={theme as ToasterProps[\"theme\"]}\n      className=\"toaster group\"\n      style={\n        {\n          \"--normal-bg\": \"var(--popover)\",\n          \"--normal-text\": \"var(--popover-foreground)\",\n          \"--normal-border\": \"var(--border)\",\n        } as React.CSSProperties\n      }\n      {...props}\n    />\n  );\n};\n\nexport { Toaster };\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKA,MAAM,UAAU;QAAC,EAAE,GAAG,OAAqB;;IACzC,MAAM,EAAE,QAAQ,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAEpC,qBACE,6LAAC,2IAAA,CAAA,UAAM;QACL,OAAO;QACP,WAAU;QACV,OACE;YACE,eAAe;YACf,iBAAiB;YACjB,mBAAmB;QACrB;QAED,GAAG,KAAK;;;;;;AAGf;GAjBM;;QACyB,mJAAA,CAAA,WAAQ;;;KADjC", "debugId": null}}, {"offset": {"line": 677, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Work/protec/apps/web/src/components/providers.tsx"], "sourcesContent": ["\"use client\";\n\nimport { QueryClientProvider } from \"@tanstack/react-query\";\nimport { ReactQueryDevtools } from \"@tanstack/react-query-devtools\";\nimport { queryClient } from \"@/utils/orpc\";\nimport { ThemeProvider } from \"./theme-provider\";\nimport { ConfirmDialogProvider } from \"@/components/ui/confirm-dialog\";\nimport { Toaster } from \"./ui/sonner\";\n\n\nexport default function Providers({\n  children\n}: {\n  children: React.ReactNode\n}) {\n  return (\n    <ThemeProvider\n      attribute=\"class\"\n      defaultTheme=\"system\"\n      enableSystem\n      disableTransitionOnChange\n    >\n      <QueryClientProvider client={queryClient}>\n        <ConfirmDialogProvider>\n          {children}\n          <ReactQueryDevtools />\n        </ConfirmDialogProvider>\n      </QueryClientProvider>\n      <Toaster richColors />\n    </ThemeProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAUe,SAAS,UAAU,KAIjC;QAJiC,EAChC,QAAQ,EAGT,GAJiC;IAKhC,qBACE,6LAAC,yJAAA,CAAA,gBAAa;QACZ,WAAU;QACV,cAAa;QACb,YAAY;QACZ,yBAAyB;;0BAEzB,6LAAC,yLAAA,CAAA,sBAAmB;gBAAC,QAAQ,sIAAA,CAAA,cAAW;0BACtC,cAAA,6LAAC,+JAAA,CAAA,wBAAqB;;wBACnB;sCACD,6LAAC,uLAAA,CAAA,qBAAkB;;;;;;;;;;;;;;;;0BAGvB,6LAAC,oJAAA,CAAA,UAAO;gBAAC,UAAU;;;;;;;;;;;;AAGzB;KArBwB", "debugId": null}}, {"offset": {"line": 750, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Work/protec/apps/web/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\r\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction DropdownMenu({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\r\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Trigger\r\n      data-slot=\"dropdown-menu-trigger\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuContent({\r\n  className,\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal>\r\n      <DropdownMenuPrimitive.Content\r\n        data-slot=\"dropdown-menu-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </DropdownMenuPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuItem({\r\n  className,\r\n  inset,\r\n  variant = \"default\",\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\r\n  inset?: boolean\r\n  variant?: \"default\" | \"destructive\"\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Item\r\n      data-slot=\"dropdown-menu-item\"\r\n      data-inset={inset}\r\n      data-variant={variant}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuCheckboxItem({\r\n  className,\r\n  children,\r\n  checked,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.CheckboxItem\r\n      data-slot=\"dropdown-menu-checkbox-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      checked={checked}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.CheckboxItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioGroup\r\n      data-slot=\"dropdown-menu-radio-group\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioItem\r\n      data-slot=\"dropdown-menu-radio-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CircleIcon className=\"size-2 fill-current\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.RadioItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuLabel({\r\n  className,\r\n  inset,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Label\r\n      data-slot=\"dropdown-menu-label\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Separator\r\n      data-slot=\"dropdown-menu-separator\"\r\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuShortcut({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"dropdown-menu-shortcut\"\r\n      className={cn(\r\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSub({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\r\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuSubTrigger({\r\n  className,\r\n  inset,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubTrigger\r\n      data-slot=\"dropdown-menu-sub-trigger\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <ChevronRightIcon className=\"ml-auto size-4\" />\r\n    </DropdownMenuPrimitive.SubTrigger>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSubContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubContent\r\n      data-slot=\"dropdown-menu-sub-content\"\r\n      className={cn(\r\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuPortal,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuLabel,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioGroup,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuSubContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,KAEoC;QAFpC,EACpB,GAAG,OACqD,GAFpC;IAGpB,qBAAO,6LAAC,+KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,KAEgC;QAFhC,EAC1B,GAAG,OACuD,GAFhC;IAG1B,qBACE,6LAAC,+KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,oBAAoB,KAEgC;QAFhC,EAC3B,GAAG,OACwD,GAFhC;IAG3B,qBACE,6LAAC,+KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,oBAAoB,KAIgC;QAJhC,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD,GAJhC;IAK3B,qBACE,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,KAEgC;QAFhC,EACzB,GAAG,OACsD,GAFhC;IAGzB,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,iBAAiB,KAQzB;QARyB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ,GARyB;IASxB,qBACE,6LAAC,+KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,KAKgC;QALhC,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D,GALhC;IAMhC,qBACE,6LAAC,+KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,KAEgC;QAFhC,EAC9B,GAAG,OAC2D,GAFhC;IAG9B,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,KAIgC;QAJhC,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D,GAJhC;IAK7B,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,6MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,KAM1B;QAN0B,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ,GAN0B;IAOzB,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAlBS;AAoBT,SAAS,sBAAsB,KAGgC;QAHhC,EAC7B,SAAS,EACT,GAAG,OAC0D,GAHhC;IAI7B,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,KAGC;QAHD,EAC5B,SAAS,EACT,GAAG,OAC0B,GAHD;IAI5B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS;AAgBT,SAAS,gBAAgB,KAEgC;QAFhC,EACvB,GAAG,OACoD,GAFhC;IAGvB,qBAAO,6LAAC,+KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,KAO/B;QAP+B,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ,GAP+B;IAQ9B,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,KAGgC;QAHhC,EAC9B,SAAS,EACT,GAAG,OAC2D,GAHhC;IAI9B,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 1061, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Work/protec/apps/web/src/components/mode-toggle.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { Moon, Sun } from \"lucide-react\"\nimport { useTheme } from \"next-themes\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\"\n\nexport function ModeToggle() {\n  const { setTheme } = useTheme()\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"outline\" size=\"icon\">\n          <Sun className=\"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n          <Moon className=\"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n          <span className=\"sr-only\">Toggle theme</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent align=\"end\">\n        <DropdownMenuItem onClick={() => setTheme(\"light\")}>\n          Light\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme(\"dark\")}>\n          Dark\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme(\"system\")}>\n          System\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AACA;AACA;;;AANA;;;;;AAaO,SAAS;;IACd,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAE5B,qBACE,6LAAC,8JAAA,CAAA,eAAY;;0BACX,6LAAC,8JAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,6LAAC,oJAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;;sCAC7B,6LAAC,mMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;sCACf,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,6LAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,6LAAC,8JAAA,CAAA,sBAAmB;gBAAC,OAAM;;kCACzB,6LAAC,8JAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAU;;;;;;kCAGpD,6LAAC,8JAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAS;;;;;;kCAGnD,6LAAC,8JAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAW;;;;;;;;;;;;;;;;;;AAM7D;GAzBgB;;QACO,mJAAA,CAAA,WAAQ;;;KADf", "debugId": null}}, {"offset": {"line": 1178, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Work/protec/apps/web/src/lib/auth-client.ts"], "sourcesContent": ["import { createAuthClient } from \"better-auth/react\";\n\nexport const authClient = createAuthClient({\n  baseURL:\n      process.env.NEXT_PUBLIC_SERVER_URL,\n});\n"], "names": [], "mappings": ";;;AAIM;AAJN;;AAEO,MAAM,aAAa,CAAA,GAAA,sKAAA,CAAA,mBAAgB,AAAD,EAAE;IACzC,OAAO;AAET", "debugId": null}}, {"offset": {"line": 1196, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Work/protec/apps/web/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\r\n\r\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"skeleton\"\r\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Skeleton }\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAChB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;KARS", "debugId": null}}, {"offset": {"line": 1228, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Work/protec/apps/web/src/components/user-menu.tsx"], "sourcesContent": ["import {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\";\nimport { authClient } from \"@/lib/auth-client\";\nimport { Button } from \"./ui/button\";\nimport { Skeleton } from \"./ui/skeleton\";\nimport { useRouter } from \"next/navigation\";\nimport Link from \"next/link\";\n\nexport default function UserMenu() {\n  const router = useRouter();\n  const { data: session, isPending } = authClient.useSession();\n\n  if (isPending) {\n    return <Skeleton className=\"h-9 w-24\" />;\n  }\n\n  if (!session) {\n    return (\n      <Button variant=\"outline\" asChild>\n        <Link href=\"/login\">Sign In</Link>\n      </Button>\n    );\n  }\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"outline\">{session.user.name}</Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent className=\"bg-card\">\n        <DropdownMenuLabel>My Account</DropdownMenuLabel>\n        <DropdownMenuSeparator />\n        <DropdownMenuItem>{session.user.email}</DropdownMenuItem>\n        <DropdownMenuItem asChild>\n          <Button\n            variant=\"destructive\"\n            className=\"w-full\"\n            onClick={() => {\n              authClient.signOut({\n                fetchOptions: {\n                  onSuccess: () => {\n                    router.push(\"/\");\n                  },\n                },\n              });\n            }}\n          >\n            Sign Out\n          </Button>\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAQA;AACA;AACA;AACA;AACA;;;;;;;;;AAEe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,MAAM,OAAO,EAAE,SAAS,EAAE,GAAG,8IAAA,CAAA,aAAU,CAAC,UAAU;IAE1D,IAAI,WAAW;QACb,qBAAO,6LAAC,sJAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;IAC7B;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC,oJAAA,CAAA,SAAM;YAAC,SAAQ;YAAU,OAAO;sBAC/B,cAAA,6LAAC,+JAAA,CAAA,UAAI;gBAAC,MAAK;0BAAS;;;;;;;;;;;IAG1B;IAEA,qBACE,6LAAC,8JAAA,CAAA,eAAY;;0BACX,6LAAC,8JAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,6LAAC,oJAAA,CAAA,SAAM;oBAAC,SAAQ;8BAAW,QAAQ,IAAI,CAAC,IAAI;;;;;;;;;;;0BAE9C,6LAAC,8JAAA,CAAA,sBAAmB;gBAAC,WAAU;;kCAC7B,6LAAC,8JAAA,CAAA,oBAAiB;kCAAC;;;;;;kCACnB,6LAAC,8JAAA,CAAA,wBAAqB;;;;;kCACtB,6LAAC,8JAAA,CAAA,mBAAgB;kCAAE,QAAQ,IAAI,CAAC,KAAK;;;;;;kCACrC,6LAAC,8JAAA,CAAA,mBAAgB;wBAAC,OAAO;kCACvB,cAAA,6LAAC,oJAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,WAAU;4BACV,SAAS;gCACP,8IAAA,CAAA,aAAU,CAAC,OAAO,CAAC;oCACjB,cAAc;wCACZ,WAAW;4CACT,OAAO,IAAI,CAAC;wCACd;oCACF;gCACF;4BACF;sCACD;;;;;;;;;;;;;;;;;;;;;;;AAOX;GA7CwB;;QACP,qIAAA,CAAA,YAAS;QACa,8IAAA,CAAA,aAAU,CAAC;;;KAF1B", "debugId": null}}, {"offset": {"line": 1372, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Work/protec/apps/web/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,KAA4D;QAA5D,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC,GAA5D;IACb,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 1405, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Work/protec/apps/web/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator-root\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,KAKoC;QALpC,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD,GALpC;IAMjB,qBACE,6LAAC,wKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf;KAlBS", "debugId": null}}, {"offset": {"line": 1442, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Work/protec/apps/web/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\r\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\r\n}\r\n\r\nfunction SheetTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\r\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\r\n}\r\n\r\nfunction SheetClose({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\r\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\r\n}\r\n\r\nfunction SheetPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\r\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\r\n}\r\n\r\nfunction SheetOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\r\n  return (\r\n    <SheetPrimitive.Overlay\r\n      data-slot=\"sheet-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetContent({\r\n  className,\r\n  children,\r\n  side = \"right\",\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\r\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\r\n}) {\r\n  return (\r\n    <SheetPortal>\r\n      <SheetOverlay />\r\n      <SheetPrimitive.Content\r\n        data-slot=\"sheet-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\r\n          side === \"right\" &&\r\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\r\n          side === \"left\" &&\r\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\r\n          side === \"top\" &&\r\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\r\n          side === \"bottom\" &&\r\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\r\n          <XIcon className=\"size-4\" />\r\n          <span className=\"sr-only\">Close</span>\r\n        </SheetPrimitive.Close>\r\n      </SheetPrimitive.Content>\r\n    </SheetPortal>\r\n  )\r\n}\r\n\r\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sheet-header\"\r\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sheet-footer\"\r\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\r\n  return (\r\n    <SheetPrimitive.Title\r\n      data-slot=\"sheet-title\"\r\n      className={cn(\"text-foreground font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\r\n  return (\r\n    <SheetPrimitive.Description\r\n      data-slot=\"sheet-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Sheet,\r\n  SheetTrigger,\r\n  SheetClose,\r\n  SheetContent,\r\n  SheetHeader,\r\n  SheetFooter,\r\n  SheetTitle,\r\n  SheetDescription,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,KAA8D;QAA9D,EAAE,GAAG,OAAyD,GAA9D;IACb,qBAAO,6LAAC,qKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;KAFS;AAIT,SAAS,aAAa,KAEgC;QAFhC,EACpB,GAAG,OACiD,GAFhC;IAGpB,qBAAO,6LAAC,qKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,WAAW,KAEgC;QAFhC,EAClB,GAAG,OAC+C,GAFhC;IAGlB,qBAAO,6LAAC,qKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,aAAa,KAGgC;QAHhC,EACpB,SAAS,EACT,GAAG,OACiD,GAHhC;IAIpB,qBACE,6LAAC,qKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,aAAa,KAOrB;QAPqB,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ,GAPqB;IAQpB,qBACE,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,6LAAC,mMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAnCS;AAqCT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAGgC;QAHhC,EAClB,SAAS,EACT,GAAG,OAC+C,GAHhC;IAIlB,qBACE,6LAAC,qKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,KAGgC;QAHhC,EACxB,SAAS,EACT,GAAG,OACqD,GAHhC;IAIxB,qBACE,6LAAC,qKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 1648, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Work/protec/apps/web/src/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction TooltipProvider({\r\n  delayDuration = 0,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\r\n  return (\r\n    <TooltipPrimitive.Provider\r\n      data-slot=\"tooltip-provider\"\r\n      delayDuration={delayDuration}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction Tooltip({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Root>) {\r\n  return (\r\n    <TooltipProvider>\r\n      <TooltipPrimitive.Root data-slot=\"tooltip\" {...props} />\r\n    </TooltipProvider>\r\n  )\r\n}\r\n\r\nfunction TooltipTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\r\n  return <TooltipPrimitive.Trigger data-slot=\"tooltip-trigger\" {...props} />\r\n}\r\n\r\nfunction TooltipContent({\r\n  className,\r\n  sideOffset = 0,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Content>) {\r\n  return (\r\n    <TooltipPrimitive.Portal>\r\n      <TooltipPrimitive.Content\r\n        data-slot=\"tooltip-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <TooltipPrimitive.Arrow className=\"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]\" />\r\n      </TooltipPrimitive.Content>\r\n    </TooltipPrimitive.Portal>\r\n  )\r\n}\r\n\r\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,gBAAgB,KAGgC;QAHhC,EACvB,gBAAgB,CAAC,EACjB,GAAG,OACoD,GAHhC;IAIvB,qBACE,6LAAC,sKAAA,CAAA,WAAyB;QACxB,aAAU;QACV,eAAe;QACd,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,QAAQ,KAEoC;QAFpC,EACf,GAAG,OACgD,GAFpC;IAGf,qBACE,6LAAC;kBACC,cAAA,6LAAC,sKAAA,CAAA,OAAqB;YAAC,aAAU;YAAW,GAAG,KAAK;;;;;;;;;;;AAG1D;MARS;AAUT,SAAS,eAAe,KAEgC;QAFhC,EACtB,GAAG,OACmD,GAFhC;IAGtB,qBAAO,6LAAC,sKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;MAJS;AAMT,SAAS,eAAe,KAKgC;QALhC,EACtB,SAAS,EACT,aAAa,CAAC,EACd,QAAQ,EACR,GAAG,OACmD,GALhC;IAMtB,qBACE,6LAAC,sKAAA,CAAA,SAAuB;kBACtB,cAAA,6LAAC,sKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,0aACA;YAED,GAAG,KAAK;;gBAER;8BACD,6LAAC,sKAAA,CAAA,QAAsB;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI1C;MAtBS", "debugId": null}}, {"offset": {"line": 1749, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Work/protec/apps/web/src/components/ui/sidebar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { VariantProps, cva } from \"class-variance-authority\"\r\nimport { PanelLeftIcon } from \"lucide-react\"\r\n\r\nimport { useIsMobile } from \"@/hooks/use-mobile\"\r\nimport { cn } from \"@/lib/utils\"\r\nimport { Button } from \"@/components/ui/button\"\r\nimport { Input } from \"@/components/ui/input\"\r\nimport { Separator } from \"@/components/ui/separator\"\r\nimport {\r\n  Sheet,\r\n  SheetContent,\r\n  SheetDescription,\r\n  SheetHeader,\r\n  SheetTitle,\r\n} from \"@/components/ui/sheet\"\r\nimport { Skeleton } from \"@/components/ui/skeleton\"\r\nimport {\r\n  Toolt<PERSON>,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\"\r\n\r\nconst SIDEBAR_COOKIE_NAME = \"sidebar_state\"\r\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7\r\nconst SIDEBAR_WIDTH = \"16rem\"\r\nconst SIDEBAR_WIDTH_MOBILE = \"18rem\"\r\nconst SIDEBAR_WIDTH_ICON = \"3rem\"\r\nconst SIDEBAR_KEYBOARD_SHORTCUT = \"b\"\r\n\r\ntype SidebarContextProps = {\r\n  state: \"expanded\" | \"collapsed\"\r\n  open: boolean\r\n  setOpen: (open: boolean) => void\r\n  openMobile: boolean\r\n  setOpenMobile: (open: boolean) => void\r\n  isMobile: boolean\r\n  toggleSidebar: () => void\r\n}\r\n\r\nconst SidebarContext = React.createContext<SidebarContextProps | null>(null)\r\n\r\nfunction useSidebar() {\r\n  const context = React.useContext(SidebarContext)\r\n  if (!context) {\r\n    throw new Error(\"useSidebar must be used within a SidebarProvider.\")\r\n  }\r\n\r\n  return context\r\n}\r\n\r\nfunction SidebarProvider({\r\n  defaultOpen = true,\r\n  open: openProp,\r\n  onOpenChange: setOpenProp,\r\n  className,\r\n  style,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  defaultOpen?: boolean\r\n  open?: boolean\r\n  onOpenChange?: (open: boolean) => void\r\n}) {\r\n  const isMobile = useIsMobile()\r\n  const [openMobile, setOpenMobile] = React.useState(false)\r\n\r\n  // This is the internal state of the sidebar.\r\n  // We use openProp and setOpenProp for control from outside the component.\r\n  const [_open, _setOpen] = React.useState(defaultOpen)\r\n  const open = openProp ?? _open\r\n  const setOpen = React.useCallback(\r\n    (value: boolean | ((value: boolean) => boolean)) => {\r\n      const openState = typeof value === \"function\" ? value(open) : value\r\n      if (setOpenProp) {\r\n        setOpenProp(openState)\r\n      } else {\r\n        _setOpen(openState)\r\n      }\r\n\r\n      // This sets the cookie to keep the sidebar state.\r\n      document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`\r\n    },\r\n    [setOpenProp, open]\r\n  )\r\n\r\n  // Helper to toggle the sidebar.\r\n  const toggleSidebar = React.useCallback(() => {\r\n    return isMobile ? setOpenMobile((open) => !open) : setOpen((open) => !open)\r\n  }, [isMobile, setOpen, setOpenMobile])\r\n\r\n  // Adds a keyboard shortcut to toggle the sidebar.\r\n  React.useEffect(() => {\r\n    const handleKeyDown = (event: KeyboardEvent) => {\r\n      if (\r\n        event.key === SIDEBAR_KEYBOARD_SHORTCUT &&\r\n        (event.metaKey || event.ctrlKey)\r\n      ) {\r\n        event.preventDefault()\r\n        toggleSidebar()\r\n      }\r\n    }\r\n\r\n    window.addEventListener(\"keydown\", handleKeyDown)\r\n    return () => window.removeEventListener(\"keydown\", handleKeyDown)\r\n  }, [toggleSidebar])\r\n\r\n  // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\r\n  // This makes it easier to style the sidebar with Tailwind classes.\r\n  const state = open ? \"expanded\" : \"collapsed\"\r\n\r\n  const contextValue = React.useMemo<SidebarContextProps>(\r\n    () => ({\r\n      state,\r\n      open,\r\n      setOpen,\r\n      isMobile,\r\n      openMobile,\r\n      setOpenMobile,\r\n      toggleSidebar,\r\n    }),\r\n    [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]\r\n  )\r\n\r\n  return (\r\n    <SidebarContext.Provider value={contextValue}>\r\n      <TooltipProvider delayDuration={0}>\r\n        <div\r\n          data-slot=\"sidebar-wrapper\"\r\n          style={\r\n            {\r\n              \"--sidebar-width\": SIDEBAR_WIDTH,\r\n              \"--sidebar-width-icon\": SIDEBAR_WIDTH_ICON,\r\n              ...style,\r\n            } as React.CSSProperties\r\n          }\r\n          className={cn(\r\n            \"group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full\",\r\n            className\r\n          )}\r\n          {...props}\r\n        >\r\n          {children}\r\n        </div>\r\n      </TooltipProvider>\r\n    </SidebarContext.Provider>\r\n  )\r\n}\r\n\r\nfunction Sidebar({\r\n  side = \"left\",\r\n  variant = \"sidebar\",\r\n  collapsible = \"offcanvas\",\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  side?: \"left\" | \"right\"\r\n  variant?: \"sidebar\" | \"floating\" | \"inset\"\r\n  collapsible?: \"offcanvas\" | \"icon\" | \"none\"\r\n}) {\r\n  const { isMobile, state, openMobile, setOpenMobile } = useSidebar()\r\n\r\n  if (collapsible === \"none\") {\r\n    return (\r\n      <div\r\n        data-slot=\"sidebar\"\r\n        className={cn(\r\n          \"bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n      </div>\r\n    )\r\n  }\r\n\r\n  if (isMobile) {\r\n    return (\r\n      <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\r\n        <SheetContent\r\n          data-sidebar=\"sidebar\"\r\n          data-slot=\"sidebar\"\r\n          data-mobile=\"true\"\r\n          className=\"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden\"\r\n          style={\r\n            {\r\n              \"--sidebar-width\": SIDEBAR_WIDTH_MOBILE,\r\n            } as React.CSSProperties\r\n          }\r\n          side={side}\r\n        >\r\n          <SheetHeader className=\"sr-only\">\r\n            <SheetTitle>Sidebar</SheetTitle>\r\n            <SheetDescription>Displays the mobile sidebar.</SheetDescription>\r\n          </SheetHeader>\r\n          <div className=\"flex h-full w-full flex-col\">{children}</div>\r\n        </SheetContent>\r\n      </Sheet>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className=\"group peer text-sidebar-foreground hidden md:block\"\r\n      data-state={state}\r\n      data-collapsible={state === \"collapsed\" ? collapsible : \"\"}\r\n      data-variant={variant}\r\n      data-side={side}\r\n      data-slot=\"sidebar\"\r\n    >\r\n      {/* This is what handles the sidebar gap on desktop */}\r\n      <div\r\n        data-slot=\"sidebar-gap\"\r\n        className={cn(\r\n          \"relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear\",\r\n          \"group-data-[collapsible=offcanvas]:w-0\",\r\n          \"group-data-[side=right]:rotate-180\",\r\n          variant === \"floating\" || variant === \"inset\"\r\n            ? \"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]\"\r\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon)\"\r\n        )}\r\n      />\r\n      <div\r\n        data-slot=\"sidebar-container\"\r\n        className={cn(\r\n          \"fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex\",\r\n          side === \"left\"\r\n            ? \"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]\"\r\n            : \"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]\",\r\n          // Adjust the padding for floating and inset variants.\r\n          variant === \"floating\" || variant === \"inset\"\r\n            ? \"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]\"\r\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        <div\r\n          data-sidebar=\"sidebar\"\r\n          data-slot=\"sidebar-inner\"\r\n          className=\"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm\"\r\n        >\r\n          {children}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction SidebarTrigger({\r\n  className,\r\n  onClick,\r\n  ...props\r\n}: React.ComponentProps<typeof Button>) {\r\n  const { toggleSidebar } = useSidebar()\r\n\r\n  return (\r\n    <Button\r\n      data-sidebar=\"trigger\"\r\n      data-slot=\"sidebar-trigger\"\r\n      variant=\"ghost\"\r\n      size=\"icon\"\r\n      className={cn(\"size-7\", className)}\r\n      onClick={(event) => {\r\n        onClick?.(event)\r\n        toggleSidebar()\r\n      }}\r\n      {...props}\r\n    >\r\n      <PanelLeftIcon />\r\n      <span className=\"sr-only\">Toggle Sidebar</span>\r\n    </Button>\r\n  )\r\n}\r\n\r\nfunction SidebarRail({ className, ...props }: React.ComponentProps<\"button\">) {\r\n  const { toggleSidebar } = useSidebar()\r\n\r\n  return (\r\n    <button\r\n      data-sidebar=\"rail\"\r\n      data-slot=\"sidebar-rail\"\r\n      aria-label=\"Toggle Sidebar\"\r\n      tabIndex={-1}\r\n      onClick={toggleSidebar}\r\n      title=\"Toggle Sidebar\"\r\n      className={cn(\r\n        \"hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex\",\r\n        \"in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize\",\r\n        \"[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize\",\r\n        \"hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full\",\r\n        \"[[data-side=left][data-collapsible=offcanvas]_&]:-right-2\",\r\n        \"[[data-side=right][data-collapsible=offcanvas]_&]:-left-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarInset({ className, ...props }: React.ComponentProps<\"main\">) {\r\n  return (\r\n    <main\r\n      data-slot=\"sidebar-inset\"\r\n      className={cn(\r\n        \"bg-background relative flex w-full flex-1 flex-col\",\r\n        \"md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarInput({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof Input>) {\r\n  return (\r\n    <Input\r\n      data-slot=\"sidebar-input\"\r\n      data-sidebar=\"input\"\r\n      className={cn(\"bg-background h-8 w-full shadow-none\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-header\"\r\n      data-sidebar=\"header\"\r\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-footer\"\r\n      data-sidebar=\"footer\"\r\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof Separator>) {\r\n  return (\r\n    <Separator\r\n      data-slot=\"sidebar-separator\"\r\n      data-sidebar=\"separator\"\r\n      className={cn(\"bg-sidebar-border mx-2 w-auto\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-content\"\r\n      data-sidebar=\"content\"\r\n      className={cn(\r\n        \"flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarGroup({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-group\"\r\n      data-sidebar=\"group\"\r\n      className={cn(\"relative flex w-full min-w-0 flex-col p-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarGroupLabel({\r\n  className,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"div\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-group-label\"\r\n      data-sidebar=\"group-label\"\r\n      className={cn(\r\n        \"text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        \"group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarGroupAction({\r\n  className,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-group-action\"\r\n      data-sidebar=\"group-action\"\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground absolute top-3.5 right-3 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        // Increases the hit area of the button on mobile.\r\n        \"after:absolute after:-inset-2 md:after:hidden\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarGroupContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-group-content\"\r\n      data-sidebar=\"group-content\"\r\n      className={cn(\"w-full text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarMenu({ className, ...props }: React.ComponentProps<\"ul\">) {\r\n  return (\r\n    <ul\r\n      data-slot=\"sidebar-menu\"\r\n      data-sidebar=\"menu\"\r\n      className={cn(\"flex w-full min-w-0 flex-col gap-1\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarMenuItem({ className, ...props }: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"sidebar-menu-item\"\r\n      data-sidebar=\"menu-item\"\r\n      className={cn(\"group/menu-item relative\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nconst sidebarMenuButtonVariants = cva(\r\n  \"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\",\r\n        outline:\r\n          \"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]\",\r\n      },\r\n      size: {\r\n        default: \"h-8 text-sm\",\r\n        sm: \"h-7 text-xs\",\r\n        lg: \"h-12 text-sm group-data-[collapsible=icon]:p-0!\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction SidebarMenuButton({\r\n  asChild = false,\r\n  isActive = false,\r\n  variant = \"default\",\r\n  size = \"default\",\r\n  tooltip,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & {\r\n  asChild?: boolean\r\n  isActive?: boolean\r\n  tooltip?: string | React.ComponentProps<typeof TooltipContent>\r\n} & VariantProps<typeof sidebarMenuButtonVariants>) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n  const { isMobile, state } = useSidebar()\r\n\r\n  const button = (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-button\"\r\n      data-sidebar=\"menu-button\"\r\n      data-size={size}\r\n      data-active={isActive}\r\n      className={cn(sidebarMenuButtonVariants({ variant, size }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n\r\n  if (!tooltip) {\r\n    return button\r\n  }\r\n\r\n  if (typeof tooltip === \"string\") {\r\n    tooltip = {\r\n      children: tooltip,\r\n    }\r\n  }\r\n\r\n  return (\r\n    <Tooltip>\r\n      <TooltipTrigger asChild>{button}</TooltipTrigger>\r\n      <TooltipContent\r\n        side=\"right\"\r\n        align=\"center\"\r\n        hidden={state !== \"collapsed\" || isMobile}\r\n        {...tooltip}\r\n      />\r\n    </Tooltip>\r\n  )\r\n}\r\n\r\nfunction SidebarMenuAction({\r\n  className,\r\n  asChild = false,\r\n  showOnHover = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & {\r\n  asChild?: boolean\r\n  showOnHover?: boolean\r\n}) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-action\"\r\n      data-sidebar=\"menu-action\"\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground peer-hover/menu-button:text-sidebar-accent-foreground absolute top-1.5 right-1 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        // Increases the hit area of the button on mobile.\r\n        \"after:absolute after:-inset-2 md:after:hidden\",\r\n        \"peer-data-[size=sm]/menu-button:top-1\",\r\n        \"peer-data-[size=default]/menu-button:top-1.5\",\r\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        showOnHover &&\r\n          \"peer-data-[active=true]/menu-button:text-sidebar-accent-foreground group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 md:opacity-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarMenuBadge({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-menu-badge\"\r\n      data-sidebar=\"menu-badge\"\r\n      className={cn(\r\n        \"text-sidebar-foreground pointer-events-none absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums select-none\",\r\n        \"peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground\",\r\n        \"peer-data-[size=sm]/menu-button:top-1\",\r\n        \"peer-data-[size=default]/menu-button:top-1.5\",\r\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarMenuSkeleton({\r\n  className,\r\n  showIcon = false,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  showIcon?: boolean\r\n}) {\r\n  // Random width between 50 to 90%.\r\n  const width = React.useMemo(() => {\r\n    return `${Math.floor(Math.random() * 40) + 50}%`\r\n  }, [])\r\n\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-menu-skeleton\"\r\n      data-sidebar=\"menu-skeleton\"\r\n      className={cn(\"flex h-8 items-center gap-2 rounded-md px-2\", className)}\r\n      {...props}\r\n    >\r\n      {showIcon && (\r\n        <Skeleton\r\n          className=\"size-4 rounded-md\"\r\n          data-sidebar=\"menu-skeleton-icon\"\r\n        />\r\n      )}\r\n      <Skeleton\r\n        className=\"h-4 max-w-(--skeleton-width) flex-1\"\r\n        data-sidebar=\"menu-skeleton-text\"\r\n        style={\r\n          {\r\n            \"--skeleton-width\": width,\r\n          } as React.CSSProperties\r\n        }\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction SidebarMenuSub({ className, ...props }: React.ComponentProps<\"ul\">) {\r\n  return (\r\n    <ul\r\n      data-slot=\"sidebar-menu-sub\"\r\n      data-sidebar=\"menu-sub\"\r\n      className={cn(\r\n        \"border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarMenuSubItem({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"sidebar-menu-sub-item\"\r\n      data-sidebar=\"menu-sub-item\"\r\n      className={cn(\"group/menu-sub-item relative\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarMenuSubButton({\r\n  asChild = false,\r\n  size = \"md\",\r\n  isActive = false,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"a\"> & {\r\n  asChild?: boolean\r\n  size?: \"sm\" | \"md\"\r\n  isActive?: boolean\r\n}) {\r\n  const Comp = asChild ? Slot : \"a\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-sub-button\"\r\n      data-sidebar=\"menu-sub-button\"\r\n      data-size={size}\r\n      data-active={isActive}\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        \"data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground\",\r\n        size === \"sm\" && \"text-xs\",\r\n        size === \"md\" && \"text-sm\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Sidebar,\r\n  SidebarContent,\r\n  SidebarFooter,\r\n  SidebarGroup,\r\n  SidebarGroupAction,\r\n  SidebarGroupContent,\r\n  SidebarGroupLabel,\r\n  SidebarHeader,\r\n  SidebarInput,\r\n  SidebarInset,\r\n  SidebarMenu,\r\n  SidebarMenuAction,\r\n  SidebarMenuBadge,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  SidebarMenuSkeleton,\r\n  SidebarMenuSub,\r\n  SidebarMenuSubButton,\r\n  SidebarMenuSubItem,\r\n  SidebarProvider,\r\n  SidebarRail,\r\n  SidebarSeparator,\r\n  SidebarTrigger,\r\n  useSidebar,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;;;;;;AAGA;AACA;AACA;AACA;AACA;AAOA;AACA;;;AApBA;;;;;;;;;;;;;AA2BA,MAAM,sBAAsB;AAC5B,MAAM,yBAAyB,KAAK,KAAK,KAAK;AAC9C,MAAM,gBAAgB;AACtB,MAAM,uBAAuB;AAC7B,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B;AAYlC,MAAM,+BAAiB,6JAAA,CAAA,gBAAmB,CAA6B;AAEvE,SAAS;;IACP,MAAM,UAAU,6JAAA,CAAA,aAAgB,CAAC;IACjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;GAPS;AAST,SAAS,gBAAgB,KAYxB;QAZwB,EACvB,cAAc,IAAI,EAClB,MAAM,QAAQ,EACd,cAAc,WAAW,EACzB,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAKJ,GAZwB;;IAavB,MAAM,WAAW;IACjB,MAAM,CAAC,YAAY,cAAc,GAAG,6JAAA,CAAA,WAAc,CAAC;IAEnD,6CAA6C;IAC7C,0EAA0E;IAC1E,MAAM,CAAC,OAAO,SAAS,GAAG,6JAAA,CAAA,WAAc,CAAC;IACzC,MAAM,OAAO,qBAAA,sBAAA,WAAY;IACzB,MAAM,UAAU,6JAAA,CAAA,cAAiB;gDAC/B,CAAC;YACC,MAAM,YAAY,OAAO,UAAU,aAAa,MAAM,QAAQ;YAC9D,IAAI,aAAa;gBACf,YAAY;YACd,OAAO;gBACL,SAAS;YACX;YAEA,kDAAkD;YAClD,SAAS,MAAM,GAAG,AAAC,GAAyB,OAAvB,qBAAoB,KAAiC,OAA9B,WAAU,sBAA2C,OAAvB;QAC5E;+CACA;QAAC;QAAa;KAAK;IAGrB,gCAAgC;IAChC,MAAM,gBAAgB,6JAAA,CAAA,cAAiB;sDAAC;YACtC,OAAO,WAAW;8DAAc,CAAC,OAAS,CAAC;+DAAQ;8DAAQ,CAAC,OAAS,CAAC;;QACxE;qDAAG;QAAC;QAAU;QAAS;KAAc;IAErC,kDAAkD;IAClD,6JAAA,CAAA,YAAe;qCAAC;YACd,MAAM;2DAAgB,CAAC;oBACrB,IACE,MAAM,GAAG,KAAK,6BACd,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,GAC/B;wBACA,MAAM,cAAc;wBACpB;oBACF;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC;6CAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;QACrD;oCAAG;QAAC;KAAc;IAElB,yEAAyE;IACzE,mEAAmE;IACnE,MAAM,QAAQ,OAAO,aAAa;IAElC,MAAM,eAAe,6JAAA,CAAA,UAAa;iDAChC,IAAM,CAAC;gBACL;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACF,CAAC;gDACD;QAAC;QAAO;QAAM;QAAS;QAAU;QAAY;QAAe;KAAc;IAG5E,qBACE,6LAAC,eAAe,QAAQ;QAAC,OAAO;kBAC9B,cAAA,6LAAC,qJAAA,CAAA,kBAAe;YAAC,eAAe;sBAC9B,cAAA,6LAAC;gBACC,aAAU;gBACV,OACE;oBACE,mBAAmB;oBACnB,wBAAwB;oBACxB,GAAG,KAAK;gBACV;gBAEF,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,mFACA;gBAED,GAAG,KAAK;0BAER;;;;;;;;;;;;;;;;AAKX;IAhGS;;QAaU;;;KAbV;AAkGT,SAAS,QAAQ,KAWhB;QAXgB,EACf,OAAO,MAAM,EACb,UAAU,SAAS,EACnB,cAAc,WAAW,EACzB,SAAS,EACT,QAAQ,EACR,GAAG,OAKJ,GAXgB;;IAYf,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEvD,IAAI,gBAAgB,QAAQ;QAC1B,qBACE,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,+EACA;YAED,GAAG,KAAK;sBAER;;;;;;IAGP;IAEA,IAAI,UAAU;QACZ,qBACE,6LAAC,mJAAA,CAAA,QAAK;YAAC,MAAM;YAAY,cAAc;YAAgB,GAAG,KAAK;sBAC7D,cAAA,6LAAC,mJAAA,CAAA,eAAY;gBACX,gBAAa;gBACb,aAAU;gBACV,eAAY;gBACZ,WAAU;gBACV,OACE;oBACE,mBAAmB;gBACrB;gBAEF,MAAM;;kCAEN,6LAAC,mJAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC,mJAAA,CAAA,aAAU;0CAAC;;;;;;0CACZ,6LAAC,mJAAA,CAAA,mBAAgB;0CAAC;;;;;;;;;;;;kCAEpB,6LAAC;wBAAI,WAAU;kCAA+B;;;;;;;;;;;;;;;;;IAItD;IAEA,qBACE,6LAAC;QACC,WAAU;QACV,cAAY;QACZ,oBAAkB,UAAU,cAAc,cAAc;QACxD,gBAAc;QACd,aAAW;QACX,aAAU;;0BAGV,6LAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,2FACA,0CACA,sCACA,YAAY,cAAc,YAAY,UAClC,qFACA;;;;;;0BAGR,6LAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,wHACA,SAAS,SACL,mFACA,oFACJ,sDAAsD;gBACtD,YAAY,cAAc,YAAY,UAClC,6FACA,2HACJ;gBAED,GAAG,KAAK;0BAET,cAAA,6LAAC;oBACC,gBAAa;oBACb,aAAU;oBACV,WAAU;8BAET;;;;;;;;;;;;;;;;;AAKX;IApGS;;QAYgD;;;MAZhD;AAsGT,SAAS,eAAe,KAIc;QAJd,EACtB,SAAS,EACT,OAAO,EACP,GAAG,OACiC,GAJd;;IAKtB,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,6LAAC,oJAAA,CAAA,SAAM;QACL,gBAAa;QACb,aAAU;QACV,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACxB,SAAS,CAAC;YACR,oBAAA,8BAAA,QAAU;YACV;QACF;QACC,GAAG,KAAK;;0BAET,6LAAC,uNAAA,CAAA,gBAAa;;;;;0BACd,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;IAxBS;;QAKmB;;;MALnB;AA0BT,SAAS,YAAY,KAAuD;QAAvD,EAAE,SAAS,EAAE,GAAG,OAAuC,GAAvD;;IACnB,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,6LAAC;QACC,gBAAa;QACb,aAAU;QACV,cAAW;QACX,UAAU,CAAC;QACX,SAAS;QACT,OAAM;QACN,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,mPACA,4EACA,0HACA,2JACA,6DACA,6DACA;QAED,GAAG,KAAK;;;;;;AAGf;IAvBS;;QACmB;;;MADnB;AAyBT,SAAS,aAAa,KAAqD;QAArD,EAAE,SAAS,EAAE,GAAG,OAAqC,GAArD;IACpB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,sDACA,mNACA;QAED,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,aAAa,KAGe;QAHf,EACpB,SAAS,EACT,GAAG,OACgC,GAHf;IAIpB,qBACE,6LAAC,mJAAA,CAAA,QAAK;QACJ,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,cAAc,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACrB,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,cAAc,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACrB,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,iBAAiB,KAGe;QAHf,EACxB,SAAS,EACT,GAAG,OACoC,GAHf;IAIxB,qBACE,6LAAC,uJAAA,CAAA,YAAS;QACR,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,eAAe,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACtB,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,aAAa,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACpB,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;OATS;AAWT,SAAS,kBAAkB,KAI2B;QAJ3B,EACzB,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACiD,GAJ3B;IAKzB,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,4OACA,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;OAnBS;AAqBT,SAAS,mBAAmB,KAI6B;QAJ7B,EAC1B,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACoD,GAJ7B;IAK1B,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,8RACA,kDAAkD;QAClD,iDACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;OArBS;AAuBT,SAAS,oBAAoB,KAGC;QAHD,EAC3B,SAAS,EACT,GAAG,OACyB,GAHD;IAI3B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;QAC/B,GAAG,KAAK;;;;;;AAGf;OAZS;AAcT,SAAS,YAAY,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;OATS;AAWT,SAAS,gBAAgB,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACvB,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf;OATS;AAWT,MAAM,4BAA4B,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EAClC,qzBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,kBAAkB,KAYuB;QAZvB,EACzB,UAAU,KAAK,EACf,WAAW,KAAK,EAChB,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,OAAO,EACP,SAAS,EACT,GAAG,OAK6C,GAZvB;;IAazB,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;IAE5B,MAAM,uBACJ,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;YAAE;YAAS;QAAK,IAAI;QAC3D,GAAG,KAAK;;;;;;IAIb,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,IAAI,OAAO,YAAY,UAAU;QAC/B,UAAU;YACR,UAAU;QACZ;IACF;IAEA,qBACE,6LAAC,qJAAA,CAAA,UAAO;;0BACN,6LAAC,qJAAA,CAAA,iBAAc;gBAAC,OAAO;0BAAE;;;;;;0BACzB,6LAAC,qJAAA,CAAA,iBAAc;gBACb,MAAK;gBACL,OAAM;gBACN,QAAQ,UAAU,eAAe;gBAChC,GAAG,OAAO;;;;;;;;;;;;AAInB;IAhDS;;QAcqB;;;OAdrB;AAkDT,SAAS,kBAAkB,KAQ1B;QAR0B,EACzB,SAAS,EACT,UAAU,KAAK,EACf,cAAc,KAAK,EACnB,GAAG,OAIJ,GAR0B;IASzB,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,oVACA,kDAAkD;QAClD,iDACA,yCACA,gDACA,2CACA,wCACA,eACE,4LACF;QAED,GAAG,KAAK;;;;;;AAGf;OA9BS;AAgCT,SAAS,iBAAiB,KAGI;QAHJ,EACxB,SAAS,EACT,GAAG,OACyB,GAHJ;IAIxB,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,0KACA,4HACA,yCACA,gDACA,2CACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;OApBS;AAsBT,SAAS,oBAAoB,KAM5B;QAN4B,EAC3B,SAAS,EACT,WAAW,KAAK,EAChB,GAAG,OAGJ,GAN4B;;IAO3B,kCAAkC;IAClC,MAAM,QAAQ,6JAAA,CAAA,UAAa;8CAAC;YAC1B,OAAO,AAAC,GAAsC,OAApC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,IAAG;QAChD;6CAAG,EAAE;IAEL,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;QAC5D,GAAG,KAAK;;YAER,0BACC,6LAAC,sJAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;;;;;;0BAGjB,6LAAC,sJAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;gBACb,OACE;oBACE,oBAAoB;gBACtB;;;;;;;;;;;;AAKV;IApCS;OAAA;AAsCT,SAAS,eAAe,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACtB,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,kGACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;OAbS;AAeT,SAAS,mBAAmB,KAGC;QAHD,EAC1B,SAAS,EACT,GAAG,OACwB,GAHD;IAI1B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EAAE,gCAAgC;QAC7C,GAAG,KAAK;;;;;;AAGf;OAZS;AAcT,SAAS,qBAAqB,KAU7B;QAV6B,EAC5B,UAAU,KAAK,EACf,OAAO,IAAI,EACX,WAAW,KAAK,EAChB,SAAS,EACT,GAAG,OAKJ,GAV6B;IAW5B,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,ifACA,0FACA,SAAS,QAAQ,WACjB,SAAS,QAAQ,WACjB,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;OA9BS", "debugId": null}}, {"offset": {"line": 2528, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Work/protec/apps/web/src/components/header.tsx"], "sourcesContent": ["\"use client\";\nimport Link from \"next/link\";\n\nimport { ModeToggle } from \"./mode-toggle\";\nimport UserMenu from \"./user-menu\";\nimport { SidebarTrigger } from \"@/components/ui/sidebar\";\n\nexport default function Header() {\n  const links = [\n    { to: \"/\", label: \"Home\" },\n      { to: \"/dashboard\", label: \"Dashboard\" },\n    { to: \"/ai\", label: \"AI Chat\" },\n  ];\n\n  return (\n    <div>\n      <div className=\"flex flex-row items-center justify-between px-2 py-1\">\n        <nav className=\"flex items-center gap-4 text-lg\">\n          <SidebarTrigger />\n          {links.map(({ to, label }) => {\n            return (\n              <Link key={to} href={to}>\n                {label}\n              </Link>\n            );\n          })}\n        </nav>\n        <div className=\"flex items-center gap-2\">\n          <ModeToggle />\n          <UserMenu />\n        </div>\n      </div>\n      <hr />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AAEA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,QAAQ;QACZ;YAAE,IAAI;YAAK,OAAO;QAAO;QACvB;YAAE,IAAI;YAAc,OAAO;QAAY;QACzC;YAAE,IAAI;YAAO,OAAO;QAAU;KAC/B;IAED,qBACE,6LAAC;;0BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qJAAA,CAAA,iBAAc;;;;;4BACd,MAAM,GAAG,CAAC;oCAAC,EAAE,EAAE,EAAE,KAAK,EAAE;gCACvB,qBACE,6LAAC,+JAAA,CAAA,UAAI;oCAAU,MAAM;8CAClB;mCADQ;;;;;4BAIf;;;;;;;kCAEF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,sJAAA,CAAA,aAAU;;;;;0CACX,6LAAC,oJAAA,CAAA,UAAQ;;;;;;;;;;;;;;;;;0BAGb,6LAAC;;;;;;;;;;;AAGP;KA5BwB", "debugId": null}}, {"offset": {"line": 2636, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Work/protec/apps/web/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nfunction Avatar({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\r\n  return (\r\n    <AvatarPrimitive.Root\r\n      data-slot=\"avatar\"\r\n      className={cn(\r\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AvatarImage({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\r\n  return (\r\n    <AvatarPrimitive.Image\r\n      data-slot=\"avatar-image\"\r\n      className={cn(\"aspect-square size-full\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AvatarFallback({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\r\n  return (\r\n    <AvatarPrimitive.Fallback\r\n      data-slot=\"avatar-fallback\"\r\n      className={cn(\r\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback };\r\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,KAGoC;QAHpC,EACd,SAAS,EACT,GAAG,OAC+C,GAHpC;IAId,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS;AAgBT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,KAGgC;QAHhC,EACtB,SAAS,EACT,GAAG,OACmD,GAHhC;IAItB,qBACE,6LAAC,qKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 2701, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Work/protec/apps/web/src/components/sidebar/nav-user.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport {\r\n  <PERSON>ge<PERSON><PERSON><PERSON>,\r\n  <PERSON>ug,\r\n  ChevronsUpDown,\r\n  CreditCard,\r\n  HelpCircle,\r\n  LogOut,\r\n  Settings,\r\n  Sparkles,\r\n  Store,\r\n  Users,\r\n} from \"lucide-react\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport {\r\n  SidebarMenu,\r\n  SidebarMenuItem,\r\n  useSidebar,\r\n} from \"@/components/ui/sidebar\";\r\nimport { authClient } from \"@/lib/auth-client\";\r\nimport { useQuery } from \"@tanstack/react-query\";\r\nimport Link from \"next/link\";\r\nimport { useRouter } from \"next/navigation\";\r\n\r\nexport function NavUser() {\r\n  const { isMobile } = useSidebar();\r\n  const router = useRouter();\r\n\r\n  const { data: session } = useQuery({\r\n    queryKey: [\"session\"],\r\n    queryFn: async () => {\r\n      const {data: session} = await authClient.getSession();\r\n      return session;\r\n    },\r\n  });\r\n\r\n  if (!session) return null;\r\n\r\n  return (\r\n    <SidebarMenu>\r\n      <SidebarMenuItem>\r\n        <DropdownMenu>\r\n          <DropdownMenuTrigger asChild>\r\n            <button className=\"group relative w-full overflow-hidden rounded-lg border bg-linear-to-br from-primary/20 to-muted px-3 py-3 shadow-lg transition-all duration-200 hover:shadow-xl\">\r\n              <div className=\"absolute inset-0 bg-linear-to-r to-primary/10 from-secondary/10 opacity-0 transition-opacity duration-200 group-hover:opacity-100\" />\r\n              <div className=\"relative flex items-center gap-3\">\r\n                <Avatar>\r\n                  <AvatarImage src={session.user.image || \"\"} />\r\n                  <AvatarFallback>{session.user.name.split(\" \")[0]}</AvatarFallback>\r\n                </Avatar>\r\n                <div className=\"grid flex-1 text-left text-sm leading-tight\">\r\n                  <span className=\"truncate font-semibold\">{session.user.name}</span>\r\n                  <span className=\"truncate text-xs text-muted-foreground\">\r\n                    {session.user.email}\r\n                  </span>\r\n                </div>\r\n                <ChevronsUpDown className=\"ml-auto size-4 text-muted-foreground transition-transform duration-200\" />\r\n              </div>\r\n            </button>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent\r\n            className=\"w-(--radix-dropdown-menu-trigger-width) min-w-64 rounded-lg p-2\"\r\n            side={isMobile ? \"bottom\" : \"right\"}\r\n            align=\"end\"\r\n            sideOffset={8}\r\n          >\r\n            <DropdownMenuLabel className=\"p-0\">\r\n              <div className=\"flex items-center gap-3 p-0.5 rounded-md bg-linear-to-br from-background to-muted\">\r\n                <Avatar className=\"h-10 w-10 rounded-lg border shadow-xs\">\r\n                  <AvatarFallback className=\"rounded-lg bg-linear-to-br from-primary/20 to-secondary/20 font-medium\">\r\n                    {session.user.name.split(\" \")[0]}\r\n                  </AvatarFallback>\r\n                </Avatar>\r\n                <div className=\"grid flex-1 gap-1 text-left\">\r\n                  <span className=\"text-sm font-semibold\">{session.user.name}</span>\r\n                  <span className=\"truncate text-xs text-muted-foreground\">\r\n                    {session.user.email}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </DropdownMenuLabel>\r\n            <DropdownMenuSeparator className=\"my-2\" />\r\n            <DropdownMenuGroup>\r\n              <DropdownMenuItem asChild>\r\n                <Link\r\n                  className=\"flex items-center gap-2 rounded-md px-2 py-1.5\"\r\n                  href=\"/settings\"\r\n                >\r\n                  <Settings className=\"size-4 text-zinc-500\" />\r\n                  <span>Settings</span>\r\n                </Link>\r\n              </DropdownMenuItem>\r\n            </DropdownMenuGroup>\r\n            <DropdownMenuSeparator className=\"my-2\" />\r\n            <DropdownMenuGroup>\r\n              <DropdownMenuItem asChild>\r\n                <Link\r\n                  className=\"flex items-center gap-2 rounded-md px-2 py-1.5\"\r\n                  href=\"/settings/billing\"\r\n                >\r\n                  <CreditCard className=\"size-4 text-violet-500\" />\r\n                  <span>Billing & Subscription</span>\r\n                </Link>\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem asChild>\r\n                <Link\r\n                  className=\"flex items-center gap-2 rounded-md px-2 py-1.5\"\r\n                  href=\"/settings/account\"\r\n                >\r\n                  <BadgeCheck className=\"size-4 text-green-500\" />\r\n                  <span>My Account</span>\r\n                </Link>\r\n              </DropdownMenuItem>\r\n            </DropdownMenuGroup>\r\n            <DropdownMenuSeparator className=\"my-2\" />\r\n            <DropdownMenuGroup>\r\n              <DropdownMenuItem asChild>\r\n                <Link\r\n                  className=\"flex items-center gap-2 rounded-md px-2 py-1.5\"\r\n                  href=\"/report-bugs\"\r\n                >\r\n                  <Bug className=\"size-4 text-orange-500\" />\r\n                  <span>Report bugs</span>\r\n                </Link>\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem asChild>\r\n                <Link\r\n                  className=\"flex items-center gap-2 rounded-md px-2 py-1.5\"\r\n                  href=\"/support\"\r\n                >\r\n                  <HelpCircle className=\"size-4 text-blue-500\" />\r\n                  <span>Support</span>\r\n                </Link>\r\n              </DropdownMenuItem>\r\n            </DropdownMenuGroup>\r\n            <DropdownMenuSeparator className=\"my-2\" />\r\n            <DropdownMenuItem\r\n              onClick={async () => {\r\n                await authClient.signOut();\r\n                router.refresh();\r\n              }}\r\n              className=\"w-full cursor-pointer gap-2 rounded-md bg-destructive px-2 py-1.5 text-destructive-foreground hover:bg-destructive/90\"\r\n            >\r\n              <LogOut className=\"size-4\" />\r\n              <span>Log out</span>\r\n            </DropdownMenuItem>\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n      </SidebarMenuItem>\r\n    </SidebarMenu>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AASA;AAKA;AACA;AACA;AACA;;;AAhCA;;;;;;;;;AAkCO,SAAS;;IACd,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD;IAC9B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACjC,UAAU;YAAC;SAAU;QACrB,OAAO;gCAAE;gBACP,MAAM,EAAC,MAAM,OAAO,EAAC,GAAG,MAAM,8IAAA,CAAA,aAAU,CAAC,UAAU;gBACnD,OAAO;YACT;;IACF;IAEA,IAAI,CAAC,SAAS,OAAO;IAErB,qBACE,6LAAC,qJAAA,CAAA,cAAW;kBACV,cAAA,6LAAC,qJAAA,CAAA,kBAAe;sBACd,cAAA,6LAAC,8JAAA,CAAA,eAAY;;kCACX,6LAAC,8JAAA,CAAA,sBAAmB;wBAAC,OAAO;kCAC1B,cAAA,6LAAC;4BAAO,WAAU;;8CAChB,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oJAAA,CAAA,SAAM;;8DACL,6LAAC,oJAAA,CAAA,cAAW;oDAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,IAAI;;;;;;8DACxC,6LAAC,oJAAA,CAAA,iBAAc;8DAAE,QAAQ,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;;;;;;;;;;;;sDAElD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAA0B,QAAQ,IAAI,CAAC,IAAI;;;;;;8DAC3D,6LAAC;oDAAK,WAAU;8DACb,QAAQ,IAAI,CAAC,KAAK;;;;;;;;;;;;sDAGvB,6LAAC,iOAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAIhC,6LAAC,8JAAA,CAAA,sBAAmB;wBAClB,WAAU;wBACV,MAAM,WAAW,WAAW;wBAC5B,OAAM;wBACN,YAAY;;0CAEZ,6LAAC,8JAAA,CAAA,oBAAiB;gCAAC,WAAU;0CAC3B,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oJAAA,CAAA,SAAM;4CAAC,WAAU;sDAChB,cAAA,6LAAC,oJAAA,CAAA,iBAAc;gDAAC,WAAU;0DACvB,QAAQ,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;;;;;;;;;;;sDAGpC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAyB,QAAQ,IAAI,CAAC,IAAI;;;;;;8DAC1D,6LAAC;oDAAK,WAAU;8DACb,QAAQ,IAAI,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;0CAK3B,6LAAC,8JAAA,CAAA,wBAAqB;gCAAC,WAAU;;;;;;0CACjC,6LAAC,8JAAA,CAAA,oBAAiB;0CAChB,cAAA,6LAAC,8JAAA,CAAA,mBAAgB;oCAAC,OAAO;8CACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCACH,WAAU;wCACV,MAAK;;0DAEL,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;0CAIZ,6LAAC,8JAAA,CAAA,wBAAqB;gCAAC,WAAU;;;;;;0CACjC,6LAAC,8JAAA,CAAA,oBAAiB;;kDAChB,6LAAC,8JAAA,CAAA,mBAAgB;wCAAC,OAAO;kDACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CACH,WAAU;4CACV,MAAK;;8DAEL,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,6LAAC;8DAAK;;;;;;;;;;;;;;;;;kDAGV,6LAAC,8JAAA,CAAA,mBAAgB;wCAAC,OAAO;kDACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CACH,WAAU;4CACV,MAAK;;8DAEL,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;0CAIZ,6LAAC,8JAAA,CAAA,wBAAqB;gCAAC,WAAU;;;;;;0CACjC,6LAAC,8JAAA,CAAA,oBAAiB;;kDAChB,6LAAC,8JAAA,CAAA,mBAAgB;wCAAC,OAAO;kDACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CACH,WAAU;4CACV,MAAK;;8DAEL,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;8DACf,6LAAC;8DAAK;;;;;;;;;;;;;;;;;kDAGV,6LAAC,8JAAA,CAAA,mBAAgB;wCAAC,OAAO;kDACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CACH,WAAU;4CACV,MAAK;;8DAEL,6LAAC,iOAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;0CAIZ,6LAAC,8JAAA,CAAA,wBAAqB;gCAAC,WAAU;;;;;;0CACjC,6LAAC,8JAAA,CAAA,mBAAgB;gCACf,SAAS;oCACP,MAAM,8IAAA,CAAA,aAAU,CAAC,OAAO;oCACxB,OAAO,OAAO;gCAChB;gCACA,WAAU;;kDAEV,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB;GA/HgB;;QACO,qJAAA,CAAA,aAAU;QAChB,qIAAA,CAAA,YAAS;QAEE,8KAAA,CAAA,WAAQ;;;KAJpB", "debugId": null}}, {"offset": {"line": 3172, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Work/protec/apps/web/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\";\r\nimport { Slot } from \"@radix-ui/react-slot\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90 dark:bg-primary/80 dark:text-primary-foreground dark:[a&]:hover:bg-primary/70\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90 dark:bg-secondary/80 dark:text-secondary-foreground dark:[a&]:hover:bg-secondary/70\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-destructive-foreground [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/80 dark:text-destructive-foreground dark:[a&]:hover:bg-destructive/70\",\r\n        success:\r\n          \"border-transparent bg-success text-success-foreground [a&]:hover:bg-success/90 dark:bg-success/80 dark:text-success-foreground dark:[a&]:hover:bg-success/70\",\r\n        warning:\r\n          \"border-transparent bg-warning text-warning-foreground [a&]:hover:bg-warning/90 dark:bg-warning/80 dark:text-warning-foreground dark:[a&]:hover:bg-warning/70\",\r\n        info: \"border-transparent bg-info text-info-foreground [a&]:hover:bg-info/90 dark:bg-info/80 dark:text-info-foreground dark:[a&]:hover:bg-info/70\",\r\n        outline:\r\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground dark:text-foreground dark:[a&]:hover:bg-accent/80 dark:[a&]:hover:text-accent-foreground\",\r\n        ghost:\r\n          \"border-transparent bg-background/40 text-foreground hover:bg-accent/50 dark:bg-background/40 dark:text-foreground dark:hover:bg-accent/30\",\r\n        dark: \"border-transparent bg-zinc-900 text-zinc-50 [a&]:hover:bg-zinc-900/90 dark:bg-zinc-800 dark:text-zinc-50 dark:[a&]:hover:bg-zinc-800/90\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n);\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"span\"> &\r\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"span\";\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"badge\"\r\n      className={cn(badgeVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Badge, badgeVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;YACF,SACE;YACF,MAAM;YACN,SACE;YACF,OACE;YACF,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,KAM6C;QAN7C,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD,GAN7C;IAOb,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 3230, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Work/protec/apps/web/src/components/sidebar/nav-menu.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport { type LucideIcon } from \"lucide-react\";\r\nimport {\r\n  SidebarGroup,\r\n  SidebarGroupLabel,\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n} from \"../ui/sidebar\";\r\nimport Link from \"next/link\";\r\nimport { usePathname } from \"next/navigation\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Badge } from \"../ui/badge\";\r\n\r\nconst NavMenu = ({\r\n  items,\r\n  title,\r\n}: {\r\n  items: {\r\n    name: string;\r\n    url: string;\r\n    icon: LucideIcon | any;\r\n    process?: boolean;\r\n    new?: boolean;\r\n  }[];\r\n  title: string;\r\n}) => {\r\n  const pathname = usePathname();\r\n  return (\r\n    <SidebarGroup className=\"group-data-[collapsible=icon]:hidden\">\r\n      <SidebarGroupLabel className=\"dark: text-gray-500 mb-1.5 text-sm\">\r\n        {title}\r\n      </SidebarGroupLabel>\r\n      <SidebarMenu>\r\n        {items.map((item) => {\r\n          const isActive = pathname.startsWith(item.url);\r\n          return (\r\n            <SidebarMenuItem\r\n              key={item.name}\r\n              className={cn(\r\n                isActive &&\r\n                  \"rounded-md border-r-4 bg-linear-to-r from-transparent to-primary/20 border-r-primary\"\r\n              )}\r\n            >\r\n              <SidebarMenuButton\r\n                className=\"hover:bg-linear-to-r hover:from-transparent hover:to-primary/20\"\r\n                asChild\r\n              >\r\n                <Link\r\n                  href={item.url}\r\n                  className=\"gap-4 w-full flex items-center \"\r\n                >\r\n                  <item.icon\r\n                    className={cn(\r\n                      \"dark:text-gray-400 text-gray-700\",\r\n                      isActive && \"\"\r\n                    )}\r\n                  />\r\n                  <span className={cn(\"font-medium\")}>{item.name}</span>\r\n\r\n                  {item.process && (\r\n                    <Badge className=\"px-1 justify-end ml-auto text-right py-0.5 text-xs\">\r\n                      Coming Soon\r\n                    </Badge>\r\n                  )}\r\n                </Link>\r\n              </SidebarMenuButton>\r\n            </SidebarMenuItem>\r\n          );\r\n        })}\r\n      </SidebarMenu>\r\n    </SidebarGroup>\r\n  );\r\n};\r\n\r\nexport default NavMenu;\r\n"], "names": [], "mappings": ";;;;AAEA;AAOA;AACA;AACA;AACA;;;;;;;;AAEA,MAAM,UAAU;QAAC,EACf,KAAK,EACL,KAAK,EAUN;;IACC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,qBACE,6LAAC,qJAAA,CAAA,eAAY;QAAC,WAAU;;0BACtB,6LAAC,qJAAA,CAAA,oBAAiB;gBAAC,WAAU;0BAC1B;;;;;;0BAEH,6LAAC,qJAAA,CAAA,cAAW;0BACT,MAAM,GAAG,CAAC,CAAC;oBACV,MAAM,WAAW,SAAS,UAAU,CAAC,KAAK,GAAG;oBAC7C,qBACE,6LAAC,qJAAA,CAAA,kBAAe;wBAEd,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,YACE;kCAGJ,cAAA,6LAAC,qJAAA,CAAA,oBAAiB;4BAChB,WAAU;4BACV,OAAO;sCAEP,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM,KAAK,GAAG;gCACd,WAAU;;kDAEV,6LAAC,KAAK,IAAI;wCACR,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,oCACA,YAAY;;;;;;kDAGhB,6LAAC;wCAAK,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EAAE;kDAAiB,KAAK,IAAI;;;;;;oCAE7C,KAAK,OAAO,kBACX,6LAAC,mJAAA,CAAA,QAAK;wCAAC,WAAU;kDAAqD;;;;;;;;;;;;;;;;;uBAvBvE,KAAK,IAAI;;;;;gBA+BpB;;;;;;;;;;;;AAIR;GA3DM;;QAaa,qIAAA,CAAA,cAAW;;;KAbxB;uCA6DS", "debugId": null}}, {"offset": {"line": 3343, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Work/protec/apps/web/src/components/sidebar/nav-header.tsx"], "sourcesContent": ["\r\nimport { useQuery } from \"@tanstack/react-query\";\r\nimport React from \"react\";\r\n\r\nconst NavHeader = () => {\r\n  //\r\n\r\n\r\n\r\n  return (\r\n    <button className=\"group px-2 relative w-full bg-muted/10 overflow-hidden rounded-lg py-3 transition-all duration-200 shadow-xl\">\r\n      <div className=\"relative flex items-center gap-3\">\r\n        <div className=\"relative h-9 w-9 overflow-hidden rounded-lg\">\r\n          <img\r\n            src=\"/icon.png\"\r\n            className=\" object-cover transition-transform duration-200 group-hover:scale-110\"\r\n            alt=\"User Icon\"\r\n          />\r\n        </div>\r\n        <div className=\"grid flex-1 text-left text-sm leading-tight\">\r\n          <span className=\"truncate font-semibold mb-0.5\">Protec</span>\r\n          <span className=\"truncate text-xs text-muted-foreground font-medium\">\r\n            V1.0.0\r\n          </span>\r\n        </div>\r\n      </div>\r\n    </button>\r\n  );\r\n};\r\n\r\nexport default NavHeader;\r\n"], "names": [], "mappings": ";;;;;AAIA,MAAM,YAAY;IAChB,EAAE;IAIF,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBACC,KAAI;wBACJ,WAAU;wBACV,KAAI;;;;;;;;;;;8BAGR,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,WAAU;sCAAgC;;;;;;sCAChD,6LAAC;4BAAK,WAAU;sCAAqD;;;;;;;;;;;;;;;;;;;;;;;AAO/E;KAxBM;uCA0BS", "debugId": null}}, {"offset": {"line": 3421, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Work/protec/apps/web/src/components/sidebar/app-sidebar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport {\r\n  LayoutDashboard,\r\n  LifeBuoy,\r\n  MessageCircle,\r\n  Users,\r\n  Settings,\r\n  BarChart2,\r\n  Calendar,\r\n  Bell,\r\n  Newspaper,\r\n  Building2,\r\n  HandCoins,\r\n  UserPlus,\r\n} from \"lucide-react\";\r\nimport { NavUser } from \"@/components/sidebar/nav-user\";\r\nimport {\r\n  Sidebar,\r\n  SidebarContent,\r\n  SidebarFooter,\r\n  SidebarGroup,\r\n  SidebarGroupContent,\r\n  SidebarHeader,\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n} from \"@/components/ui/sidebar\";\r\nimport NavMenu from \"./nav-menu\";\r\nimport Link from \"next/link\";\r\n\r\n\r\nimport NavHeader from \"./nav-header\";\r\n\r\ntype NavGroup = {\r\n  name: string;\r\n  url: string;\r\n  icon: React.ElementType;\r\n  process?: boolean;\r\n  new?: boolean;\r\n};\r\n\r\ntype NavData = {\r\n  navMain: NavGroup[];\r\n  community: NavGroup[];\r\n  organization: NavGroup[];\r\n  navSecondary: NavGroup[];\r\n};\r\n\r\nconst data: NavData = {\r\n  navMain: [\r\n    {\r\n      name: \"Dashboard\",\r\n      url: \"/dashboard\",\r\n      icon: LayoutDashboard,\r\n    },\r\n    {\r\n      name: \"Feed\",\r\n      url: \"/feed\",\r\n      icon: Newspaper,\r\n    },\r\n    {\r\n      name: \"Directory\",\r\n      url: \"/directory\",\r\n      icon: Users,\r\n    },\r\n    {\r\n      name: \"Messages\",\r\n      url: \"/messages\",\r\n      icon: MessageCircle,\r\n    },\r\n  ],\r\n  community: [\r\n    {\r\n      name: \"Connections\",\r\n      url: \"/connections\",\r\n      icon: UserPlus,\r\n    },\r\n    {\r\n      name: \"Events\",\r\n      url: \"/events\",\r\n      icon: Calendar,\r\n    },\r\n    {\r\n      name: \"News\",\r\n      url: \"/news\",\r\n      icon: Newspaper,\r\n    },\r\n    {\r\n      name: \"Donations\",\r\n      url: \"/donations\",\r\n      icon: HandCoins,\r\n    },\r\n    {\r\n      name: \"Notifications\",\r\n      url: \"/notifications\",\r\n      icon: Bell,\r\n    },\r\n  ],\r\n  organization: [\r\n    {\r\n      name: \"Organizations\",\r\n      url: \"/organizations\",\r\n      icon: Building2,\r\n    },\r\n    {\r\n      name: \"Analytics\",\r\n      url: \"/analytics\",\r\n      icon: BarChart2,\r\n    },\r\n  ],\r\n  navSecondary: [\r\n    {\r\n      name: \"Settings\",\r\n      url: \"/settings\",\r\n      icon: Settings,\r\n    },\r\n    {\r\n      name: \"Support\",\r\n      url: \"/support\",\r\n      icon: LifeBuoy,\r\n    },\r\n  ],\r\n};\r\n\r\nexport function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {\r\n  return (\r\n    <Sidebar variant=\"floating\" {...props}>\r\n      <SidebarHeader>\r\n        <SidebarMenu>\r\n          <SidebarMenuItem>\r\n            <SidebarMenuButton size=\"sm\" asChild>\r\n              <NavHeader />\r\n            </SidebarMenuButton>\r\n          </SidebarMenuItem>\r\n        </SidebarMenu>\r\n      </SidebarHeader>\r\n      <SidebarContent>\r\n        <div className=\"grow\">\r\n          <NavMenu items={data.navMain} title=\"Overview\" />\r\n          <NavMenu items={data.community} title=\"Community\" />\r\n          <NavMenu items={data.organization} title=\"Organization\" />\r\n        </div>\r\n        <SidebarGroup {...props}>\r\n          <SidebarGroupContent>\r\n            <SidebarMenu>\r\n              {data.navSecondary.map((item) => (\r\n                <SidebarMenuItem key={item.name}>\r\n                  <SidebarMenuButton asChild size=\"sm\">\r\n                    <Link href={item.url}>\r\n                      <item.icon />\r\n                      <span>{item.name}</span>\r\n                    </Link>\r\n                  </SidebarMenuButton>\r\n                </SidebarMenuItem>\r\n              ))}\r\n            </SidebarMenu>\r\n          </SidebarGroupContent>\r\n        </SidebarGroup>\r\n      </SidebarContent>\r\n      <SidebarFooter>\r\n        <NavUser />\r\n      </SidebarFooter>\r\n    </Sidebar>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AACA;AAWA;AACA;AAGA;AAjCA;;;;;;;;AAkDA,MAAM,OAAgB;IACpB,SAAS;QACP;YACE,MAAM;YACN,KAAK;YACL,MAAM,+NAAA,CAAA,kBAAe;QACvB;QACA;YACE,MAAM;YACN,KAAK;YACL,MAAM,+MAAA,CAAA,YAAS;QACjB;QACA;YACE,MAAM;YACN,KAAK;YACL,MAAM,uMAAA,CAAA,QAAK;QACb;QACA;YACE,MAAM;YACN,KAAK;YACL,MAAM,2NAAA,CAAA,gBAAa;QACrB;KACD;IACD,WAAW;QACT;YACE,MAAM;YACN,KAAK;YACL,MAAM,iNAAA,CAAA,WAAQ;QAChB;QACA;YACE,MAAM;YACN,KAAK;YAC<PERSON>,MAAM,6MAAA,CAAA,WAAQ;QAChB;QACA;YACE,MAAM;YACN,KAAK;YACL,MAAM,+MAAA,CAAA,YAAS;QACjB;QACA;YACE,MAAM;YACN,KAAK;YACL,MAAM,mNAAA,CAAA,YAAS;QACjB;QACA;YACE,MAAM;YACN,KAAK;YACL,MAAM,qMAAA,CAAA,OAAI;QACZ;KACD;IACD,cAAc;QACZ;YACE,MAAM;YACN,KAAK;YACL,MAAM,mNAAA,CAAA,YAAS;QACjB;QACA;YACE,MAAM;YACN,KAAK;YACL,MAAM,mOAAA,CAAA,YAAS;QACjB;KACD;IACD,cAAc;QACZ;YACE,MAAM;YACN,KAAK;YACL,MAAM,6MAAA,CAAA,WAAQ;QAChB;QACA;YACE,MAAM;YACN,KAAK;YACL,MAAM,iNAAA,CAAA,WAAQ;QAChB;KACD;AACH;AAEO,SAAS,WAAW,KAAkD;QAAlD,EAAE,GAAG,OAA6C,GAAlD;IACzB,qBACE,6LAAC,qJAAA,CAAA,UAAO;QAAC,SAAQ;QAAY,GAAG,KAAK;;0BACnC,6LAAC,qJAAA,CAAA,gBAAa;0BACZ,cAAA,6LAAC,qJAAA,CAAA,cAAW;8BACV,cAAA,6LAAC,qJAAA,CAAA,kBAAe;kCACd,cAAA,6LAAC,qJAAA,CAAA,oBAAiB;4BAAC,MAAK;4BAAK,OAAO;sCAClC,cAAA,6LAAC,gKAAA,CAAA,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;0BAKlB,6LAAC,qJAAA,CAAA,iBAAc;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,8JAAA,CAAA,UAAO;gCAAC,OAAO,KAAK,OAAO;gCAAE,OAAM;;;;;;0CACpC,6LAAC,8JAAA,CAAA,UAAO;gCAAC,OAAO,KAAK,SAAS;gCAAE,OAAM;;;;;;0CACtC,6LAAC,8JAAA,CAAA,UAAO;gCAAC,OAAO,KAAK,YAAY;gCAAE,OAAM;;;;;;;;;;;;kCAE3C,6LAAC,qJAAA,CAAA,eAAY;wBAAE,GAAG,KAAK;kCACrB,cAAA,6LAAC,qJAAA,CAAA,sBAAmB;sCAClB,cAAA,6LAAC,qJAAA,CAAA,cAAW;0CACT,KAAK,YAAY,CAAC,GAAG,CAAC,CAAC,qBACtB,6LAAC,qJAAA,CAAA,kBAAe;kDACd,cAAA,6LAAC,qJAAA,CAAA,oBAAiB;4CAAC,OAAO;4CAAC,MAAK;sDAC9B,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAM,KAAK,GAAG;;kEAClB,6LAAC,KAAK,IAAI;;;;;kEACV,6LAAC;kEAAM,KAAK,IAAI;;;;;;;;;;;;;;;;;uCAJA,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;0BAazC,6LAAC,qJAAA,CAAA,gBAAa;0BACZ,cAAA,6LAAC,8JAAA,CAAA,UAAO;;;;;;;;;;;;;;;;AAIhB;KAxCgB", "debugId": null}}]}