{"name": "protec", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"check": "biome check --write .", "dev": "turbo dev", "build:web": "turbo run build --filter=web", "build:server": "turbo run build --filter=server", "start:web": "turbo run start --filter=web", "start:server": "turbo run start --filter=server", "check-types": "turbo check-types", "postinstall": "pnpm run db:generate", "dev:native": "turbo -F native dev", "dev:web": "turbo -F web dev", "dev:server": "turbo -F server dev", "db:push": "turbo -F server db:push", "db:studio": "turbo -F server db:studio", "db:generate": "turbo -F server db:generate", "db:migrate": "turbo -F server db:migrate", "update": "pnpm update --recursive", "update:latest": "pnpm update --recursive --latest", "update:interactive": "pnpm update --recursive --interactive", "outdated": "pnpm outdated --recursive"}, "devDependencies": {"@biomejs/biome": "^2.1.4", "turbo": "^2.5.5"}, "packageManager": "pnpm@10.14.0"}