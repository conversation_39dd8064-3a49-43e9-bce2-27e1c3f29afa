{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Work/protec/apps/web/src/app/dashboard/page.tsx"], "sourcesContent": ["\"use client\"\nimport { authClient } from \"@/lib/auth-client\";\nimport { useQuery } from \"@tanstack/react-query\";\nimport { orpc } from \"@/utils/orpc\";\nimport { useRouter } from \"next/navigation\";\nimport { useEffect } from \"react\";\n\nexport default function Dashboard() {\n  const router = useRouter();\n  const { data: session, isPending } = authClient.useSession();\n\n  const privateData = useQuery(orpc.privateData.queryOptions());\n\n  useEffect(() => {\n    if (!session && !isPending) {\n      router.push(\"/login\");\n    }\n  }, [session, isPending]);\n\n  if (isPending) {\n    return <div>Loading...</div>;\n  }\n\n  return (\n    <div>\n      <h1>Dashboard</h1>\n      <p>Welcome {session?.user.name}</p>\n      <p>privateData: {privateData.data?.message}</p>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AALA;;;;;;;AAOe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,MAAM,OAAO,EAAE,SAAS,EAAE,GAAG,2IAAA,CAAA,aAAU,CAAC,UAAU;IAE1D,MAAM,cAAc,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE,mIAAA,CAAA,OAAI,CAAC,WAAW,CAAC,YAAY;IAE1D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,WAAW;YAC1B,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAS;KAAU;IAEvB,IAAI,WAAW;QACb,qBAAO,8OAAC;sBAAI;;;;;;IACd;IAEA,qBACE,8OAAC;;0BACC,8OAAC;0BAAG;;;;;;0BACJ,8OAAC;;oBAAE;oBAAS,SAAS,KAAK;;;;;;;0BAC1B,8OAAC;;oBAAE;oBAAc,YAAY,IAAI,EAAE;;;;;;;;;;;;;AAGzC", "debugId": null}}]}