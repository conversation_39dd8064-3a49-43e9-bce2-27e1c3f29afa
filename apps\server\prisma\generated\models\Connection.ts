
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `Connection` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model Connection
 * 
 */
export type ConnectionModel = runtime.Types.Result.DefaultSelection<Prisma.$ConnectionPayload>

export type AggregateConnection = {
  _count: ConnectionCountAggregateOutputType | null
  _min: ConnectionMinAggregateOutputType | null
  _max: ConnectionMaxAggregateOutputType | null
}

export type ConnectionMinAggregateOutputType = {
  id: string | null
  requesterId: string | null
  requestedId: string | null
  status: $Enums.ConnectionStatus | null
  requestedAt: Date | null
  acceptedAt: Date | null
}

export type ConnectionMaxAggregateOutputType = {
  id: string | null
  requesterId: string | null
  requestedId: string | null
  status: $Enums.ConnectionStatus | null
  requestedAt: Date | null
  acceptedAt: Date | null
}

export type ConnectionCountAggregateOutputType = {
  id: number
  requesterId: number
  requestedId: number
  status: number
  requestedAt: number
  acceptedAt: number
  _all: number
}


export type ConnectionMinAggregateInputType = {
  id?: true
  requesterId?: true
  requestedId?: true
  status?: true
  requestedAt?: true
  acceptedAt?: true
}

export type ConnectionMaxAggregateInputType = {
  id?: true
  requesterId?: true
  requestedId?: true
  status?: true
  requestedAt?: true
  acceptedAt?: true
}

export type ConnectionCountAggregateInputType = {
  id?: true
  requesterId?: true
  requestedId?: true
  status?: true
  requestedAt?: true
  acceptedAt?: true
  _all?: true
}

export type ConnectionAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Connection to aggregate.
   */
  where?: Prisma.ConnectionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Connections to fetch.
   */
  orderBy?: Prisma.ConnectionOrderByWithRelationInput | Prisma.ConnectionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.ConnectionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Connections from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Connections.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Connections
  **/
  _count?: true | ConnectionCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: ConnectionMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: ConnectionMaxAggregateInputType
}

export type GetConnectionAggregateType<T extends ConnectionAggregateArgs> = {
      [P in keyof T & keyof AggregateConnection]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateConnection[P]>
    : Prisma.GetScalarType<T[P], AggregateConnection[P]>
}




export type ConnectionGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ConnectionWhereInput
  orderBy?: Prisma.ConnectionOrderByWithAggregationInput | Prisma.ConnectionOrderByWithAggregationInput[]
  by: Prisma.ConnectionScalarFieldEnum[] | Prisma.ConnectionScalarFieldEnum
  having?: Prisma.ConnectionScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: ConnectionCountAggregateInputType | true
  _min?: ConnectionMinAggregateInputType
  _max?: ConnectionMaxAggregateInputType
}

export type ConnectionGroupByOutputType = {
  id: string
  requesterId: string
  requestedId: string
  status: $Enums.ConnectionStatus
  requestedAt: Date
  acceptedAt: Date | null
  _count: ConnectionCountAggregateOutputType | null
  _min: ConnectionMinAggregateOutputType | null
  _max: ConnectionMaxAggregateOutputType | null
}

type GetConnectionGroupByPayload<T extends ConnectionGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<ConnectionGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof ConnectionGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], ConnectionGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], ConnectionGroupByOutputType[P]>
      }
    >
  >



export type ConnectionWhereInput = {
  AND?: Prisma.ConnectionWhereInput | Prisma.ConnectionWhereInput[]
  OR?: Prisma.ConnectionWhereInput[]
  NOT?: Prisma.ConnectionWhereInput | Prisma.ConnectionWhereInput[]
  id?: Prisma.StringFilter<"Connection"> | string
  requesterId?: Prisma.StringFilter<"Connection"> | string
  requestedId?: Prisma.StringFilter<"Connection"> | string
  status?: Prisma.EnumConnectionStatusFilter<"Connection"> | $Enums.ConnectionStatus
  requestedAt?: Prisma.DateTimeFilter<"Connection"> | Date | string
  acceptedAt?: Prisma.DateTimeNullableFilter<"Connection"> | Date | string | null
  requester?: Prisma.XOR<Prisma.AlumniProfileScalarRelationFilter, Prisma.AlumniProfileWhereInput>
  requested?: Prisma.XOR<Prisma.AlumniProfileScalarRelationFilter, Prisma.AlumniProfileWhereInput>
}

export type ConnectionOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  requesterId?: Prisma.SortOrder
  requestedId?: Prisma.SortOrder
  status?: Prisma.SortOrder
  requestedAt?: Prisma.SortOrder
  acceptedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  requester?: Prisma.AlumniProfileOrderByWithRelationInput
  requested?: Prisma.AlumniProfileOrderByWithRelationInput
  _relevance?: Prisma.ConnectionOrderByRelevanceInput
}

export type ConnectionWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  requesterId_requestedId?: Prisma.ConnectionRequesterIdRequestedIdCompoundUniqueInput
  AND?: Prisma.ConnectionWhereInput | Prisma.ConnectionWhereInput[]
  OR?: Prisma.ConnectionWhereInput[]
  NOT?: Prisma.ConnectionWhereInput | Prisma.ConnectionWhereInput[]
  requesterId?: Prisma.StringFilter<"Connection"> | string
  requestedId?: Prisma.StringFilter<"Connection"> | string
  status?: Prisma.EnumConnectionStatusFilter<"Connection"> | $Enums.ConnectionStatus
  requestedAt?: Prisma.DateTimeFilter<"Connection"> | Date | string
  acceptedAt?: Prisma.DateTimeNullableFilter<"Connection"> | Date | string | null
  requester?: Prisma.XOR<Prisma.AlumniProfileScalarRelationFilter, Prisma.AlumniProfileWhereInput>
  requested?: Prisma.XOR<Prisma.AlumniProfileScalarRelationFilter, Prisma.AlumniProfileWhereInput>
}, "id" | "requesterId_requestedId">

export type ConnectionOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  requesterId?: Prisma.SortOrder
  requestedId?: Prisma.SortOrder
  status?: Prisma.SortOrder
  requestedAt?: Prisma.SortOrder
  acceptedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.ConnectionCountOrderByAggregateInput
  _max?: Prisma.ConnectionMaxOrderByAggregateInput
  _min?: Prisma.ConnectionMinOrderByAggregateInput
}

export type ConnectionScalarWhereWithAggregatesInput = {
  AND?: Prisma.ConnectionScalarWhereWithAggregatesInput | Prisma.ConnectionScalarWhereWithAggregatesInput[]
  OR?: Prisma.ConnectionScalarWhereWithAggregatesInput[]
  NOT?: Prisma.ConnectionScalarWhereWithAggregatesInput | Prisma.ConnectionScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"Connection"> | string
  requesterId?: Prisma.StringWithAggregatesFilter<"Connection"> | string
  requestedId?: Prisma.StringWithAggregatesFilter<"Connection"> | string
  status?: Prisma.EnumConnectionStatusWithAggregatesFilter<"Connection"> | $Enums.ConnectionStatus
  requestedAt?: Prisma.DateTimeWithAggregatesFilter<"Connection"> | Date | string
  acceptedAt?: Prisma.DateTimeNullableWithAggregatesFilter<"Connection"> | Date | string | null
}

export type ConnectionCreateInput = {
  id?: string
  status?: $Enums.ConnectionStatus
  requestedAt?: Date | string
  acceptedAt?: Date | string | null
  requester: Prisma.AlumniProfileCreateNestedOneWithoutConnectionsInput
  requested: Prisma.AlumniProfileCreateNestedOneWithoutConnectionRequestsInput
}

export type ConnectionUncheckedCreateInput = {
  id?: string
  requesterId: string
  requestedId: string
  status?: $Enums.ConnectionStatus
  requestedAt?: Date | string
  acceptedAt?: Date | string | null
}

export type ConnectionUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumConnectionStatusFieldUpdateOperationsInput | $Enums.ConnectionStatus
  requestedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  acceptedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  requester?: Prisma.AlumniProfileUpdateOneRequiredWithoutConnectionsNestedInput
  requested?: Prisma.AlumniProfileUpdateOneRequiredWithoutConnectionRequestsNestedInput
}

export type ConnectionUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  requesterId?: Prisma.StringFieldUpdateOperationsInput | string
  requestedId?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumConnectionStatusFieldUpdateOperationsInput | $Enums.ConnectionStatus
  requestedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  acceptedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

export type ConnectionCreateManyInput = {
  id?: string
  requesterId: string
  requestedId: string
  status?: $Enums.ConnectionStatus
  requestedAt?: Date | string
  acceptedAt?: Date | string | null
}

export type ConnectionUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumConnectionStatusFieldUpdateOperationsInput | $Enums.ConnectionStatus
  requestedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  acceptedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

export type ConnectionUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  requesterId?: Prisma.StringFieldUpdateOperationsInput | string
  requestedId?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumConnectionStatusFieldUpdateOperationsInput | $Enums.ConnectionStatus
  requestedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  acceptedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

export type ConnectionListRelationFilter = {
  every?: Prisma.ConnectionWhereInput
  some?: Prisma.ConnectionWhereInput
  none?: Prisma.ConnectionWhereInput
}

export type ConnectionOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type ConnectionOrderByRelevanceInput = {
  fields: Prisma.ConnectionOrderByRelevanceFieldEnum | Prisma.ConnectionOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type ConnectionRequesterIdRequestedIdCompoundUniqueInput = {
  requesterId: string
  requestedId: string
}

export type ConnectionCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  requesterId?: Prisma.SortOrder
  requestedId?: Prisma.SortOrder
  status?: Prisma.SortOrder
  requestedAt?: Prisma.SortOrder
  acceptedAt?: Prisma.SortOrder
}

export type ConnectionMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  requesterId?: Prisma.SortOrder
  requestedId?: Prisma.SortOrder
  status?: Prisma.SortOrder
  requestedAt?: Prisma.SortOrder
  acceptedAt?: Prisma.SortOrder
}

export type ConnectionMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  requesterId?: Prisma.SortOrder
  requestedId?: Prisma.SortOrder
  status?: Prisma.SortOrder
  requestedAt?: Prisma.SortOrder
  acceptedAt?: Prisma.SortOrder
}

export type ConnectionCreateNestedManyWithoutRequesterInput = {
  create?: Prisma.XOR<Prisma.ConnectionCreateWithoutRequesterInput, Prisma.ConnectionUncheckedCreateWithoutRequesterInput> | Prisma.ConnectionCreateWithoutRequesterInput[] | Prisma.ConnectionUncheckedCreateWithoutRequesterInput[]
  connectOrCreate?: Prisma.ConnectionCreateOrConnectWithoutRequesterInput | Prisma.ConnectionCreateOrConnectWithoutRequesterInput[]
  createMany?: Prisma.ConnectionCreateManyRequesterInputEnvelope
  connect?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
}

export type ConnectionCreateNestedManyWithoutRequestedInput = {
  create?: Prisma.XOR<Prisma.ConnectionCreateWithoutRequestedInput, Prisma.ConnectionUncheckedCreateWithoutRequestedInput> | Prisma.ConnectionCreateWithoutRequestedInput[] | Prisma.ConnectionUncheckedCreateWithoutRequestedInput[]
  connectOrCreate?: Prisma.ConnectionCreateOrConnectWithoutRequestedInput | Prisma.ConnectionCreateOrConnectWithoutRequestedInput[]
  createMany?: Prisma.ConnectionCreateManyRequestedInputEnvelope
  connect?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
}

export type ConnectionUncheckedCreateNestedManyWithoutRequesterInput = {
  create?: Prisma.XOR<Prisma.ConnectionCreateWithoutRequesterInput, Prisma.ConnectionUncheckedCreateWithoutRequesterInput> | Prisma.ConnectionCreateWithoutRequesterInput[] | Prisma.ConnectionUncheckedCreateWithoutRequesterInput[]
  connectOrCreate?: Prisma.ConnectionCreateOrConnectWithoutRequesterInput | Prisma.ConnectionCreateOrConnectWithoutRequesterInput[]
  createMany?: Prisma.ConnectionCreateManyRequesterInputEnvelope
  connect?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
}

export type ConnectionUncheckedCreateNestedManyWithoutRequestedInput = {
  create?: Prisma.XOR<Prisma.ConnectionCreateWithoutRequestedInput, Prisma.ConnectionUncheckedCreateWithoutRequestedInput> | Prisma.ConnectionCreateWithoutRequestedInput[] | Prisma.ConnectionUncheckedCreateWithoutRequestedInput[]
  connectOrCreate?: Prisma.ConnectionCreateOrConnectWithoutRequestedInput | Prisma.ConnectionCreateOrConnectWithoutRequestedInput[]
  createMany?: Prisma.ConnectionCreateManyRequestedInputEnvelope
  connect?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
}

export type ConnectionUpdateManyWithoutRequesterNestedInput = {
  create?: Prisma.XOR<Prisma.ConnectionCreateWithoutRequesterInput, Prisma.ConnectionUncheckedCreateWithoutRequesterInput> | Prisma.ConnectionCreateWithoutRequesterInput[] | Prisma.ConnectionUncheckedCreateWithoutRequesterInput[]
  connectOrCreate?: Prisma.ConnectionCreateOrConnectWithoutRequesterInput | Prisma.ConnectionCreateOrConnectWithoutRequesterInput[]
  upsert?: Prisma.ConnectionUpsertWithWhereUniqueWithoutRequesterInput | Prisma.ConnectionUpsertWithWhereUniqueWithoutRequesterInput[]
  createMany?: Prisma.ConnectionCreateManyRequesterInputEnvelope
  set?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
  disconnect?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
  delete?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
  connect?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
  update?: Prisma.ConnectionUpdateWithWhereUniqueWithoutRequesterInput | Prisma.ConnectionUpdateWithWhereUniqueWithoutRequesterInput[]
  updateMany?: Prisma.ConnectionUpdateManyWithWhereWithoutRequesterInput | Prisma.ConnectionUpdateManyWithWhereWithoutRequesterInput[]
  deleteMany?: Prisma.ConnectionScalarWhereInput | Prisma.ConnectionScalarWhereInput[]
}

export type ConnectionUpdateManyWithoutRequestedNestedInput = {
  create?: Prisma.XOR<Prisma.ConnectionCreateWithoutRequestedInput, Prisma.ConnectionUncheckedCreateWithoutRequestedInput> | Prisma.ConnectionCreateWithoutRequestedInput[] | Prisma.ConnectionUncheckedCreateWithoutRequestedInput[]
  connectOrCreate?: Prisma.ConnectionCreateOrConnectWithoutRequestedInput | Prisma.ConnectionCreateOrConnectWithoutRequestedInput[]
  upsert?: Prisma.ConnectionUpsertWithWhereUniqueWithoutRequestedInput | Prisma.ConnectionUpsertWithWhereUniqueWithoutRequestedInput[]
  createMany?: Prisma.ConnectionCreateManyRequestedInputEnvelope
  set?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
  disconnect?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
  delete?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
  connect?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
  update?: Prisma.ConnectionUpdateWithWhereUniqueWithoutRequestedInput | Prisma.ConnectionUpdateWithWhereUniqueWithoutRequestedInput[]
  updateMany?: Prisma.ConnectionUpdateManyWithWhereWithoutRequestedInput | Prisma.ConnectionUpdateManyWithWhereWithoutRequestedInput[]
  deleteMany?: Prisma.ConnectionScalarWhereInput | Prisma.ConnectionScalarWhereInput[]
}

export type ConnectionUncheckedUpdateManyWithoutRequesterNestedInput = {
  create?: Prisma.XOR<Prisma.ConnectionCreateWithoutRequesterInput, Prisma.ConnectionUncheckedCreateWithoutRequesterInput> | Prisma.ConnectionCreateWithoutRequesterInput[] | Prisma.ConnectionUncheckedCreateWithoutRequesterInput[]
  connectOrCreate?: Prisma.ConnectionCreateOrConnectWithoutRequesterInput | Prisma.ConnectionCreateOrConnectWithoutRequesterInput[]
  upsert?: Prisma.ConnectionUpsertWithWhereUniqueWithoutRequesterInput | Prisma.ConnectionUpsertWithWhereUniqueWithoutRequesterInput[]
  createMany?: Prisma.ConnectionCreateManyRequesterInputEnvelope
  set?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
  disconnect?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
  delete?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
  connect?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
  update?: Prisma.ConnectionUpdateWithWhereUniqueWithoutRequesterInput | Prisma.ConnectionUpdateWithWhereUniqueWithoutRequesterInput[]
  updateMany?: Prisma.ConnectionUpdateManyWithWhereWithoutRequesterInput | Prisma.ConnectionUpdateManyWithWhereWithoutRequesterInput[]
  deleteMany?: Prisma.ConnectionScalarWhereInput | Prisma.ConnectionScalarWhereInput[]
}

export type ConnectionUncheckedUpdateManyWithoutRequestedNestedInput = {
  create?: Prisma.XOR<Prisma.ConnectionCreateWithoutRequestedInput, Prisma.ConnectionUncheckedCreateWithoutRequestedInput> | Prisma.ConnectionCreateWithoutRequestedInput[] | Prisma.ConnectionUncheckedCreateWithoutRequestedInput[]
  connectOrCreate?: Prisma.ConnectionCreateOrConnectWithoutRequestedInput | Prisma.ConnectionCreateOrConnectWithoutRequestedInput[]
  upsert?: Prisma.ConnectionUpsertWithWhereUniqueWithoutRequestedInput | Prisma.ConnectionUpsertWithWhereUniqueWithoutRequestedInput[]
  createMany?: Prisma.ConnectionCreateManyRequestedInputEnvelope
  set?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
  disconnect?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
  delete?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
  connect?: Prisma.ConnectionWhereUniqueInput | Prisma.ConnectionWhereUniqueInput[]
  update?: Prisma.ConnectionUpdateWithWhereUniqueWithoutRequestedInput | Prisma.ConnectionUpdateWithWhereUniqueWithoutRequestedInput[]
  updateMany?: Prisma.ConnectionUpdateManyWithWhereWithoutRequestedInput | Prisma.ConnectionUpdateManyWithWhereWithoutRequestedInput[]
  deleteMany?: Prisma.ConnectionScalarWhereInput | Prisma.ConnectionScalarWhereInput[]
}

export type EnumConnectionStatusFieldUpdateOperationsInput = {
  set?: $Enums.ConnectionStatus
}

export type ConnectionCreateWithoutRequesterInput = {
  id?: string
  status?: $Enums.ConnectionStatus
  requestedAt?: Date | string
  acceptedAt?: Date | string | null
  requested: Prisma.AlumniProfileCreateNestedOneWithoutConnectionRequestsInput
}

export type ConnectionUncheckedCreateWithoutRequesterInput = {
  id?: string
  requestedId: string
  status?: $Enums.ConnectionStatus
  requestedAt?: Date | string
  acceptedAt?: Date | string | null
}

export type ConnectionCreateOrConnectWithoutRequesterInput = {
  where: Prisma.ConnectionWhereUniqueInput
  create: Prisma.XOR<Prisma.ConnectionCreateWithoutRequesterInput, Prisma.ConnectionUncheckedCreateWithoutRequesterInput>
}

export type ConnectionCreateManyRequesterInputEnvelope = {
  data: Prisma.ConnectionCreateManyRequesterInput | Prisma.ConnectionCreateManyRequesterInput[]
  skipDuplicates?: boolean
}

export type ConnectionCreateWithoutRequestedInput = {
  id?: string
  status?: $Enums.ConnectionStatus
  requestedAt?: Date | string
  acceptedAt?: Date | string | null
  requester: Prisma.AlumniProfileCreateNestedOneWithoutConnectionsInput
}

export type ConnectionUncheckedCreateWithoutRequestedInput = {
  id?: string
  requesterId: string
  status?: $Enums.ConnectionStatus
  requestedAt?: Date | string
  acceptedAt?: Date | string | null
}

export type ConnectionCreateOrConnectWithoutRequestedInput = {
  where: Prisma.ConnectionWhereUniqueInput
  create: Prisma.XOR<Prisma.ConnectionCreateWithoutRequestedInput, Prisma.ConnectionUncheckedCreateWithoutRequestedInput>
}

export type ConnectionCreateManyRequestedInputEnvelope = {
  data: Prisma.ConnectionCreateManyRequestedInput | Prisma.ConnectionCreateManyRequestedInput[]
  skipDuplicates?: boolean
}

export type ConnectionUpsertWithWhereUniqueWithoutRequesterInput = {
  where: Prisma.ConnectionWhereUniqueInput
  update: Prisma.XOR<Prisma.ConnectionUpdateWithoutRequesterInput, Prisma.ConnectionUncheckedUpdateWithoutRequesterInput>
  create: Prisma.XOR<Prisma.ConnectionCreateWithoutRequesterInput, Prisma.ConnectionUncheckedCreateWithoutRequesterInput>
}

export type ConnectionUpdateWithWhereUniqueWithoutRequesterInput = {
  where: Prisma.ConnectionWhereUniqueInput
  data: Prisma.XOR<Prisma.ConnectionUpdateWithoutRequesterInput, Prisma.ConnectionUncheckedUpdateWithoutRequesterInput>
}

export type ConnectionUpdateManyWithWhereWithoutRequesterInput = {
  where: Prisma.ConnectionScalarWhereInput
  data: Prisma.XOR<Prisma.ConnectionUpdateManyMutationInput, Prisma.ConnectionUncheckedUpdateManyWithoutRequesterInput>
}

export type ConnectionScalarWhereInput = {
  AND?: Prisma.ConnectionScalarWhereInput | Prisma.ConnectionScalarWhereInput[]
  OR?: Prisma.ConnectionScalarWhereInput[]
  NOT?: Prisma.ConnectionScalarWhereInput | Prisma.ConnectionScalarWhereInput[]
  id?: Prisma.StringFilter<"Connection"> | string
  requesterId?: Prisma.StringFilter<"Connection"> | string
  requestedId?: Prisma.StringFilter<"Connection"> | string
  status?: Prisma.EnumConnectionStatusFilter<"Connection"> | $Enums.ConnectionStatus
  requestedAt?: Prisma.DateTimeFilter<"Connection"> | Date | string
  acceptedAt?: Prisma.DateTimeNullableFilter<"Connection"> | Date | string | null
}

export type ConnectionUpsertWithWhereUniqueWithoutRequestedInput = {
  where: Prisma.ConnectionWhereUniqueInput
  update: Prisma.XOR<Prisma.ConnectionUpdateWithoutRequestedInput, Prisma.ConnectionUncheckedUpdateWithoutRequestedInput>
  create: Prisma.XOR<Prisma.ConnectionCreateWithoutRequestedInput, Prisma.ConnectionUncheckedCreateWithoutRequestedInput>
}

export type ConnectionUpdateWithWhereUniqueWithoutRequestedInput = {
  where: Prisma.ConnectionWhereUniqueInput
  data: Prisma.XOR<Prisma.ConnectionUpdateWithoutRequestedInput, Prisma.ConnectionUncheckedUpdateWithoutRequestedInput>
}

export type ConnectionUpdateManyWithWhereWithoutRequestedInput = {
  where: Prisma.ConnectionScalarWhereInput
  data: Prisma.XOR<Prisma.ConnectionUpdateManyMutationInput, Prisma.ConnectionUncheckedUpdateManyWithoutRequestedInput>
}

export type ConnectionCreateManyRequesterInput = {
  id?: string
  requestedId: string
  status?: $Enums.ConnectionStatus
  requestedAt?: Date | string
  acceptedAt?: Date | string | null
}

export type ConnectionCreateManyRequestedInput = {
  id?: string
  requesterId: string
  status?: $Enums.ConnectionStatus
  requestedAt?: Date | string
  acceptedAt?: Date | string | null
}

export type ConnectionUpdateWithoutRequesterInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumConnectionStatusFieldUpdateOperationsInput | $Enums.ConnectionStatus
  requestedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  acceptedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  requested?: Prisma.AlumniProfileUpdateOneRequiredWithoutConnectionRequestsNestedInput
}

export type ConnectionUncheckedUpdateWithoutRequesterInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  requestedId?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumConnectionStatusFieldUpdateOperationsInput | $Enums.ConnectionStatus
  requestedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  acceptedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

export type ConnectionUncheckedUpdateManyWithoutRequesterInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  requestedId?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumConnectionStatusFieldUpdateOperationsInput | $Enums.ConnectionStatus
  requestedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  acceptedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

export type ConnectionUpdateWithoutRequestedInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumConnectionStatusFieldUpdateOperationsInput | $Enums.ConnectionStatus
  requestedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  acceptedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  requester?: Prisma.AlumniProfileUpdateOneRequiredWithoutConnectionsNestedInput
}

export type ConnectionUncheckedUpdateWithoutRequestedInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  requesterId?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumConnectionStatusFieldUpdateOperationsInput | $Enums.ConnectionStatus
  requestedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  acceptedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

export type ConnectionUncheckedUpdateManyWithoutRequestedInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  requesterId?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumConnectionStatusFieldUpdateOperationsInput | $Enums.ConnectionStatus
  requestedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  acceptedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}



export type ConnectionSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  requesterId?: boolean
  requestedId?: boolean
  status?: boolean
  requestedAt?: boolean
  acceptedAt?: boolean
  requester?: boolean | Prisma.AlumniProfileDefaultArgs<ExtArgs>
  requested?: boolean | Prisma.AlumniProfileDefaultArgs<ExtArgs>
}, ExtArgs["result"]["connection"]>



export type ConnectionSelectScalar = {
  id?: boolean
  requesterId?: boolean
  requestedId?: boolean
  status?: boolean
  requestedAt?: boolean
  acceptedAt?: boolean
}

export type ConnectionOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "requesterId" | "requestedId" | "status" | "requestedAt" | "acceptedAt", ExtArgs["result"]["connection"]>
export type ConnectionInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  requester?: boolean | Prisma.AlumniProfileDefaultArgs<ExtArgs>
  requested?: boolean | Prisma.AlumniProfileDefaultArgs<ExtArgs>
}

export type $ConnectionPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Connection"
  objects: {
    requester: Prisma.$AlumniProfilePayload<ExtArgs>
    requested: Prisma.$AlumniProfilePayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    requesterId: string
    requestedId: string
    status: $Enums.ConnectionStatus
    requestedAt: Date
    acceptedAt: Date | null
  }, ExtArgs["result"]["connection"]>
  composites: {}
}

export type ConnectionGetPayload<S extends boolean | null | undefined | ConnectionDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$ConnectionPayload, S>

export type ConnectionCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<ConnectionFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: ConnectionCountAggregateInputType | true
  }

export interface ConnectionDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Connection'], meta: { name: 'Connection' } }
  /**
   * Find zero or one Connection that matches the filter.
   * @param {ConnectionFindUniqueArgs} args - Arguments to find a Connection
   * @example
   * // Get one Connection
   * const connection = await prisma.connection.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends ConnectionFindUniqueArgs>(args: Prisma.SelectSubset<T, ConnectionFindUniqueArgs<ExtArgs>>): Prisma.Prisma__ConnectionClient<runtime.Types.Result.GetResult<Prisma.$ConnectionPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Connection that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {ConnectionFindUniqueOrThrowArgs} args - Arguments to find a Connection
   * @example
   * // Get one Connection
   * const connection = await prisma.connection.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends ConnectionFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, ConnectionFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__ConnectionClient<runtime.Types.Result.GetResult<Prisma.$ConnectionPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Connection that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ConnectionFindFirstArgs} args - Arguments to find a Connection
   * @example
   * // Get one Connection
   * const connection = await prisma.connection.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends ConnectionFindFirstArgs>(args?: Prisma.SelectSubset<T, ConnectionFindFirstArgs<ExtArgs>>): Prisma.Prisma__ConnectionClient<runtime.Types.Result.GetResult<Prisma.$ConnectionPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Connection that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ConnectionFindFirstOrThrowArgs} args - Arguments to find a Connection
   * @example
   * // Get one Connection
   * const connection = await prisma.connection.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends ConnectionFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, ConnectionFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__ConnectionClient<runtime.Types.Result.GetResult<Prisma.$ConnectionPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Connections that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ConnectionFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Connections
   * const connections = await prisma.connection.findMany()
   * 
   * // Get first 10 Connections
   * const connections = await prisma.connection.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const connectionWithIdOnly = await prisma.connection.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends ConnectionFindManyArgs>(args?: Prisma.SelectSubset<T, ConnectionFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ConnectionPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Connection.
   * @param {ConnectionCreateArgs} args - Arguments to create a Connection.
   * @example
   * // Create one Connection
   * const Connection = await prisma.connection.create({
   *   data: {
   *     // ... data to create a Connection
   *   }
   * })
   * 
   */
  create<T extends ConnectionCreateArgs>(args: Prisma.SelectSubset<T, ConnectionCreateArgs<ExtArgs>>): Prisma.Prisma__ConnectionClient<runtime.Types.Result.GetResult<Prisma.$ConnectionPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Connections.
   * @param {ConnectionCreateManyArgs} args - Arguments to create many Connections.
   * @example
   * // Create many Connections
   * const connection = await prisma.connection.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends ConnectionCreateManyArgs>(args?: Prisma.SelectSubset<T, ConnectionCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Connection.
   * @param {ConnectionDeleteArgs} args - Arguments to delete one Connection.
   * @example
   * // Delete one Connection
   * const Connection = await prisma.connection.delete({
   *   where: {
   *     // ... filter to delete one Connection
   *   }
   * })
   * 
   */
  delete<T extends ConnectionDeleteArgs>(args: Prisma.SelectSubset<T, ConnectionDeleteArgs<ExtArgs>>): Prisma.Prisma__ConnectionClient<runtime.Types.Result.GetResult<Prisma.$ConnectionPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Connection.
   * @param {ConnectionUpdateArgs} args - Arguments to update one Connection.
   * @example
   * // Update one Connection
   * const connection = await prisma.connection.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends ConnectionUpdateArgs>(args: Prisma.SelectSubset<T, ConnectionUpdateArgs<ExtArgs>>): Prisma.Prisma__ConnectionClient<runtime.Types.Result.GetResult<Prisma.$ConnectionPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Connections.
   * @param {ConnectionDeleteManyArgs} args - Arguments to filter Connections to delete.
   * @example
   * // Delete a few Connections
   * const { count } = await prisma.connection.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends ConnectionDeleteManyArgs>(args?: Prisma.SelectSubset<T, ConnectionDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Connections.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ConnectionUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Connections
   * const connection = await prisma.connection.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends ConnectionUpdateManyArgs>(args: Prisma.SelectSubset<T, ConnectionUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Connection.
   * @param {ConnectionUpsertArgs} args - Arguments to update or create a Connection.
   * @example
   * // Update or create a Connection
   * const connection = await prisma.connection.upsert({
   *   create: {
   *     // ... data to create a Connection
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Connection we want to update
   *   }
   * })
   */
  upsert<T extends ConnectionUpsertArgs>(args: Prisma.SelectSubset<T, ConnectionUpsertArgs<ExtArgs>>): Prisma.Prisma__ConnectionClient<runtime.Types.Result.GetResult<Prisma.$ConnectionPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Connections.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ConnectionCountArgs} args - Arguments to filter Connections to count.
   * @example
   * // Count the number of Connections
   * const count = await prisma.connection.count({
   *   where: {
   *     // ... the filter for the Connections we want to count
   *   }
   * })
  **/
  count<T extends ConnectionCountArgs>(
    args?: Prisma.Subset<T, ConnectionCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], ConnectionCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Connection.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ConnectionAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends ConnectionAggregateArgs>(args: Prisma.Subset<T, ConnectionAggregateArgs>): Prisma.PrismaPromise<GetConnectionAggregateType<T>>

  /**
   * Group by Connection.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ConnectionGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends ConnectionGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: ConnectionGroupByArgs['orderBy'] }
      : { orderBy?: ConnectionGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, ConnectionGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetConnectionGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Connection model
 */
readonly fields: ConnectionFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Connection.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__ConnectionClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  requester<T extends Prisma.AlumniProfileDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.AlumniProfileDefaultArgs<ExtArgs>>): Prisma.Prisma__AlumniProfileClient<runtime.Types.Result.GetResult<Prisma.$AlumniProfilePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  requested<T extends Prisma.AlumniProfileDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.AlumniProfileDefaultArgs<ExtArgs>>): Prisma.Prisma__AlumniProfileClient<runtime.Types.Result.GetResult<Prisma.$AlumniProfilePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Connection model
 */
export interface ConnectionFieldRefs {
  readonly id: Prisma.FieldRef<"Connection", 'String'>
  readonly requesterId: Prisma.FieldRef<"Connection", 'String'>
  readonly requestedId: Prisma.FieldRef<"Connection", 'String'>
  readonly status: Prisma.FieldRef<"Connection", 'ConnectionStatus'>
  readonly requestedAt: Prisma.FieldRef<"Connection", 'DateTime'>
  readonly acceptedAt: Prisma.FieldRef<"Connection", 'DateTime'>
}
    

// Custom InputTypes
/**
 * Connection findUnique
 */
export type ConnectionFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Connection
   */
  select?: Prisma.ConnectionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Connection
   */
  omit?: Prisma.ConnectionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ConnectionInclude<ExtArgs> | null
  /**
   * Filter, which Connection to fetch.
   */
  where: Prisma.ConnectionWhereUniqueInput
}

/**
 * Connection findUniqueOrThrow
 */
export type ConnectionFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Connection
   */
  select?: Prisma.ConnectionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Connection
   */
  omit?: Prisma.ConnectionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ConnectionInclude<ExtArgs> | null
  /**
   * Filter, which Connection to fetch.
   */
  where: Prisma.ConnectionWhereUniqueInput
}

/**
 * Connection findFirst
 */
export type ConnectionFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Connection
   */
  select?: Prisma.ConnectionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Connection
   */
  omit?: Prisma.ConnectionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ConnectionInclude<ExtArgs> | null
  /**
   * Filter, which Connection to fetch.
   */
  where?: Prisma.ConnectionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Connections to fetch.
   */
  orderBy?: Prisma.ConnectionOrderByWithRelationInput | Prisma.ConnectionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Connections.
   */
  cursor?: Prisma.ConnectionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Connections from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Connections.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Connections.
   */
  distinct?: Prisma.ConnectionScalarFieldEnum | Prisma.ConnectionScalarFieldEnum[]
}

/**
 * Connection findFirstOrThrow
 */
export type ConnectionFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Connection
   */
  select?: Prisma.ConnectionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Connection
   */
  omit?: Prisma.ConnectionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ConnectionInclude<ExtArgs> | null
  /**
   * Filter, which Connection to fetch.
   */
  where?: Prisma.ConnectionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Connections to fetch.
   */
  orderBy?: Prisma.ConnectionOrderByWithRelationInput | Prisma.ConnectionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Connections.
   */
  cursor?: Prisma.ConnectionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Connections from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Connections.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Connections.
   */
  distinct?: Prisma.ConnectionScalarFieldEnum | Prisma.ConnectionScalarFieldEnum[]
}

/**
 * Connection findMany
 */
export type ConnectionFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Connection
   */
  select?: Prisma.ConnectionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Connection
   */
  omit?: Prisma.ConnectionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ConnectionInclude<ExtArgs> | null
  /**
   * Filter, which Connections to fetch.
   */
  where?: Prisma.ConnectionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Connections to fetch.
   */
  orderBy?: Prisma.ConnectionOrderByWithRelationInput | Prisma.ConnectionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Connections.
   */
  cursor?: Prisma.ConnectionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Connections from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Connections.
   */
  skip?: number
  distinct?: Prisma.ConnectionScalarFieldEnum | Prisma.ConnectionScalarFieldEnum[]
}

/**
 * Connection create
 */
export type ConnectionCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Connection
   */
  select?: Prisma.ConnectionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Connection
   */
  omit?: Prisma.ConnectionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ConnectionInclude<ExtArgs> | null
  /**
   * The data needed to create a Connection.
   */
  data: Prisma.XOR<Prisma.ConnectionCreateInput, Prisma.ConnectionUncheckedCreateInput>
}

/**
 * Connection createMany
 */
export type ConnectionCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Connections.
   */
  data: Prisma.ConnectionCreateManyInput | Prisma.ConnectionCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Connection update
 */
export type ConnectionUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Connection
   */
  select?: Prisma.ConnectionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Connection
   */
  omit?: Prisma.ConnectionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ConnectionInclude<ExtArgs> | null
  /**
   * The data needed to update a Connection.
   */
  data: Prisma.XOR<Prisma.ConnectionUpdateInput, Prisma.ConnectionUncheckedUpdateInput>
  /**
   * Choose, which Connection to update.
   */
  where: Prisma.ConnectionWhereUniqueInput
}

/**
 * Connection updateMany
 */
export type ConnectionUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Connections.
   */
  data: Prisma.XOR<Prisma.ConnectionUpdateManyMutationInput, Prisma.ConnectionUncheckedUpdateManyInput>
  /**
   * Filter which Connections to update
   */
  where?: Prisma.ConnectionWhereInput
  /**
   * Limit how many Connections to update.
   */
  limit?: number
}

/**
 * Connection upsert
 */
export type ConnectionUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Connection
   */
  select?: Prisma.ConnectionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Connection
   */
  omit?: Prisma.ConnectionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ConnectionInclude<ExtArgs> | null
  /**
   * The filter to search for the Connection to update in case it exists.
   */
  where: Prisma.ConnectionWhereUniqueInput
  /**
   * In case the Connection found by the `where` argument doesn't exist, create a new Connection with this data.
   */
  create: Prisma.XOR<Prisma.ConnectionCreateInput, Prisma.ConnectionUncheckedCreateInput>
  /**
   * In case the Connection was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.ConnectionUpdateInput, Prisma.ConnectionUncheckedUpdateInput>
}

/**
 * Connection delete
 */
export type ConnectionDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Connection
   */
  select?: Prisma.ConnectionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Connection
   */
  omit?: Prisma.ConnectionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ConnectionInclude<ExtArgs> | null
  /**
   * Filter which Connection to delete.
   */
  where: Prisma.ConnectionWhereUniqueInput
}

/**
 * Connection deleteMany
 */
export type ConnectionDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Connections to delete
   */
  where?: Prisma.ConnectionWhereInput
  /**
   * Limit how many Connections to delete.
   */
  limit?: number
}

/**
 * Connection without action
 */
export type ConnectionDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Connection
   */
  select?: Prisma.ConnectionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Connection
   */
  omit?: Prisma.ConnectionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ConnectionInclude<ExtArgs> | null
}
