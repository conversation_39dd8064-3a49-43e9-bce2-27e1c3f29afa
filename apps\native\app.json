{"expo": {"name": "my-better-t-app", "slug": "my-better-t-app", "version": "1.0.0", "scheme": "my-better-t-app", "web": {"bundler": "metro", "output": "static", "favicon": "./assets/favicon.png"}, "plugins": ["expo-router", "expo-secure-store", "expo-web-browser"], "experiments": {"typedRoutes": true, "tsconfigPaths": true}, "newArchEnabled": true, "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.amanvarshney01.mybettertapp"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.amanvarshney01.mybettertapp", "edgeToEdgeEnabled": true}}}