
/* !!! This is code generated by <PERSON><PERSON><PERSON>. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
* This file exports all enum related types from the schema.
*
* 🟢 You can import this file directly.
*/
export const ProfileVisibility = {
  PUBLIC: 'PUBLIC',
  ALUMNI_ONLY: 'ALUMNI_ONLY',
  PRIVATE: 'PRIVATE'
} as const

export type ProfileVisibility = (typeof ProfileVisibility)[keyof typeof ProfileVisibility]


export const ConnectionStatus = {
  PENDING: 'PENDING',
  ACCEPTED: 'ACCEPTED',
  DECLINED: 'DECLINED',
  BLOCKED: 'BLOCKED'
} as const

export type ConnectionStatus = (typeof ConnectionStatus)[keyof typeof ConnectionStatus]


export const PostType = {
  GENERAL: 'GENERAL',
  SUCCESS_STORY: 'SUCCESS_STORY',
  JOB_OPPORTUNITY: 'JOB_OPPORTUNITY',
  MENTORSHIP: 'MENTORSHIP',
  ANNOUNCEMENT: 'ANNOUNCEMENT'
} as const

export type PostType = (typeof PostType)[keyof typeof PostType]


export const EventType = {
  WORKSHOP: 'WORKSHOP',
  NETWORKING: 'NETWORKING',
  CONFERENCE: 'CONFERENCE',
  WEBINAR: 'WEBINAR',
  SOCIAL: 'SOCIAL',
  FUNDRAISING: 'FUNDRAISING',
  MENTORSHIP: 'MENTORSHIP'
} as const

export type EventType = (typeof EventType)[keyof typeof EventType]


export const RegistrationStatus = {
  REGISTERED: 'REGISTERED',
  ATTENDED: 'ATTENDED',
  NO_SHOW: 'NO_SHOW',
  CANCELLED: 'CANCELLED'
} as const

export type RegistrationStatus = (typeof RegistrationStatus)[keyof typeof RegistrationStatus]


export const DonationType = {
  ONE_TIME: 'ONE_TIME',
  MONTHLY: 'MONTHLY',
  QUARTERLY: 'QUARTERLY',
  ANNUAL: 'ANNUAL'
} as const

export type DonationType = (typeof DonationType)[keyof typeof DonationType]


export const PaymentStatus = {
  PENDING: 'PENDING',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED',
  REFUNDED: 'REFUNDED',
  CANCELLED: 'CANCELLED'
} as const

export type PaymentStatus = (typeof PaymentStatus)[keyof typeof PaymentStatus]


export const NewsCategory = {
  GENERAL: 'GENERAL',
  EVENTS: 'EVENTS',
  SUCCESS_STORIES: 'SUCCESS_STORIES',
  OPPORTUNITIES: 'OPPORTUNITIES',
  ANNOUNCEMENTS: 'ANNOUNCEMENTS'
} as const

export type NewsCategory = (typeof NewsCategory)[keyof typeof NewsCategory]


export const NotificationType = {
  CONNECTION_REQUEST: 'CONNECTION_REQUEST',
  CONNECTION_ACCEPTED: 'CONNECTION_ACCEPTED',
  EVENT_REMINDER: 'EVENT_REMINDER',
  EVENT_REGISTRATION: 'EVENT_REGISTRATION',
  NEW_MESSAGE: 'NEW_MESSAGE',
  POST_LIKE: 'POST_LIKE',
  POST_COMMENT: 'POST_COMMENT',
  DONATION_CONFIRMATION: 'DONATION_CONFIRMATION',
  NEWS_ARTICLE: 'NEWS_ARTICLE',
  SYSTEM_ANNOUNCEMENT: 'SYSTEM_ANNOUNCEMENT'
} as const

export type NotificationType = (typeof NotificationType)[keyof typeof NotificationType]
