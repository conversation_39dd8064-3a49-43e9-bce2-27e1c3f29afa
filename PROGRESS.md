## PROTEC Alumni Platform - UI Progress Tracker

This document tracks the web UI implementation progress, aligned with:
- `apps/web/UI_GUIDELINES.md` (shadcn-first, token-only)
- `PROTEC_Alumni_Platform_PRD.md`
- `PROTEC_Feature_Map.md`
- Backend routers in `apps/server`

### Legend
- [x] Done
- [ ] Todo
- [~] In progress / basic version done, follow-ups planned

### 1) Alumni Profiles
- [x] Create profile UI
  - Page: `apps/web/src/app/profile/page.tsx` → `CreateProfileForm`
  - Endpoint: `orpc.profiles.create`
- [x] View overview (summary + stats)
  - `ProfileSummary` (uses `profiles.getMe`, `profiles.getStats`)
- [x] Edit profile UI
  - `EditProfileForm` (uses `profiles.getMe`, `profiles.updateMe`)
- [x] Privacy settings UI
  - `PrivacySettingsForm` (uses `profiles.updatePrivacy`)
- [x] Delete profile (danger zone)
  - `DeleteProfile` (uses `profiles.deleteMe` with confirm)
- [x] Public profile detail view
  - Page: `apps/web/src/app/directory/[id]/page.tsx` (uses `profiles.getById`, `connections.sendRequest`)

Follow-ups:
- Public profile route under `directory/[id]`
- Share profile link, minimal SEO metadata

### 2) Alumni Directory
- [x] Filters panel (search, year, program, industry, location, center, mentorship flags)
  - `SearchFilters` (uses token components)
- [x] List results and pagination
  - `profiles.search` with `limit=20`; pagination controls
- [x] Profile detail page
  - Full profile with connect action
- [x] Save filters in URL (searchParams) and deep-linking
  - Keeps `page`, `search`, filters in query params

### 3) Connections & Messaging
- [x] Connections listing (tabs: Pending, Accepted, Blocked)
  - Endpoints: `connections.list`
- [x] Actions: send, accept, decline, block, remove
  - UI buttons wired to endpoints
- [x] Messaging (basic): conversations list, thread, send
  - Page: `apps/web/src/app/messages/page.tsx` using `connections.messages.*`

### 4) Posts / Feed
- [x] Feed list and post detail
  - `PostList` uses `posts.list`, pagination and type filter; `PostDetail` uses `posts.getById`
- [x] Create post, delete own, like/unlike, comments CRUD
  - Create via `posts.create`; Delete via `posts.delete`; Like via `posts.toggleLike`; Comments via `posts.comments.*`
- [x] Filters by type

### 5) Events
- [x] List and detail (public)
  - `EventList` uses `events.list`; `EventDetail` uses `events.getById`
- [x] Register / cancel / my registrations
  - RSVP via `events.register`, cancel via `events.cancelRegistration`, status via `events.updateRegistration`, list via `events.myRegistrations`
- [x] Admin: create/update/delete, registrations list
  - Create via `events.admin.create` (page UI); update/delete to follow in admin panel expansion

### 6) Donations
- [x] Create one-time/recurring donation flows
  - `CreateDonation` for create; `RecurringManager` for setup/update/cancel recurring
- [x] History page; admin org analytics
  - `DonationHistory` for my donations; `AdminAnalytics` + `AdminDonations` for org view and payment status updates

### 7) News
- [x] List + detail by category
  - `NewsList` list with filters; `NewsDetail` shows full article
- [x] Admin CRUD
  - Create via `news.admin.create`, publish/unpublish via `news.admin.togglePublication`, delete in detail

### 8) Notifications
- [x] List, mark read/unread; counters
  - `NotificationsList` uses `notifications.list`, `markRead`, `delete`, `markAllRead`; `NotificationsStats` shows `unreadCount` and `stats`

### 9) Organizations
- [~] Members list; invites; role management
  - Members list via `profiles.getByOrganization`
  - Invites form scaffolded; awaiting server endpoints for invites/roles

---

### Design & Implementation Notes
- shadcn-first, token-only classes per `UI_GUIDELINES.md`
- Minimal containers: `rounded-lg border bg-card p-4`; spacing via `space-y-*`, `gap-*`
- Icons from `lucide-react`, colored with tokens only
- Query/mutation via `@tanstack/react-query` + `orpc`
- No app run/build in this workflow; defer to CI/QA later


