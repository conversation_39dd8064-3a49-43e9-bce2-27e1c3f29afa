
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `Notification` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model Notification
 * 
 */
export type NotificationModel = runtime.Types.Result.DefaultSelection<Prisma.$NotificationPayload>

export type AggregateNotification = {
  _count: NotificationCountAggregateOutputType | null
  _min: NotificationMinAggregateOutputType | null
  _max: NotificationMaxAggregateOutputType | null
}

export type NotificationMinAggregateOutputType = {
  id: string | null
  recipientId: string | null
  type: $Enums.NotificationType | null
  title: string | null
  message: string | null
  isRead: boolean | null
  actionUrl: string | null
  createdAt: Date | null
  organizationId: string | null
}

export type NotificationMaxAggregateOutputType = {
  id: string | null
  recipientId: string | null
  type: $Enums.NotificationType | null
  title: string | null
  message: string | null
  isRead: boolean | null
  actionUrl: string | null
  createdAt: Date | null
  organizationId: string | null
}

export type NotificationCountAggregateOutputType = {
  id: number
  recipientId: number
  type: number
  title: number
  message: number
  isRead: number
  actionUrl: number
  createdAt: number
  organizationId: number
  _all: number
}


export type NotificationMinAggregateInputType = {
  id?: true
  recipientId?: true
  type?: true
  title?: true
  message?: true
  isRead?: true
  actionUrl?: true
  createdAt?: true
  organizationId?: true
}

export type NotificationMaxAggregateInputType = {
  id?: true
  recipientId?: true
  type?: true
  title?: true
  message?: true
  isRead?: true
  actionUrl?: true
  createdAt?: true
  organizationId?: true
}

export type NotificationCountAggregateInputType = {
  id?: true
  recipientId?: true
  type?: true
  title?: true
  message?: true
  isRead?: true
  actionUrl?: true
  createdAt?: true
  organizationId?: true
  _all?: true
}

export type NotificationAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Notification to aggregate.
   */
  where?: Prisma.NotificationWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Notifications to fetch.
   */
  orderBy?: Prisma.NotificationOrderByWithRelationInput | Prisma.NotificationOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.NotificationWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Notifications from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Notifications.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Notifications
  **/
  _count?: true | NotificationCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: NotificationMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: NotificationMaxAggregateInputType
}

export type GetNotificationAggregateType<T extends NotificationAggregateArgs> = {
      [P in keyof T & keyof AggregateNotification]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateNotification[P]>
    : Prisma.GetScalarType<T[P], AggregateNotification[P]>
}




export type NotificationGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.NotificationWhereInput
  orderBy?: Prisma.NotificationOrderByWithAggregationInput | Prisma.NotificationOrderByWithAggregationInput[]
  by: Prisma.NotificationScalarFieldEnum[] | Prisma.NotificationScalarFieldEnum
  having?: Prisma.NotificationScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: NotificationCountAggregateInputType | true
  _min?: NotificationMinAggregateInputType
  _max?: NotificationMaxAggregateInputType
}

export type NotificationGroupByOutputType = {
  id: string
  recipientId: string
  type: $Enums.NotificationType
  title: string
  message: string
  isRead: boolean
  actionUrl: string | null
  createdAt: Date
  organizationId: string | null
  _count: NotificationCountAggregateOutputType | null
  _min: NotificationMinAggregateOutputType | null
  _max: NotificationMaxAggregateOutputType | null
}

type GetNotificationGroupByPayload<T extends NotificationGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<NotificationGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof NotificationGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], NotificationGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], NotificationGroupByOutputType[P]>
      }
    >
  >



export type NotificationWhereInput = {
  AND?: Prisma.NotificationWhereInput | Prisma.NotificationWhereInput[]
  OR?: Prisma.NotificationWhereInput[]
  NOT?: Prisma.NotificationWhereInput | Prisma.NotificationWhereInput[]
  id?: Prisma.StringFilter<"Notification"> | string
  recipientId?: Prisma.StringFilter<"Notification"> | string
  type?: Prisma.EnumNotificationTypeFilter<"Notification"> | $Enums.NotificationType
  title?: Prisma.StringFilter<"Notification"> | string
  message?: Prisma.StringFilter<"Notification"> | string
  isRead?: Prisma.BoolFilter<"Notification"> | boolean
  actionUrl?: Prisma.StringNullableFilter<"Notification"> | string | null
  createdAt?: Prisma.DateTimeFilter<"Notification"> | Date | string
  organizationId?: Prisma.StringNullableFilter<"Notification"> | string | null
  recipient?: Prisma.XOR<Prisma.AlumniProfileScalarRelationFilter, Prisma.AlumniProfileWhereInput>
  Organization?: Prisma.XOR<Prisma.OrganizationNullableScalarRelationFilter, Prisma.OrganizationWhereInput> | null
}

export type NotificationOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  recipientId?: Prisma.SortOrder
  type?: Prisma.SortOrder
  title?: Prisma.SortOrder
  message?: Prisma.SortOrder
  isRead?: Prisma.SortOrder
  actionUrl?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  organizationId?: Prisma.SortOrderInput | Prisma.SortOrder
  recipient?: Prisma.AlumniProfileOrderByWithRelationInput
  Organization?: Prisma.OrganizationOrderByWithRelationInput
  _relevance?: Prisma.NotificationOrderByRelevanceInput
}

export type NotificationWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  AND?: Prisma.NotificationWhereInput | Prisma.NotificationWhereInput[]
  OR?: Prisma.NotificationWhereInput[]
  NOT?: Prisma.NotificationWhereInput | Prisma.NotificationWhereInput[]
  recipientId?: Prisma.StringFilter<"Notification"> | string
  type?: Prisma.EnumNotificationTypeFilter<"Notification"> | $Enums.NotificationType
  title?: Prisma.StringFilter<"Notification"> | string
  message?: Prisma.StringFilter<"Notification"> | string
  isRead?: Prisma.BoolFilter<"Notification"> | boolean
  actionUrl?: Prisma.StringNullableFilter<"Notification"> | string | null
  createdAt?: Prisma.DateTimeFilter<"Notification"> | Date | string
  organizationId?: Prisma.StringNullableFilter<"Notification"> | string | null
  recipient?: Prisma.XOR<Prisma.AlumniProfileScalarRelationFilter, Prisma.AlumniProfileWhereInput>
  Organization?: Prisma.XOR<Prisma.OrganizationNullableScalarRelationFilter, Prisma.OrganizationWhereInput> | null
}, "id">

export type NotificationOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  recipientId?: Prisma.SortOrder
  type?: Prisma.SortOrder
  title?: Prisma.SortOrder
  message?: Prisma.SortOrder
  isRead?: Prisma.SortOrder
  actionUrl?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  organizationId?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.NotificationCountOrderByAggregateInput
  _max?: Prisma.NotificationMaxOrderByAggregateInput
  _min?: Prisma.NotificationMinOrderByAggregateInput
}

export type NotificationScalarWhereWithAggregatesInput = {
  AND?: Prisma.NotificationScalarWhereWithAggregatesInput | Prisma.NotificationScalarWhereWithAggregatesInput[]
  OR?: Prisma.NotificationScalarWhereWithAggregatesInput[]
  NOT?: Prisma.NotificationScalarWhereWithAggregatesInput | Prisma.NotificationScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"Notification"> | string
  recipientId?: Prisma.StringWithAggregatesFilter<"Notification"> | string
  type?: Prisma.EnumNotificationTypeWithAggregatesFilter<"Notification"> | $Enums.NotificationType
  title?: Prisma.StringWithAggregatesFilter<"Notification"> | string
  message?: Prisma.StringWithAggregatesFilter<"Notification"> | string
  isRead?: Prisma.BoolWithAggregatesFilter<"Notification"> | boolean
  actionUrl?: Prisma.StringNullableWithAggregatesFilter<"Notification"> | string | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"Notification"> | Date | string
  organizationId?: Prisma.StringNullableWithAggregatesFilter<"Notification"> | string | null
}

export type NotificationCreateInput = {
  id?: string
  type: $Enums.NotificationType
  title: string
  message: string
  isRead?: boolean
  actionUrl?: string | null
  createdAt?: Date | string
  recipient: Prisma.AlumniProfileCreateNestedOneWithoutNotificationsInput
  Organization?: Prisma.OrganizationCreateNestedOneWithoutNotificationsInput
}

export type NotificationUncheckedCreateInput = {
  id?: string
  recipientId: string
  type: $Enums.NotificationType
  title: string
  message: string
  isRead?: boolean
  actionUrl?: string | null
  createdAt?: Date | string
  organizationId?: string | null
}

export type NotificationUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  type?: Prisma.EnumNotificationTypeFieldUpdateOperationsInput | $Enums.NotificationType
  title?: Prisma.StringFieldUpdateOperationsInput | string
  message?: Prisma.StringFieldUpdateOperationsInput | string
  isRead?: Prisma.BoolFieldUpdateOperationsInput | boolean
  actionUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  recipient?: Prisma.AlumniProfileUpdateOneRequiredWithoutNotificationsNestedInput
  Organization?: Prisma.OrganizationUpdateOneWithoutNotificationsNestedInput
}

export type NotificationUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  recipientId?: Prisma.StringFieldUpdateOperationsInput | string
  type?: Prisma.EnumNotificationTypeFieldUpdateOperationsInput | $Enums.NotificationType
  title?: Prisma.StringFieldUpdateOperationsInput | string
  message?: Prisma.StringFieldUpdateOperationsInput | string
  isRead?: Prisma.BoolFieldUpdateOperationsInput | boolean
  actionUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  organizationId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type NotificationCreateManyInput = {
  id?: string
  recipientId: string
  type: $Enums.NotificationType
  title: string
  message: string
  isRead?: boolean
  actionUrl?: string | null
  createdAt?: Date | string
  organizationId?: string | null
}

export type NotificationUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  type?: Prisma.EnumNotificationTypeFieldUpdateOperationsInput | $Enums.NotificationType
  title?: Prisma.StringFieldUpdateOperationsInput | string
  message?: Prisma.StringFieldUpdateOperationsInput | string
  isRead?: Prisma.BoolFieldUpdateOperationsInput | boolean
  actionUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type NotificationUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  recipientId?: Prisma.StringFieldUpdateOperationsInput | string
  type?: Prisma.EnumNotificationTypeFieldUpdateOperationsInput | $Enums.NotificationType
  title?: Prisma.StringFieldUpdateOperationsInput | string
  message?: Prisma.StringFieldUpdateOperationsInput | string
  isRead?: Prisma.BoolFieldUpdateOperationsInput | boolean
  actionUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  organizationId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type NotificationListRelationFilter = {
  every?: Prisma.NotificationWhereInput
  some?: Prisma.NotificationWhereInput
  none?: Prisma.NotificationWhereInput
}

export type NotificationOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type NotificationOrderByRelevanceInput = {
  fields: Prisma.NotificationOrderByRelevanceFieldEnum | Prisma.NotificationOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type NotificationCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  recipientId?: Prisma.SortOrder
  type?: Prisma.SortOrder
  title?: Prisma.SortOrder
  message?: Prisma.SortOrder
  isRead?: Prisma.SortOrder
  actionUrl?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  organizationId?: Prisma.SortOrder
}

export type NotificationMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  recipientId?: Prisma.SortOrder
  type?: Prisma.SortOrder
  title?: Prisma.SortOrder
  message?: Prisma.SortOrder
  isRead?: Prisma.SortOrder
  actionUrl?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  organizationId?: Prisma.SortOrder
}

export type NotificationMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  recipientId?: Prisma.SortOrder
  type?: Prisma.SortOrder
  title?: Prisma.SortOrder
  message?: Prisma.SortOrder
  isRead?: Prisma.SortOrder
  actionUrl?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  organizationId?: Prisma.SortOrder
}

export type NotificationCreateNestedManyWithoutOrganizationInput = {
  create?: Prisma.XOR<Prisma.NotificationCreateWithoutOrganizationInput, Prisma.NotificationUncheckedCreateWithoutOrganizationInput> | Prisma.NotificationCreateWithoutOrganizationInput[] | Prisma.NotificationUncheckedCreateWithoutOrganizationInput[]
  connectOrCreate?: Prisma.NotificationCreateOrConnectWithoutOrganizationInput | Prisma.NotificationCreateOrConnectWithoutOrganizationInput[]
  createMany?: Prisma.NotificationCreateManyOrganizationInputEnvelope
  connect?: Prisma.NotificationWhereUniqueInput | Prisma.NotificationWhereUniqueInput[]
}

export type NotificationUncheckedCreateNestedManyWithoutOrganizationInput = {
  create?: Prisma.XOR<Prisma.NotificationCreateWithoutOrganizationInput, Prisma.NotificationUncheckedCreateWithoutOrganizationInput> | Prisma.NotificationCreateWithoutOrganizationInput[] | Prisma.NotificationUncheckedCreateWithoutOrganizationInput[]
  connectOrCreate?: Prisma.NotificationCreateOrConnectWithoutOrganizationInput | Prisma.NotificationCreateOrConnectWithoutOrganizationInput[]
  createMany?: Prisma.NotificationCreateManyOrganizationInputEnvelope
  connect?: Prisma.NotificationWhereUniqueInput | Prisma.NotificationWhereUniqueInput[]
}

export type NotificationUpdateManyWithoutOrganizationNestedInput = {
  create?: Prisma.XOR<Prisma.NotificationCreateWithoutOrganizationInput, Prisma.NotificationUncheckedCreateWithoutOrganizationInput> | Prisma.NotificationCreateWithoutOrganizationInput[] | Prisma.NotificationUncheckedCreateWithoutOrganizationInput[]
  connectOrCreate?: Prisma.NotificationCreateOrConnectWithoutOrganizationInput | Prisma.NotificationCreateOrConnectWithoutOrganizationInput[]
  upsert?: Prisma.NotificationUpsertWithWhereUniqueWithoutOrganizationInput | Prisma.NotificationUpsertWithWhereUniqueWithoutOrganizationInput[]
  createMany?: Prisma.NotificationCreateManyOrganizationInputEnvelope
  set?: Prisma.NotificationWhereUniqueInput | Prisma.NotificationWhereUniqueInput[]
  disconnect?: Prisma.NotificationWhereUniqueInput | Prisma.NotificationWhereUniqueInput[]
  delete?: Prisma.NotificationWhereUniqueInput | Prisma.NotificationWhereUniqueInput[]
  connect?: Prisma.NotificationWhereUniqueInput | Prisma.NotificationWhereUniqueInput[]
  update?: Prisma.NotificationUpdateWithWhereUniqueWithoutOrganizationInput | Prisma.NotificationUpdateWithWhereUniqueWithoutOrganizationInput[]
  updateMany?: Prisma.NotificationUpdateManyWithWhereWithoutOrganizationInput | Prisma.NotificationUpdateManyWithWhereWithoutOrganizationInput[]
  deleteMany?: Prisma.NotificationScalarWhereInput | Prisma.NotificationScalarWhereInput[]
}

export type NotificationUncheckedUpdateManyWithoutOrganizationNestedInput = {
  create?: Prisma.XOR<Prisma.NotificationCreateWithoutOrganizationInput, Prisma.NotificationUncheckedCreateWithoutOrganizationInput> | Prisma.NotificationCreateWithoutOrganizationInput[] | Prisma.NotificationUncheckedCreateWithoutOrganizationInput[]
  connectOrCreate?: Prisma.NotificationCreateOrConnectWithoutOrganizationInput | Prisma.NotificationCreateOrConnectWithoutOrganizationInput[]
  upsert?: Prisma.NotificationUpsertWithWhereUniqueWithoutOrganizationInput | Prisma.NotificationUpsertWithWhereUniqueWithoutOrganizationInput[]
  createMany?: Prisma.NotificationCreateManyOrganizationInputEnvelope
  set?: Prisma.NotificationWhereUniqueInput | Prisma.NotificationWhereUniqueInput[]
  disconnect?: Prisma.NotificationWhereUniqueInput | Prisma.NotificationWhereUniqueInput[]
  delete?: Prisma.NotificationWhereUniqueInput | Prisma.NotificationWhereUniqueInput[]
  connect?: Prisma.NotificationWhereUniqueInput | Prisma.NotificationWhereUniqueInput[]
  update?: Prisma.NotificationUpdateWithWhereUniqueWithoutOrganizationInput | Prisma.NotificationUpdateWithWhereUniqueWithoutOrganizationInput[]
  updateMany?: Prisma.NotificationUpdateManyWithWhereWithoutOrganizationInput | Prisma.NotificationUpdateManyWithWhereWithoutOrganizationInput[]
  deleteMany?: Prisma.NotificationScalarWhereInput | Prisma.NotificationScalarWhereInput[]
}

export type NotificationCreateNestedManyWithoutRecipientInput = {
  create?: Prisma.XOR<Prisma.NotificationCreateWithoutRecipientInput, Prisma.NotificationUncheckedCreateWithoutRecipientInput> | Prisma.NotificationCreateWithoutRecipientInput[] | Prisma.NotificationUncheckedCreateWithoutRecipientInput[]
  connectOrCreate?: Prisma.NotificationCreateOrConnectWithoutRecipientInput | Prisma.NotificationCreateOrConnectWithoutRecipientInput[]
  createMany?: Prisma.NotificationCreateManyRecipientInputEnvelope
  connect?: Prisma.NotificationWhereUniqueInput | Prisma.NotificationWhereUniqueInput[]
}

export type NotificationUncheckedCreateNestedManyWithoutRecipientInput = {
  create?: Prisma.XOR<Prisma.NotificationCreateWithoutRecipientInput, Prisma.NotificationUncheckedCreateWithoutRecipientInput> | Prisma.NotificationCreateWithoutRecipientInput[] | Prisma.NotificationUncheckedCreateWithoutRecipientInput[]
  connectOrCreate?: Prisma.NotificationCreateOrConnectWithoutRecipientInput | Prisma.NotificationCreateOrConnectWithoutRecipientInput[]
  createMany?: Prisma.NotificationCreateManyRecipientInputEnvelope
  connect?: Prisma.NotificationWhereUniqueInput | Prisma.NotificationWhereUniqueInput[]
}

export type NotificationUpdateManyWithoutRecipientNestedInput = {
  create?: Prisma.XOR<Prisma.NotificationCreateWithoutRecipientInput, Prisma.NotificationUncheckedCreateWithoutRecipientInput> | Prisma.NotificationCreateWithoutRecipientInput[] | Prisma.NotificationUncheckedCreateWithoutRecipientInput[]
  connectOrCreate?: Prisma.NotificationCreateOrConnectWithoutRecipientInput | Prisma.NotificationCreateOrConnectWithoutRecipientInput[]
  upsert?: Prisma.NotificationUpsertWithWhereUniqueWithoutRecipientInput | Prisma.NotificationUpsertWithWhereUniqueWithoutRecipientInput[]
  createMany?: Prisma.NotificationCreateManyRecipientInputEnvelope
  set?: Prisma.NotificationWhereUniqueInput | Prisma.NotificationWhereUniqueInput[]
  disconnect?: Prisma.NotificationWhereUniqueInput | Prisma.NotificationWhereUniqueInput[]
  delete?: Prisma.NotificationWhereUniqueInput | Prisma.NotificationWhereUniqueInput[]
  connect?: Prisma.NotificationWhereUniqueInput | Prisma.NotificationWhereUniqueInput[]
  update?: Prisma.NotificationUpdateWithWhereUniqueWithoutRecipientInput | Prisma.NotificationUpdateWithWhereUniqueWithoutRecipientInput[]
  updateMany?: Prisma.NotificationUpdateManyWithWhereWithoutRecipientInput | Prisma.NotificationUpdateManyWithWhereWithoutRecipientInput[]
  deleteMany?: Prisma.NotificationScalarWhereInput | Prisma.NotificationScalarWhereInput[]
}

export type NotificationUncheckedUpdateManyWithoutRecipientNestedInput = {
  create?: Prisma.XOR<Prisma.NotificationCreateWithoutRecipientInput, Prisma.NotificationUncheckedCreateWithoutRecipientInput> | Prisma.NotificationCreateWithoutRecipientInput[] | Prisma.NotificationUncheckedCreateWithoutRecipientInput[]
  connectOrCreate?: Prisma.NotificationCreateOrConnectWithoutRecipientInput | Prisma.NotificationCreateOrConnectWithoutRecipientInput[]
  upsert?: Prisma.NotificationUpsertWithWhereUniqueWithoutRecipientInput | Prisma.NotificationUpsertWithWhereUniqueWithoutRecipientInput[]
  createMany?: Prisma.NotificationCreateManyRecipientInputEnvelope
  set?: Prisma.NotificationWhereUniqueInput | Prisma.NotificationWhereUniqueInput[]
  disconnect?: Prisma.NotificationWhereUniqueInput | Prisma.NotificationWhereUniqueInput[]
  delete?: Prisma.NotificationWhereUniqueInput | Prisma.NotificationWhereUniqueInput[]
  connect?: Prisma.NotificationWhereUniqueInput | Prisma.NotificationWhereUniqueInput[]
  update?: Prisma.NotificationUpdateWithWhereUniqueWithoutRecipientInput | Prisma.NotificationUpdateWithWhereUniqueWithoutRecipientInput[]
  updateMany?: Prisma.NotificationUpdateManyWithWhereWithoutRecipientInput | Prisma.NotificationUpdateManyWithWhereWithoutRecipientInput[]
  deleteMany?: Prisma.NotificationScalarWhereInput | Prisma.NotificationScalarWhereInput[]
}

export type EnumNotificationTypeFieldUpdateOperationsInput = {
  set?: $Enums.NotificationType
}

export type NotificationCreateWithoutOrganizationInput = {
  id?: string
  type: $Enums.NotificationType
  title: string
  message: string
  isRead?: boolean
  actionUrl?: string | null
  createdAt?: Date | string
  recipient: Prisma.AlumniProfileCreateNestedOneWithoutNotificationsInput
}

export type NotificationUncheckedCreateWithoutOrganizationInput = {
  id?: string
  recipientId: string
  type: $Enums.NotificationType
  title: string
  message: string
  isRead?: boolean
  actionUrl?: string | null
  createdAt?: Date | string
}

export type NotificationCreateOrConnectWithoutOrganizationInput = {
  where: Prisma.NotificationWhereUniqueInput
  create: Prisma.XOR<Prisma.NotificationCreateWithoutOrganizationInput, Prisma.NotificationUncheckedCreateWithoutOrganizationInput>
}

export type NotificationCreateManyOrganizationInputEnvelope = {
  data: Prisma.NotificationCreateManyOrganizationInput | Prisma.NotificationCreateManyOrganizationInput[]
  skipDuplicates?: boolean
}

export type NotificationUpsertWithWhereUniqueWithoutOrganizationInput = {
  where: Prisma.NotificationWhereUniqueInput
  update: Prisma.XOR<Prisma.NotificationUpdateWithoutOrganizationInput, Prisma.NotificationUncheckedUpdateWithoutOrganizationInput>
  create: Prisma.XOR<Prisma.NotificationCreateWithoutOrganizationInput, Prisma.NotificationUncheckedCreateWithoutOrganizationInput>
}

export type NotificationUpdateWithWhereUniqueWithoutOrganizationInput = {
  where: Prisma.NotificationWhereUniqueInput
  data: Prisma.XOR<Prisma.NotificationUpdateWithoutOrganizationInput, Prisma.NotificationUncheckedUpdateWithoutOrganizationInput>
}

export type NotificationUpdateManyWithWhereWithoutOrganizationInput = {
  where: Prisma.NotificationScalarWhereInput
  data: Prisma.XOR<Prisma.NotificationUpdateManyMutationInput, Prisma.NotificationUncheckedUpdateManyWithoutOrganizationInput>
}

export type NotificationScalarWhereInput = {
  AND?: Prisma.NotificationScalarWhereInput | Prisma.NotificationScalarWhereInput[]
  OR?: Prisma.NotificationScalarWhereInput[]
  NOT?: Prisma.NotificationScalarWhereInput | Prisma.NotificationScalarWhereInput[]
  id?: Prisma.StringFilter<"Notification"> | string
  recipientId?: Prisma.StringFilter<"Notification"> | string
  type?: Prisma.EnumNotificationTypeFilter<"Notification"> | $Enums.NotificationType
  title?: Prisma.StringFilter<"Notification"> | string
  message?: Prisma.StringFilter<"Notification"> | string
  isRead?: Prisma.BoolFilter<"Notification"> | boolean
  actionUrl?: Prisma.StringNullableFilter<"Notification"> | string | null
  createdAt?: Prisma.DateTimeFilter<"Notification"> | Date | string
  organizationId?: Prisma.StringNullableFilter<"Notification"> | string | null
}

export type NotificationCreateWithoutRecipientInput = {
  id?: string
  type: $Enums.NotificationType
  title: string
  message: string
  isRead?: boolean
  actionUrl?: string | null
  createdAt?: Date | string
  Organization?: Prisma.OrganizationCreateNestedOneWithoutNotificationsInput
}

export type NotificationUncheckedCreateWithoutRecipientInput = {
  id?: string
  type: $Enums.NotificationType
  title: string
  message: string
  isRead?: boolean
  actionUrl?: string | null
  createdAt?: Date | string
  organizationId?: string | null
}

export type NotificationCreateOrConnectWithoutRecipientInput = {
  where: Prisma.NotificationWhereUniqueInput
  create: Prisma.XOR<Prisma.NotificationCreateWithoutRecipientInput, Prisma.NotificationUncheckedCreateWithoutRecipientInput>
}

export type NotificationCreateManyRecipientInputEnvelope = {
  data: Prisma.NotificationCreateManyRecipientInput | Prisma.NotificationCreateManyRecipientInput[]
  skipDuplicates?: boolean
}

export type NotificationUpsertWithWhereUniqueWithoutRecipientInput = {
  where: Prisma.NotificationWhereUniqueInput
  update: Prisma.XOR<Prisma.NotificationUpdateWithoutRecipientInput, Prisma.NotificationUncheckedUpdateWithoutRecipientInput>
  create: Prisma.XOR<Prisma.NotificationCreateWithoutRecipientInput, Prisma.NotificationUncheckedCreateWithoutRecipientInput>
}

export type NotificationUpdateWithWhereUniqueWithoutRecipientInput = {
  where: Prisma.NotificationWhereUniqueInput
  data: Prisma.XOR<Prisma.NotificationUpdateWithoutRecipientInput, Prisma.NotificationUncheckedUpdateWithoutRecipientInput>
}

export type NotificationUpdateManyWithWhereWithoutRecipientInput = {
  where: Prisma.NotificationScalarWhereInput
  data: Prisma.XOR<Prisma.NotificationUpdateManyMutationInput, Prisma.NotificationUncheckedUpdateManyWithoutRecipientInput>
}

export type NotificationCreateManyOrganizationInput = {
  id?: string
  recipientId: string
  type: $Enums.NotificationType
  title: string
  message: string
  isRead?: boolean
  actionUrl?: string | null
  createdAt?: Date | string
}

export type NotificationUpdateWithoutOrganizationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  type?: Prisma.EnumNotificationTypeFieldUpdateOperationsInput | $Enums.NotificationType
  title?: Prisma.StringFieldUpdateOperationsInput | string
  message?: Prisma.StringFieldUpdateOperationsInput | string
  isRead?: Prisma.BoolFieldUpdateOperationsInput | boolean
  actionUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  recipient?: Prisma.AlumniProfileUpdateOneRequiredWithoutNotificationsNestedInput
}

export type NotificationUncheckedUpdateWithoutOrganizationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  recipientId?: Prisma.StringFieldUpdateOperationsInput | string
  type?: Prisma.EnumNotificationTypeFieldUpdateOperationsInput | $Enums.NotificationType
  title?: Prisma.StringFieldUpdateOperationsInput | string
  message?: Prisma.StringFieldUpdateOperationsInput | string
  isRead?: Prisma.BoolFieldUpdateOperationsInput | boolean
  actionUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type NotificationUncheckedUpdateManyWithoutOrganizationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  recipientId?: Prisma.StringFieldUpdateOperationsInput | string
  type?: Prisma.EnumNotificationTypeFieldUpdateOperationsInput | $Enums.NotificationType
  title?: Prisma.StringFieldUpdateOperationsInput | string
  message?: Prisma.StringFieldUpdateOperationsInput | string
  isRead?: Prisma.BoolFieldUpdateOperationsInput | boolean
  actionUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type NotificationCreateManyRecipientInput = {
  id?: string
  type: $Enums.NotificationType
  title: string
  message: string
  isRead?: boolean
  actionUrl?: string | null
  createdAt?: Date | string
  organizationId?: string | null
}

export type NotificationUpdateWithoutRecipientInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  type?: Prisma.EnumNotificationTypeFieldUpdateOperationsInput | $Enums.NotificationType
  title?: Prisma.StringFieldUpdateOperationsInput | string
  message?: Prisma.StringFieldUpdateOperationsInput | string
  isRead?: Prisma.BoolFieldUpdateOperationsInput | boolean
  actionUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  Organization?: Prisma.OrganizationUpdateOneWithoutNotificationsNestedInput
}

export type NotificationUncheckedUpdateWithoutRecipientInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  type?: Prisma.EnumNotificationTypeFieldUpdateOperationsInput | $Enums.NotificationType
  title?: Prisma.StringFieldUpdateOperationsInput | string
  message?: Prisma.StringFieldUpdateOperationsInput | string
  isRead?: Prisma.BoolFieldUpdateOperationsInput | boolean
  actionUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  organizationId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type NotificationUncheckedUpdateManyWithoutRecipientInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  type?: Prisma.EnumNotificationTypeFieldUpdateOperationsInput | $Enums.NotificationType
  title?: Prisma.StringFieldUpdateOperationsInput | string
  message?: Prisma.StringFieldUpdateOperationsInput | string
  isRead?: Prisma.BoolFieldUpdateOperationsInput | boolean
  actionUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  organizationId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}



export type NotificationSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  recipientId?: boolean
  type?: boolean
  title?: boolean
  message?: boolean
  isRead?: boolean
  actionUrl?: boolean
  createdAt?: boolean
  organizationId?: boolean
  recipient?: boolean | Prisma.AlumniProfileDefaultArgs<ExtArgs>
  Organization?: boolean | Prisma.Notification$OrganizationArgs<ExtArgs>
}, ExtArgs["result"]["notification"]>



export type NotificationSelectScalar = {
  id?: boolean
  recipientId?: boolean
  type?: boolean
  title?: boolean
  message?: boolean
  isRead?: boolean
  actionUrl?: boolean
  createdAt?: boolean
  organizationId?: boolean
}

export type NotificationOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "recipientId" | "type" | "title" | "message" | "isRead" | "actionUrl" | "createdAt" | "organizationId", ExtArgs["result"]["notification"]>
export type NotificationInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  recipient?: boolean | Prisma.AlumniProfileDefaultArgs<ExtArgs>
  Organization?: boolean | Prisma.Notification$OrganizationArgs<ExtArgs>
}

export type $NotificationPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Notification"
  objects: {
    recipient: Prisma.$AlumniProfilePayload<ExtArgs>
    Organization: Prisma.$OrganizationPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    recipientId: string
    type: $Enums.NotificationType
    title: string
    message: string
    isRead: boolean
    actionUrl: string | null
    createdAt: Date
    organizationId: string | null
  }, ExtArgs["result"]["notification"]>
  composites: {}
}

export type NotificationGetPayload<S extends boolean | null | undefined | NotificationDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$NotificationPayload, S>

export type NotificationCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<NotificationFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: NotificationCountAggregateInputType | true
  }

export interface NotificationDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Notification'], meta: { name: 'Notification' } }
  /**
   * Find zero or one Notification that matches the filter.
   * @param {NotificationFindUniqueArgs} args - Arguments to find a Notification
   * @example
   * // Get one Notification
   * const notification = await prisma.notification.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends NotificationFindUniqueArgs>(args: Prisma.SelectSubset<T, NotificationFindUniqueArgs<ExtArgs>>): Prisma.Prisma__NotificationClient<runtime.Types.Result.GetResult<Prisma.$NotificationPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Notification that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {NotificationFindUniqueOrThrowArgs} args - Arguments to find a Notification
   * @example
   * // Get one Notification
   * const notification = await prisma.notification.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends NotificationFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, NotificationFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__NotificationClient<runtime.Types.Result.GetResult<Prisma.$NotificationPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Notification that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {NotificationFindFirstArgs} args - Arguments to find a Notification
   * @example
   * // Get one Notification
   * const notification = await prisma.notification.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends NotificationFindFirstArgs>(args?: Prisma.SelectSubset<T, NotificationFindFirstArgs<ExtArgs>>): Prisma.Prisma__NotificationClient<runtime.Types.Result.GetResult<Prisma.$NotificationPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Notification that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {NotificationFindFirstOrThrowArgs} args - Arguments to find a Notification
   * @example
   * // Get one Notification
   * const notification = await prisma.notification.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends NotificationFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, NotificationFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__NotificationClient<runtime.Types.Result.GetResult<Prisma.$NotificationPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Notifications that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {NotificationFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Notifications
   * const notifications = await prisma.notification.findMany()
   * 
   * // Get first 10 Notifications
   * const notifications = await prisma.notification.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const notificationWithIdOnly = await prisma.notification.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends NotificationFindManyArgs>(args?: Prisma.SelectSubset<T, NotificationFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$NotificationPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Notification.
   * @param {NotificationCreateArgs} args - Arguments to create a Notification.
   * @example
   * // Create one Notification
   * const Notification = await prisma.notification.create({
   *   data: {
   *     // ... data to create a Notification
   *   }
   * })
   * 
   */
  create<T extends NotificationCreateArgs>(args: Prisma.SelectSubset<T, NotificationCreateArgs<ExtArgs>>): Prisma.Prisma__NotificationClient<runtime.Types.Result.GetResult<Prisma.$NotificationPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Notifications.
   * @param {NotificationCreateManyArgs} args - Arguments to create many Notifications.
   * @example
   * // Create many Notifications
   * const notification = await prisma.notification.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends NotificationCreateManyArgs>(args?: Prisma.SelectSubset<T, NotificationCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Notification.
   * @param {NotificationDeleteArgs} args - Arguments to delete one Notification.
   * @example
   * // Delete one Notification
   * const Notification = await prisma.notification.delete({
   *   where: {
   *     // ... filter to delete one Notification
   *   }
   * })
   * 
   */
  delete<T extends NotificationDeleteArgs>(args: Prisma.SelectSubset<T, NotificationDeleteArgs<ExtArgs>>): Prisma.Prisma__NotificationClient<runtime.Types.Result.GetResult<Prisma.$NotificationPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Notification.
   * @param {NotificationUpdateArgs} args - Arguments to update one Notification.
   * @example
   * // Update one Notification
   * const notification = await prisma.notification.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends NotificationUpdateArgs>(args: Prisma.SelectSubset<T, NotificationUpdateArgs<ExtArgs>>): Prisma.Prisma__NotificationClient<runtime.Types.Result.GetResult<Prisma.$NotificationPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Notifications.
   * @param {NotificationDeleteManyArgs} args - Arguments to filter Notifications to delete.
   * @example
   * // Delete a few Notifications
   * const { count } = await prisma.notification.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends NotificationDeleteManyArgs>(args?: Prisma.SelectSubset<T, NotificationDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Notifications.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {NotificationUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Notifications
   * const notification = await prisma.notification.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends NotificationUpdateManyArgs>(args: Prisma.SelectSubset<T, NotificationUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Notification.
   * @param {NotificationUpsertArgs} args - Arguments to update or create a Notification.
   * @example
   * // Update or create a Notification
   * const notification = await prisma.notification.upsert({
   *   create: {
   *     // ... data to create a Notification
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Notification we want to update
   *   }
   * })
   */
  upsert<T extends NotificationUpsertArgs>(args: Prisma.SelectSubset<T, NotificationUpsertArgs<ExtArgs>>): Prisma.Prisma__NotificationClient<runtime.Types.Result.GetResult<Prisma.$NotificationPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Notifications.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {NotificationCountArgs} args - Arguments to filter Notifications to count.
   * @example
   * // Count the number of Notifications
   * const count = await prisma.notification.count({
   *   where: {
   *     // ... the filter for the Notifications we want to count
   *   }
   * })
  **/
  count<T extends NotificationCountArgs>(
    args?: Prisma.Subset<T, NotificationCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], NotificationCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Notification.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {NotificationAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends NotificationAggregateArgs>(args: Prisma.Subset<T, NotificationAggregateArgs>): Prisma.PrismaPromise<GetNotificationAggregateType<T>>

  /**
   * Group by Notification.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {NotificationGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends NotificationGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: NotificationGroupByArgs['orderBy'] }
      : { orderBy?: NotificationGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, NotificationGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetNotificationGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Notification model
 */
readonly fields: NotificationFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Notification.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__NotificationClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  recipient<T extends Prisma.AlumniProfileDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.AlumniProfileDefaultArgs<ExtArgs>>): Prisma.Prisma__AlumniProfileClient<runtime.Types.Result.GetResult<Prisma.$AlumniProfilePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  Organization<T extends Prisma.Notification$OrganizationArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Notification$OrganizationArgs<ExtArgs>>): Prisma.Prisma__OrganizationClient<runtime.Types.Result.GetResult<Prisma.$OrganizationPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Notification model
 */
export interface NotificationFieldRefs {
  readonly id: Prisma.FieldRef<"Notification", 'String'>
  readonly recipientId: Prisma.FieldRef<"Notification", 'String'>
  readonly type: Prisma.FieldRef<"Notification", 'NotificationType'>
  readonly title: Prisma.FieldRef<"Notification", 'String'>
  readonly message: Prisma.FieldRef<"Notification", 'String'>
  readonly isRead: Prisma.FieldRef<"Notification", 'Boolean'>
  readonly actionUrl: Prisma.FieldRef<"Notification", 'String'>
  readonly createdAt: Prisma.FieldRef<"Notification", 'DateTime'>
  readonly organizationId: Prisma.FieldRef<"Notification", 'String'>
}
    

// Custom InputTypes
/**
 * Notification findUnique
 */
export type NotificationFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Notification
   */
  select?: Prisma.NotificationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Notification
   */
  omit?: Prisma.NotificationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.NotificationInclude<ExtArgs> | null
  /**
   * Filter, which Notification to fetch.
   */
  where: Prisma.NotificationWhereUniqueInput
}

/**
 * Notification findUniqueOrThrow
 */
export type NotificationFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Notification
   */
  select?: Prisma.NotificationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Notification
   */
  omit?: Prisma.NotificationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.NotificationInclude<ExtArgs> | null
  /**
   * Filter, which Notification to fetch.
   */
  where: Prisma.NotificationWhereUniqueInput
}

/**
 * Notification findFirst
 */
export type NotificationFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Notification
   */
  select?: Prisma.NotificationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Notification
   */
  omit?: Prisma.NotificationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.NotificationInclude<ExtArgs> | null
  /**
   * Filter, which Notification to fetch.
   */
  where?: Prisma.NotificationWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Notifications to fetch.
   */
  orderBy?: Prisma.NotificationOrderByWithRelationInput | Prisma.NotificationOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Notifications.
   */
  cursor?: Prisma.NotificationWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Notifications from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Notifications.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Notifications.
   */
  distinct?: Prisma.NotificationScalarFieldEnum | Prisma.NotificationScalarFieldEnum[]
}

/**
 * Notification findFirstOrThrow
 */
export type NotificationFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Notification
   */
  select?: Prisma.NotificationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Notification
   */
  omit?: Prisma.NotificationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.NotificationInclude<ExtArgs> | null
  /**
   * Filter, which Notification to fetch.
   */
  where?: Prisma.NotificationWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Notifications to fetch.
   */
  orderBy?: Prisma.NotificationOrderByWithRelationInput | Prisma.NotificationOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Notifications.
   */
  cursor?: Prisma.NotificationWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Notifications from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Notifications.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Notifications.
   */
  distinct?: Prisma.NotificationScalarFieldEnum | Prisma.NotificationScalarFieldEnum[]
}

/**
 * Notification findMany
 */
export type NotificationFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Notification
   */
  select?: Prisma.NotificationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Notification
   */
  omit?: Prisma.NotificationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.NotificationInclude<ExtArgs> | null
  /**
   * Filter, which Notifications to fetch.
   */
  where?: Prisma.NotificationWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Notifications to fetch.
   */
  orderBy?: Prisma.NotificationOrderByWithRelationInput | Prisma.NotificationOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Notifications.
   */
  cursor?: Prisma.NotificationWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Notifications from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Notifications.
   */
  skip?: number
  distinct?: Prisma.NotificationScalarFieldEnum | Prisma.NotificationScalarFieldEnum[]
}

/**
 * Notification create
 */
export type NotificationCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Notification
   */
  select?: Prisma.NotificationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Notification
   */
  omit?: Prisma.NotificationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.NotificationInclude<ExtArgs> | null
  /**
   * The data needed to create a Notification.
   */
  data: Prisma.XOR<Prisma.NotificationCreateInput, Prisma.NotificationUncheckedCreateInput>
}

/**
 * Notification createMany
 */
export type NotificationCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Notifications.
   */
  data: Prisma.NotificationCreateManyInput | Prisma.NotificationCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Notification update
 */
export type NotificationUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Notification
   */
  select?: Prisma.NotificationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Notification
   */
  omit?: Prisma.NotificationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.NotificationInclude<ExtArgs> | null
  /**
   * The data needed to update a Notification.
   */
  data: Prisma.XOR<Prisma.NotificationUpdateInput, Prisma.NotificationUncheckedUpdateInput>
  /**
   * Choose, which Notification to update.
   */
  where: Prisma.NotificationWhereUniqueInput
}

/**
 * Notification updateMany
 */
export type NotificationUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Notifications.
   */
  data: Prisma.XOR<Prisma.NotificationUpdateManyMutationInput, Prisma.NotificationUncheckedUpdateManyInput>
  /**
   * Filter which Notifications to update
   */
  where?: Prisma.NotificationWhereInput
  /**
   * Limit how many Notifications to update.
   */
  limit?: number
}

/**
 * Notification upsert
 */
export type NotificationUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Notification
   */
  select?: Prisma.NotificationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Notification
   */
  omit?: Prisma.NotificationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.NotificationInclude<ExtArgs> | null
  /**
   * The filter to search for the Notification to update in case it exists.
   */
  where: Prisma.NotificationWhereUniqueInput
  /**
   * In case the Notification found by the `where` argument doesn't exist, create a new Notification with this data.
   */
  create: Prisma.XOR<Prisma.NotificationCreateInput, Prisma.NotificationUncheckedCreateInput>
  /**
   * In case the Notification was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.NotificationUpdateInput, Prisma.NotificationUncheckedUpdateInput>
}

/**
 * Notification delete
 */
export type NotificationDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Notification
   */
  select?: Prisma.NotificationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Notification
   */
  omit?: Prisma.NotificationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.NotificationInclude<ExtArgs> | null
  /**
   * Filter which Notification to delete.
   */
  where: Prisma.NotificationWhereUniqueInput
}

/**
 * Notification deleteMany
 */
export type NotificationDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Notifications to delete
   */
  where?: Prisma.NotificationWhereInput
  /**
   * Limit how many Notifications to delete.
   */
  limit?: number
}

/**
 * Notification.Organization
 */
export type Notification$OrganizationArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Organization
   */
  select?: Prisma.OrganizationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Organization
   */
  omit?: Prisma.OrganizationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.OrganizationInclude<ExtArgs> | null
  where?: Prisma.OrganizationWhereInput
}

/**
 * Notification without action
 */
export type NotificationDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Notification
   */
  select?: Prisma.NotificationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Notification
   */
  omit?: Prisma.NotificationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.NotificationInclude<ExtArgs> | null
}
