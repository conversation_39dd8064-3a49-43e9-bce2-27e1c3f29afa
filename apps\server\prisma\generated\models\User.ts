
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `User` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model User
 * 
 */
export type UserModel = runtime.Types.Result.DefaultSelection<Prisma.$UserPayload>

export type AggregateUser = {
  _count: UserCountAggregateOutputType | null
  _min: UserMinAggregateOutputType | null
  _max: UserMaxAggregateOutputType | null
}

export type UserMinAggregateOutputType = {
  id: string | null
  name: string | null
  email: string | null
  emailVerified: boolean | null
  image: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type UserMaxAggregateOutputType = {
  id: string | null
  name: string | null
  email: string | null
  emailVerified: boolean | null
  image: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type UserCountAggregateOutputType = {
  id: number
  name: number
  email: number
  emailVerified: number
  image: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type UserMinAggregateInputType = {
  id?: true
  name?: true
  email?: true
  emailVerified?: true
  image?: true
  createdAt?: true
  updatedAt?: true
}

export type UserMaxAggregateInputType = {
  id?: true
  name?: true
  email?: true
  emailVerified?: true
  image?: true
  createdAt?: true
  updatedAt?: true
}

export type UserCountAggregateInputType = {
  id?: true
  name?: true
  email?: true
  emailVerified?: true
  image?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type UserAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which User to aggregate.
   */
  where?: Prisma.UserWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Users to fetch.
   */
  orderBy?: Prisma.UserOrderByWithRelationInput | Prisma.UserOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.UserWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Users from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Users.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Users
  **/
  _count?: true | UserCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: UserMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: UserMaxAggregateInputType
}

export type GetUserAggregateType<T extends UserAggregateArgs> = {
      [P in keyof T & keyof AggregateUser]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateUser[P]>
    : Prisma.GetScalarType<T[P], AggregateUser[P]>
}




export type UserGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.UserWhereInput
  orderBy?: Prisma.UserOrderByWithAggregationInput | Prisma.UserOrderByWithAggregationInput[]
  by: Prisma.UserScalarFieldEnum[] | Prisma.UserScalarFieldEnum
  having?: Prisma.UserScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: UserCountAggregateInputType | true
  _min?: UserMinAggregateInputType
  _max?: UserMaxAggregateInputType
}

export type UserGroupByOutputType = {
  id: string
  name: string
  email: string
  emailVerified: boolean
  image: string | null
  createdAt: Date
  updatedAt: Date
  _count: UserCountAggregateOutputType | null
  _min: UserMinAggregateOutputType | null
  _max: UserMaxAggregateOutputType | null
}

type GetUserGroupByPayload<T extends UserGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<UserGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof UserGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], UserGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], UserGroupByOutputType[P]>
      }
    >
  >



export type UserWhereInput = {
  AND?: Prisma.UserWhereInput | Prisma.UserWhereInput[]
  OR?: Prisma.UserWhereInput[]
  NOT?: Prisma.UserWhereInput | Prisma.UserWhereInput[]
  id?: Prisma.StringFilter<"User"> | string
  name?: Prisma.StringFilter<"User"> | string
  email?: Prisma.StringFilter<"User"> | string
  emailVerified?: Prisma.BoolFilter<"User"> | boolean
  image?: Prisma.StringNullableFilter<"User"> | string | null
  createdAt?: Prisma.DateTimeFilter<"User"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"User"> | Date | string
  sessions?: Prisma.SessionListRelationFilter
  accounts?: Prisma.AccountListRelationFilter
  alumniProfile?: Prisma.XOR<Prisma.AlumniProfileNullableScalarRelationFilter, Prisma.AlumniProfileWhereInput> | null
  Member?: Prisma.MemberListRelationFilter
  Invitation?: Prisma.InvitationListRelationFilter
  TwoFactor?: Prisma.TwoFactorListRelationFilter
  Passkey?: Prisma.PasskeyListRelationFilter
}

export type UserOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  email?: Prisma.SortOrder
  emailVerified?: Prisma.SortOrder
  image?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  sessions?: Prisma.SessionOrderByRelationAggregateInput
  accounts?: Prisma.AccountOrderByRelationAggregateInput
  alumniProfile?: Prisma.AlumniProfileOrderByWithRelationInput
  Member?: Prisma.MemberOrderByRelationAggregateInput
  Invitation?: Prisma.InvitationOrderByRelationAggregateInput
  TwoFactor?: Prisma.TwoFactorOrderByRelationAggregateInput
  Passkey?: Prisma.PasskeyOrderByRelationAggregateInput
  _relevance?: Prisma.UserOrderByRelevanceInput
}

export type UserWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  email?: string
  AND?: Prisma.UserWhereInput | Prisma.UserWhereInput[]
  OR?: Prisma.UserWhereInput[]
  NOT?: Prisma.UserWhereInput | Prisma.UserWhereInput[]
  name?: Prisma.StringFilter<"User"> | string
  emailVerified?: Prisma.BoolFilter<"User"> | boolean
  image?: Prisma.StringNullableFilter<"User"> | string | null
  createdAt?: Prisma.DateTimeFilter<"User"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"User"> | Date | string
  sessions?: Prisma.SessionListRelationFilter
  accounts?: Prisma.AccountListRelationFilter
  alumniProfile?: Prisma.XOR<Prisma.AlumniProfileNullableScalarRelationFilter, Prisma.AlumniProfileWhereInput> | null
  Member?: Prisma.MemberListRelationFilter
  Invitation?: Prisma.InvitationListRelationFilter
  TwoFactor?: Prisma.TwoFactorListRelationFilter
  Passkey?: Prisma.PasskeyListRelationFilter
}, "id" | "email">

export type UserOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  email?: Prisma.SortOrder
  emailVerified?: Prisma.SortOrder
  image?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.UserCountOrderByAggregateInput
  _max?: Prisma.UserMaxOrderByAggregateInput
  _min?: Prisma.UserMinOrderByAggregateInput
}

export type UserScalarWhereWithAggregatesInput = {
  AND?: Prisma.UserScalarWhereWithAggregatesInput | Prisma.UserScalarWhereWithAggregatesInput[]
  OR?: Prisma.UserScalarWhereWithAggregatesInput[]
  NOT?: Prisma.UserScalarWhereWithAggregatesInput | Prisma.UserScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"User"> | string
  name?: Prisma.StringWithAggregatesFilter<"User"> | string
  email?: Prisma.StringWithAggregatesFilter<"User"> | string
  emailVerified?: Prisma.BoolWithAggregatesFilter<"User"> | boolean
  image?: Prisma.StringNullableWithAggregatesFilter<"User"> | string | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"User"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"User"> | Date | string
}

export type UserCreateInput = {
  id: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  createdAt: Date | string
  updatedAt: Date | string
  sessions?: Prisma.SessionCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountCreateNestedManyWithoutUserInput
  alumniProfile?: Prisma.AlumniProfileCreateNestedOneWithoutUserInput
  Member?: Prisma.MemberCreateNestedManyWithoutUserInput
  Invitation?: Prisma.InvitationCreateNestedManyWithoutUserInput
  TwoFactor?: Prisma.TwoFactorCreateNestedManyWithoutUserInput
  Passkey?: Prisma.PasskeyCreateNestedManyWithoutUserInput
}

export type UserUncheckedCreateInput = {
  id: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  createdAt: Date | string
  updatedAt: Date | string
  sessions?: Prisma.SessionUncheckedCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountUncheckedCreateNestedManyWithoutUserInput
  alumniProfile?: Prisma.AlumniProfileUncheckedCreateNestedOneWithoutUserInput
  Member?: Prisma.MemberUncheckedCreateNestedManyWithoutUserInput
  Invitation?: Prisma.InvitationUncheckedCreateNestedManyWithoutUserInput
  TwoFactor?: Prisma.TwoFactorUncheckedCreateNestedManyWithoutUserInput
  Passkey?: Prisma.PasskeyUncheckedCreateNestedManyWithoutUserInput
}

export type UserUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUpdateManyWithoutUserNestedInput
  alumniProfile?: Prisma.AlumniProfileUpdateOneWithoutUserNestedInput
  Member?: Prisma.MemberUpdateManyWithoutUserNestedInput
  Invitation?: Prisma.InvitationUpdateManyWithoutUserNestedInput
  TwoFactor?: Prisma.TwoFactorUpdateManyWithoutUserNestedInput
  Passkey?: Prisma.PasskeyUpdateManyWithoutUserNestedInput
}

export type UserUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUncheckedUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUncheckedUpdateManyWithoutUserNestedInput
  alumniProfile?: Prisma.AlumniProfileUncheckedUpdateOneWithoutUserNestedInput
  Member?: Prisma.MemberUncheckedUpdateManyWithoutUserNestedInput
  Invitation?: Prisma.InvitationUncheckedUpdateManyWithoutUserNestedInput
  TwoFactor?: Prisma.TwoFactorUncheckedUpdateManyWithoutUserNestedInput
  Passkey?: Prisma.PasskeyUncheckedUpdateManyWithoutUserNestedInput
}

export type UserCreateManyInput = {
  id: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  createdAt: Date | string
  updatedAt: Date | string
}

export type UserUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type UserUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type UserOrderByRelevanceInput = {
  fields: Prisma.UserOrderByRelevanceFieldEnum | Prisma.UserOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type UserCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  email?: Prisma.SortOrder
  emailVerified?: Prisma.SortOrder
  image?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type UserMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  email?: Prisma.SortOrder
  emailVerified?: Prisma.SortOrder
  image?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type UserMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  email?: Prisma.SortOrder
  emailVerified?: Prisma.SortOrder
  image?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type UserScalarRelationFilter = {
  is?: Prisma.UserWhereInput
  isNot?: Prisma.UserWhereInput
}

export type StringFieldUpdateOperationsInput = {
  set?: string
}

export type BoolFieldUpdateOperationsInput = {
  set?: boolean
}

export type NullableStringFieldUpdateOperationsInput = {
  set?: string | null
}

export type DateTimeFieldUpdateOperationsInput = {
  set?: Date | string
}

export type UserCreateNestedOneWithoutSessionsInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutSessionsInput, Prisma.UserUncheckedCreateWithoutSessionsInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutSessionsInput
  connect?: Prisma.UserWhereUniqueInput
}

export type UserUpdateOneRequiredWithoutSessionsNestedInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutSessionsInput, Prisma.UserUncheckedCreateWithoutSessionsInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutSessionsInput
  upsert?: Prisma.UserUpsertWithoutSessionsInput
  connect?: Prisma.UserWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.UserUpdateToOneWithWhereWithoutSessionsInput, Prisma.UserUpdateWithoutSessionsInput>, Prisma.UserUncheckedUpdateWithoutSessionsInput>
}

export type UserCreateNestedOneWithoutAccountsInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutAccountsInput, Prisma.UserUncheckedCreateWithoutAccountsInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutAccountsInput
  connect?: Prisma.UserWhereUniqueInput
}

export type UserUpdateOneRequiredWithoutAccountsNestedInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutAccountsInput, Prisma.UserUncheckedCreateWithoutAccountsInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutAccountsInput
  upsert?: Prisma.UserUpsertWithoutAccountsInput
  connect?: Prisma.UserWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.UserUpdateToOneWithWhereWithoutAccountsInput, Prisma.UserUpdateWithoutAccountsInput>, Prisma.UserUncheckedUpdateWithoutAccountsInput>
}

export type UserCreateNestedOneWithoutMemberInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutMemberInput, Prisma.UserUncheckedCreateWithoutMemberInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutMemberInput
  connect?: Prisma.UserWhereUniqueInput
}

export type UserUpdateOneRequiredWithoutMemberNestedInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutMemberInput, Prisma.UserUncheckedCreateWithoutMemberInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutMemberInput
  upsert?: Prisma.UserUpsertWithoutMemberInput
  connect?: Prisma.UserWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.UserUpdateToOneWithWhereWithoutMemberInput, Prisma.UserUpdateWithoutMemberInput>, Prisma.UserUncheckedUpdateWithoutMemberInput>
}

export type UserCreateNestedOneWithoutInvitationInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutInvitationInput, Prisma.UserUncheckedCreateWithoutInvitationInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutInvitationInput
  connect?: Prisma.UserWhereUniqueInput
}

export type UserUpdateOneRequiredWithoutInvitationNestedInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutInvitationInput, Prisma.UserUncheckedCreateWithoutInvitationInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutInvitationInput
  upsert?: Prisma.UserUpsertWithoutInvitationInput
  connect?: Prisma.UserWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.UserUpdateToOneWithWhereWithoutInvitationInput, Prisma.UserUpdateWithoutInvitationInput>, Prisma.UserUncheckedUpdateWithoutInvitationInput>
}

export type UserCreateNestedOneWithoutTwoFactorInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutTwoFactorInput, Prisma.UserUncheckedCreateWithoutTwoFactorInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutTwoFactorInput
  connect?: Prisma.UserWhereUniqueInput
}

export type UserUpdateOneRequiredWithoutTwoFactorNestedInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutTwoFactorInput, Prisma.UserUncheckedCreateWithoutTwoFactorInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutTwoFactorInput
  upsert?: Prisma.UserUpsertWithoutTwoFactorInput
  connect?: Prisma.UserWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.UserUpdateToOneWithWhereWithoutTwoFactorInput, Prisma.UserUpdateWithoutTwoFactorInput>, Prisma.UserUncheckedUpdateWithoutTwoFactorInput>
}

export type UserCreateNestedOneWithoutPasskeyInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutPasskeyInput, Prisma.UserUncheckedCreateWithoutPasskeyInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutPasskeyInput
  connect?: Prisma.UserWhereUniqueInput
}

export type UserUpdateOneRequiredWithoutPasskeyNestedInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutPasskeyInput, Prisma.UserUncheckedCreateWithoutPasskeyInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutPasskeyInput
  upsert?: Prisma.UserUpsertWithoutPasskeyInput
  connect?: Prisma.UserWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.UserUpdateToOneWithWhereWithoutPasskeyInput, Prisma.UserUpdateWithoutPasskeyInput>, Prisma.UserUncheckedUpdateWithoutPasskeyInput>
}

export type UserCreateNestedOneWithoutAlumniProfileInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutAlumniProfileInput, Prisma.UserUncheckedCreateWithoutAlumniProfileInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutAlumniProfileInput
  connect?: Prisma.UserWhereUniqueInput
}

export type UserUpdateOneRequiredWithoutAlumniProfileNestedInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutAlumniProfileInput, Prisma.UserUncheckedCreateWithoutAlumniProfileInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutAlumniProfileInput
  upsert?: Prisma.UserUpsertWithoutAlumniProfileInput
  connect?: Prisma.UserWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.UserUpdateToOneWithWhereWithoutAlumniProfileInput, Prisma.UserUpdateWithoutAlumniProfileInput>, Prisma.UserUncheckedUpdateWithoutAlumniProfileInput>
}

export type UserCreateWithoutSessionsInput = {
  id: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  createdAt: Date | string
  updatedAt: Date | string
  accounts?: Prisma.AccountCreateNestedManyWithoutUserInput
  alumniProfile?: Prisma.AlumniProfileCreateNestedOneWithoutUserInput
  Member?: Prisma.MemberCreateNestedManyWithoutUserInput
  Invitation?: Prisma.InvitationCreateNestedManyWithoutUserInput
  TwoFactor?: Prisma.TwoFactorCreateNestedManyWithoutUserInput
  Passkey?: Prisma.PasskeyCreateNestedManyWithoutUserInput
}

export type UserUncheckedCreateWithoutSessionsInput = {
  id: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  createdAt: Date | string
  updatedAt: Date | string
  accounts?: Prisma.AccountUncheckedCreateNestedManyWithoutUserInput
  alumniProfile?: Prisma.AlumniProfileUncheckedCreateNestedOneWithoutUserInput
  Member?: Prisma.MemberUncheckedCreateNestedManyWithoutUserInput
  Invitation?: Prisma.InvitationUncheckedCreateNestedManyWithoutUserInput
  TwoFactor?: Prisma.TwoFactorUncheckedCreateNestedManyWithoutUserInput
  Passkey?: Prisma.PasskeyUncheckedCreateNestedManyWithoutUserInput
}

export type UserCreateOrConnectWithoutSessionsInput = {
  where: Prisma.UserWhereUniqueInput
  create: Prisma.XOR<Prisma.UserCreateWithoutSessionsInput, Prisma.UserUncheckedCreateWithoutSessionsInput>
}

export type UserUpsertWithoutSessionsInput = {
  update: Prisma.XOR<Prisma.UserUpdateWithoutSessionsInput, Prisma.UserUncheckedUpdateWithoutSessionsInput>
  create: Prisma.XOR<Prisma.UserCreateWithoutSessionsInput, Prisma.UserUncheckedCreateWithoutSessionsInput>
  where?: Prisma.UserWhereInput
}

export type UserUpdateToOneWithWhereWithoutSessionsInput = {
  where?: Prisma.UserWhereInput
  data: Prisma.XOR<Prisma.UserUpdateWithoutSessionsInput, Prisma.UserUncheckedUpdateWithoutSessionsInput>
}

export type UserUpdateWithoutSessionsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  accounts?: Prisma.AccountUpdateManyWithoutUserNestedInput
  alumniProfile?: Prisma.AlumniProfileUpdateOneWithoutUserNestedInput
  Member?: Prisma.MemberUpdateManyWithoutUserNestedInput
  Invitation?: Prisma.InvitationUpdateManyWithoutUserNestedInput
  TwoFactor?: Prisma.TwoFactorUpdateManyWithoutUserNestedInput
  Passkey?: Prisma.PasskeyUpdateManyWithoutUserNestedInput
}

export type UserUncheckedUpdateWithoutSessionsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  accounts?: Prisma.AccountUncheckedUpdateManyWithoutUserNestedInput
  alumniProfile?: Prisma.AlumniProfileUncheckedUpdateOneWithoutUserNestedInput
  Member?: Prisma.MemberUncheckedUpdateManyWithoutUserNestedInput
  Invitation?: Prisma.InvitationUncheckedUpdateManyWithoutUserNestedInput
  TwoFactor?: Prisma.TwoFactorUncheckedUpdateManyWithoutUserNestedInput
  Passkey?: Prisma.PasskeyUncheckedUpdateManyWithoutUserNestedInput
}

export type UserCreateWithoutAccountsInput = {
  id: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  createdAt: Date | string
  updatedAt: Date | string
  sessions?: Prisma.SessionCreateNestedManyWithoutUserInput
  alumniProfile?: Prisma.AlumniProfileCreateNestedOneWithoutUserInput
  Member?: Prisma.MemberCreateNestedManyWithoutUserInput
  Invitation?: Prisma.InvitationCreateNestedManyWithoutUserInput
  TwoFactor?: Prisma.TwoFactorCreateNestedManyWithoutUserInput
  Passkey?: Prisma.PasskeyCreateNestedManyWithoutUserInput
}

export type UserUncheckedCreateWithoutAccountsInput = {
  id: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  createdAt: Date | string
  updatedAt: Date | string
  sessions?: Prisma.SessionUncheckedCreateNestedManyWithoutUserInput
  alumniProfile?: Prisma.AlumniProfileUncheckedCreateNestedOneWithoutUserInput
  Member?: Prisma.MemberUncheckedCreateNestedManyWithoutUserInput
  Invitation?: Prisma.InvitationUncheckedCreateNestedManyWithoutUserInput
  TwoFactor?: Prisma.TwoFactorUncheckedCreateNestedManyWithoutUserInput
  Passkey?: Prisma.PasskeyUncheckedCreateNestedManyWithoutUserInput
}

export type UserCreateOrConnectWithoutAccountsInput = {
  where: Prisma.UserWhereUniqueInput
  create: Prisma.XOR<Prisma.UserCreateWithoutAccountsInput, Prisma.UserUncheckedCreateWithoutAccountsInput>
}

export type UserUpsertWithoutAccountsInput = {
  update: Prisma.XOR<Prisma.UserUpdateWithoutAccountsInput, Prisma.UserUncheckedUpdateWithoutAccountsInput>
  create: Prisma.XOR<Prisma.UserCreateWithoutAccountsInput, Prisma.UserUncheckedCreateWithoutAccountsInput>
  where?: Prisma.UserWhereInput
}

export type UserUpdateToOneWithWhereWithoutAccountsInput = {
  where?: Prisma.UserWhereInput
  data: Prisma.XOR<Prisma.UserUpdateWithoutAccountsInput, Prisma.UserUncheckedUpdateWithoutAccountsInput>
}

export type UserUpdateWithoutAccountsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUpdateManyWithoutUserNestedInput
  alumniProfile?: Prisma.AlumniProfileUpdateOneWithoutUserNestedInput
  Member?: Prisma.MemberUpdateManyWithoutUserNestedInput
  Invitation?: Prisma.InvitationUpdateManyWithoutUserNestedInput
  TwoFactor?: Prisma.TwoFactorUpdateManyWithoutUserNestedInput
  Passkey?: Prisma.PasskeyUpdateManyWithoutUserNestedInput
}

export type UserUncheckedUpdateWithoutAccountsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUncheckedUpdateManyWithoutUserNestedInput
  alumniProfile?: Prisma.AlumniProfileUncheckedUpdateOneWithoutUserNestedInput
  Member?: Prisma.MemberUncheckedUpdateManyWithoutUserNestedInput
  Invitation?: Prisma.InvitationUncheckedUpdateManyWithoutUserNestedInput
  TwoFactor?: Prisma.TwoFactorUncheckedUpdateManyWithoutUserNestedInput
  Passkey?: Prisma.PasskeyUncheckedUpdateManyWithoutUserNestedInput
}

export type UserCreateWithoutMemberInput = {
  id: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  createdAt: Date | string
  updatedAt: Date | string
  sessions?: Prisma.SessionCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountCreateNestedManyWithoutUserInput
  alumniProfile?: Prisma.AlumniProfileCreateNestedOneWithoutUserInput
  Invitation?: Prisma.InvitationCreateNestedManyWithoutUserInput
  TwoFactor?: Prisma.TwoFactorCreateNestedManyWithoutUserInput
  Passkey?: Prisma.PasskeyCreateNestedManyWithoutUserInput
}

export type UserUncheckedCreateWithoutMemberInput = {
  id: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  createdAt: Date | string
  updatedAt: Date | string
  sessions?: Prisma.SessionUncheckedCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountUncheckedCreateNestedManyWithoutUserInput
  alumniProfile?: Prisma.AlumniProfileUncheckedCreateNestedOneWithoutUserInput
  Invitation?: Prisma.InvitationUncheckedCreateNestedManyWithoutUserInput
  TwoFactor?: Prisma.TwoFactorUncheckedCreateNestedManyWithoutUserInput
  Passkey?: Prisma.PasskeyUncheckedCreateNestedManyWithoutUserInput
}

export type UserCreateOrConnectWithoutMemberInput = {
  where: Prisma.UserWhereUniqueInput
  create: Prisma.XOR<Prisma.UserCreateWithoutMemberInput, Prisma.UserUncheckedCreateWithoutMemberInput>
}

export type UserUpsertWithoutMemberInput = {
  update: Prisma.XOR<Prisma.UserUpdateWithoutMemberInput, Prisma.UserUncheckedUpdateWithoutMemberInput>
  create: Prisma.XOR<Prisma.UserCreateWithoutMemberInput, Prisma.UserUncheckedCreateWithoutMemberInput>
  where?: Prisma.UserWhereInput
}

export type UserUpdateToOneWithWhereWithoutMemberInput = {
  where?: Prisma.UserWhereInput
  data: Prisma.XOR<Prisma.UserUpdateWithoutMemberInput, Prisma.UserUncheckedUpdateWithoutMemberInput>
}

export type UserUpdateWithoutMemberInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUpdateManyWithoutUserNestedInput
  alumniProfile?: Prisma.AlumniProfileUpdateOneWithoutUserNestedInput
  Invitation?: Prisma.InvitationUpdateManyWithoutUserNestedInput
  TwoFactor?: Prisma.TwoFactorUpdateManyWithoutUserNestedInput
  Passkey?: Prisma.PasskeyUpdateManyWithoutUserNestedInput
}

export type UserUncheckedUpdateWithoutMemberInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUncheckedUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUncheckedUpdateManyWithoutUserNestedInput
  alumniProfile?: Prisma.AlumniProfileUncheckedUpdateOneWithoutUserNestedInput
  Invitation?: Prisma.InvitationUncheckedUpdateManyWithoutUserNestedInput
  TwoFactor?: Prisma.TwoFactorUncheckedUpdateManyWithoutUserNestedInput
  Passkey?: Prisma.PasskeyUncheckedUpdateManyWithoutUserNestedInput
}

export type UserCreateWithoutInvitationInput = {
  id: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  createdAt: Date | string
  updatedAt: Date | string
  sessions?: Prisma.SessionCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountCreateNestedManyWithoutUserInput
  alumniProfile?: Prisma.AlumniProfileCreateNestedOneWithoutUserInput
  Member?: Prisma.MemberCreateNestedManyWithoutUserInput
  TwoFactor?: Prisma.TwoFactorCreateNestedManyWithoutUserInput
  Passkey?: Prisma.PasskeyCreateNestedManyWithoutUserInput
}

export type UserUncheckedCreateWithoutInvitationInput = {
  id: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  createdAt: Date | string
  updatedAt: Date | string
  sessions?: Prisma.SessionUncheckedCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountUncheckedCreateNestedManyWithoutUserInput
  alumniProfile?: Prisma.AlumniProfileUncheckedCreateNestedOneWithoutUserInput
  Member?: Prisma.MemberUncheckedCreateNestedManyWithoutUserInput
  TwoFactor?: Prisma.TwoFactorUncheckedCreateNestedManyWithoutUserInput
  Passkey?: Prisma.PasskeyUncheckedCreateNestedManyWithoutUserInput
}

export type UserCreateOrConnectWithoutInvitationInput = {
  where: Prisma.UserWhereUniqueInput
  create: Prisma.XOR<Prisma.UserCreateWithoutInvitationInput, Prisma.UserUncheckedCreateWithoutInvitationInput>
}

export type UserUpsertWithoutInvitationInput = {
  update: Prisma.XOR<Prisma.UserUpdateWithoutInvitationInput, Prisma.UserUncheckedUpdateWithoutInvitationInput>
  create: Prisma.XOR<Prisma.UserCreateWithoutInvitationInput, Prisma.UserUncheckedCreateWithoutInvitationInput>
  where?: Prisma.UserWhereInput
}

export type UserUpdateToOneWithWhereWithoutInvitationInput = {
  where?: Prisma.UserWhereInput
  data: Prisma.XOR<Prisma.UserUpdateWithoutInvitationInput, Prisma.UserUncheckedUpdateWithoutInvitationInput>
}

export type UserUpdateWithoutInvitationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUpdateManyWithoutUserNestedInput
  alumniProfile?: Prisma.AlumniProfileUpdateOneWithoutUserNestedInput
  Member?: Prisma.MemberUpdateManyWithoutUserNestedInput
  TwoFactor?: Prisma.TwoFactorUpdateManyWithoutUserNestedInput
  Passkey?: Prisma.PasskeyUpdateManyWithoutUserNestedInput
}

export type UserUncheckedUpdateWithoutInvitationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUncheckedUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUncheckedUpdateManyWithoutUserNestedInput
  alumniProfile?: Prisma.AlumniProfileUncheckedUpdateOneWithoutUserNestedInput
  Member?: Prisma.MemberUncheckedUpdateManyWithoutUserNestedInput
  TwoFactor?: Prisma.TwoFactorUncheckedUpdateManyWithoutUserNestedInput
  Passkey?: Prisma.PasskeyUncheckedUpdateManyWithoutUserNestedInput
}

export type UserCreateWithoutTwoFactorInput = {
  id: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  createdAt: Date | string
  updatedAt: Date | string
  sessions?: Prisma.SessionCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountCreateNestedManyWithoutUserInput
  alumniProfile?: Prisma.AlumniProfileCreateNestedOneWithoutUserInput
  Member?: Prisma.MemberCreateNestedManyWithoutUserInput
  Invitation?: Prisma.InvitationCreateNestedManyWithoutUserInput
  Passkey?: Prisma.PasskeyCreateNestedManyWithoutUserInput
}

export type UserUncheckedCreateWithoutTwoFactorInput = {
  id: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  createdAt: Date | string
  updatedAt: Date | string
  sessions?: Prisma.SessionUncheckedCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountUncheckedCreateNestedManyWithoutUserInput
  alumniProfile?: Prisma.AlumniProfileUncheckedCreateNestedOneWithoutUserInput
  Member?: Prisma.MemberUncheckedCreateNestedManyWithoutUserInput
  Invitation?: Prisma.InvitationUncheckedCreateNestedManyWithoutUserInput
  Passkey?: Prisma.PasskeyUncheckedCreateNestedManyWithoutUserInput
}

export type UserCreateOrConnectWithoutTwoFactorInput = {
  where: Prisma.UserWhereUniqueInput
  create: Prisma.XOR<Prisma.UserCreateWithoutTwoFactorInput, Prisma.UserUncheckedCreateWithoutTwoFactorInput>
}

export type UserUpsertWithoutTwoFactorInput = {
  update: Prisma.XOR<Prisma.UserUpdateWithoutTwoFactorInput, Prisma.UserUncheckedUpdateWithoutTwoFactorInput>
  create: Prisma.XOR<Prisma.UserCreateWithoutTwoFactorInput, Prisma.UserUncheckedCreateWithoutTwoFactorInput>
  where?: Prisma.UserWhereInput
}

export type UserUpdateToOneWithWhereWithoutTwoFactorInput = {
  where?: Prisma.UserWhereInput
  data: Prisma.XOR<Prisma.UserUpdateWithoutTwoFactorInput, Prisma.UserUncheckedUpdateWithoutTwoFactorInput>
}

export type UserUpdateWithoutTwoFactorInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUpdateManyWithoutUserNestedInput
  alumniProfile?: Prisma.AlumniProfileUpdateOneWithoutUserNestedInput
  Member?: Prisma.MemberUpdateManyWithoutUserNestedInput
  Invitation?: Prisma.InvitationUpdateManyWithoutUserNestedInput
  Passkey?: Prisma.PasskeyUpdateManyWithoutUserNestedInput
}

export type UserUncheckedUpdateWithoutTwoFactorInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUncheckedUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUncheckedUpdateManyWithoutUserNestedInput
  alumniProfile?: Prisma.AlumniProfileUncheckedUpdateOneWithoutUserNestedInput
  Member?: Prisma.MemberUncheckedUpdateManyWithoutUserNestedInput
  Invitation?: Prisma.InvitationUncheckedUpdateManyWithoutUserNestedInput
  Passkey?: Prisma.PasskeyUncheckedUpdateManyWithoutUserNestedInput
}

export type UserCreateWithoutPasskeyInput = {
  id: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  createdAt: Date | string
  updatedAt: Date | string
  sessions?: Prisma.SessionCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountCreateNestedManyWithoutUserInput
  alumniProfile?: Prisma.AlumniProfileCreateNestedOneWithoutUserInput
  Member?: Prisma.MemberCreateNestedManyWithoutUserInput
  Invitation?: Prisma.InvitationCreateNestedManyWithoutUserInput
  TwoFactor?: Prisma.TwoFactorCreateNestedManyWithoutUserInput
}

export type UserUncheckedCreateWithoutPasskeyInput = {
  id: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  createdAt: Date | string
  updatedAt: Date | string
  sessions?: Prisma.SessionUncheckedCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountUncheckedCreateNestedManyWithoutUserInput
  alumniProfile?: Prisma.AlumniProfileUncheckedCreateNestedOneWithoutUserInput
  Member?: Prisma.MemberUncheckedCreateNestedManyWithoutUserInput
  Invitation?: Prisma.InvitationUncheckedCreateNestedManyWithoutUserInput
  TwoFactor?: Prisma.TwoFactorUncheckedCreateNestedManyWithoutUserInput
}

export type UserCreateOrConnectWithoutPasskeyInput = {
  where: Prisma.UserWhereUniqueInput
  create: Prisma.XOR<Prisma.UserCreateWithoutPasskeyInput, Prisma.UserUncheckedCreateWithoutPasskeyInput>
}

export type UserUpsertWithoutPasskeyInput = {
  update: Prisma.XOR<Prisma.UserUpdateWithoutPasskeyInput, Prisma.UserUncheckedUpdateWithoutPasskeyInput>
  create: Prisma.XOR<Prisma.UserCreateWithoutPasskeyInput, Prisma.UserUncheckedCreateWithoutPasskeyInput>
  where?: Prisma.UserWhereInput
}

export type UserUpdateToOneWithWhereWithoutPasskeyInput = {
  where?: Prisma.UserWhereInput
  data: Prisma.XOR<Prisma.UserUpdateWithoutPasskeyInput, Prisma.UserUncheckedUpdateWithoutPasskeyInput>
}

export type UserUpdateWithoutPasskeyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUpdateManyWithoutUserNestedInput
  alumniProfile?: Prisma.AlumniProfileUpdateOneWithoutUserNestedInput
  Member?: Prisma.MemberUpdateManyWithoutUserNestedInput
  Invitation?: Prisma.InvitationUpdateManyWithoutUserNestedInput
  TwoFactor?: Prisma.TwoFactorUpdateManyWithoutUserNestedInput
}

export type UserUncheckedUpdateWithoutPasskeyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUncheckedUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUncheckedUpdateManyWithoutUserNestedInput
  alumniProfile?: Prisma.AlumniProfileUncheckedUpdateOneWithoutUserNestedInput
  Member?: Prisma.MemberUncheckedUpdateManyWithoutUserNestedInput
  Invitation?: Prisma.InvitationUncheckedUpdateManyWithoutUserNestedInput
  TwoFactor?: Prisma.TwoFactorUncheckedUpdateManyWithoutUserNestedInput
}

export type UserCreateWithoutAlumniProfileInput = {
  id: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  createdAt: Date | string
  updatedAt: Date | string
  sessions?: Prisma.SessionCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountCreateNestedManyWithoutUserInput
  Member?: Prisma.MemberCreateNestedManyWithoutUserInput
  Invitation?: Prisma.InvitationCreateNestedManyWithoutUserInput
  TwoFactor?: Prisma.TwoFactorCreateNestedManyWithoutUserInput
  Passkey?: Prisma.PasskeyCreateNestedManyWithoutUserInput
}

export type UserUncheckedCreateWithoutAlumniProfileInput = {
  id: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  createdAt: Date | string
  updatedAt: Date | string
  sessions?: Prisma.SessionUncheckedCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountUncheckedCreateNestedManyWithoutUserInput
  Member?: Prisma.MemberUncheckedCreateNestedManyWithoutUserInput
  Invitation?: Prisma.InvitationUncheckedCreateNestedManyWithoutUserInput
  TwoFactor?: Prisma.TwoFactorUncheckedCreateNestedManyWithoutUserInput
  Passkey?: Prisma.PasskeyUncheckedCreateNestedManyWithoutUserInput
}

export type UserCreateOrConnectWithoutAlumniProfileInput = {
  where: Prisma.UserWhereUniqueInput
  create: Prisma.XOR<Prisma.UserCreateWithoutAlumniProfileInput, Prisma.UserUncheckedCreateWithoutAlumniProfileInput>
}

export type UserUpsertWithoutAlumniProfileInput = {
  update: Prisma.XOR<Prisma.UserUpdateWithoutAlumniProfileInput, Prisma.UserUncheckedUpdateWithoutAlumniProfileInput>
  create: Prisma.XOR<Prisma.UserCreateWithoutAlumniProfileInput, Prisma.UserUncheckedCreateWithoutAlumniProfileInput>
  where?: Prisma.UserWhereInput
}

export type UserUpdateToOneWithWhereWithoutAlumniProfileInput = {
  where?: Prisma.UserWhereInput
  data: Prisma.XOR<Prisma.UserUpdateWithoutAlumniProfileInput, Prisma.UserUncheckedUpdateWithoutAlumniProfileInput>
}

export type UserUpdateWithoutAlumniProfileInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUpdateManyWithoutUserNestedInput
  Member?: Prisma.MemberUpdateManyWithoutUserNestedInput
  Invitation?: Prisma.InvitationUpdateManyWithoutUserNestedInput
  TwoFactor?: Prisma.TwoFactorUpdateManyWithoutUserNestedInput
  Passkey?: Prisma.PasskeyUpdateManyWithoutUserNestedInput
}

export type UserUncheckedUpdateWithoutAlumniProfileInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUncheckedUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUncheckedUpdateManyWithoutUserNestedInput
  Member?: Prisma.MemberUncheckedUpdateManyWithoutUserNestedInput
  Invitation?: Prisma.InvitationUncheckedUpdateManyWithoutUserNestedInput
  TwoFactor?: Prisma.TwoFactorUncheckedUpdateManyWithoutUserNestedInput
  Passkey?: Prisma.PasskeyUncheckedUpdateManyWithoutUserNestedInput
}


/**
 * Count Type UserCountOutputType
 */

export type UserCountOutputType = {
  sessions: number
  accounts: number
  Member: number
  Invitation: number
  TwoFactor: number
  Passkey: number
}

export type UserCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  sessions?: boolean | UserCountOutputTypeCountSessionsArgs
  accounts?: boolean | UserCountOutputTypeCountAccountsArgs
  Member?: boolean | UserCountOutputTypeCountMemberArgs
  Invitation?: boolean | UserCountOutputTypeCountInvitationArgs
  TwoFactor?: boolean | UserCountOutputTypeCountTwoFactorArgs
  Passkey?: boolean | UserCountOutputTypeCountPasskeyArgs
}

/**
 * UserCountOutputType without action
 */
export type UserCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UserCountOutputType
   */
  select?: Prisma.UserCountOutputTypeSelect<ExtArgs> | null
}

/**
 * UserCountOutputType without action
 */
export type UserCountOutputTypeCountSessionsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.SessionWhereInput
}

/**
 * UserCountOutputType without action
 */
export type UserCountOutputTypeCountAccountsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.AccountWhereInput
}

/**
 * UserCountOutputType without action
 */
export type UserCountOutputTypeCountMemberArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.MemberWhereInput
}

/**
 * UserCountOutputType without action
 */
export type UserCountOutputTypeCountInvitationArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.InvitationWhereInput
}

/**
 * UserCountOutputType without action
 */
export type UserCountOutputTypeCountTwoFactorArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.TwoFactorWhereInput
}

/**
 * UserCountOutputType without action
 */
export type UserCountOutputTypeCountPasskeyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.PasskeyWhereInput
}


export type UserSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  email?: boolean
  emailVerified?: boolean
  image?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  sessions?: boolean | Prisma.User$sessionsArgs<ExtArgs>
  accounts?: boolean | Prisma.User$accountsArgs<ExtArgs>
  alumniProfile?: boolean | Prisma.User$alumniProfileArgs<ExtArgs>
  Member?: boolean | Prisma.User$MemberArgs<ExtArgs>
  Invitation?: boolean | Prisma.User$InvitationArgs<ExtArgs>
  TwoFactor?: boolean | Prisma.User$TwoFactorArgs<ExtArgs>
  Passkey?: boolean | Prisma.User$PasskeyArgs<ExtArgs>
  _count?: boolean | Prisma.UserCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["user"]>



export type UserSelectScalar = {
  id?: boolean
  name?: boolean
  email?: boolean
  emailVerified?: boolean
  image?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type UserOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "name" | "email" | "emailVerified" | "image" | "createdAt" | "updatedAt", ExtArgs["result"]["user"]>
export type UserInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  sessions?: boolean | Prisma.User$sessionsArgs<ExtArgs>
  accounts?: boolean | Prisma.User$accountsArgs<ExtArgs>
  alumniProfile?: boolean | Prisma.User$alumniProfileArgs<ExtArgs>
  Member?: boolean | Prisma.User$MemberArgs<ExtArgs>
  Invitation?: boolean | Prisma.User$InvitationArgs<ExtArgs>
  TwoFactor?: boolean | Prisma.User$TwoFactorArgs<ExtArgs>
  Passkey?: boolean | Prisma.User$PasskeyArgs<ExtArgs>
  _count?: boolean | Prisma.UserCountOutputTypeDefaultArgs<ExtArgs>
}

export type $UserPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "User"
  objects: {
    sessions: Prisma.$SessionPayload<ExtArgs>[]
    accounts: Prisma.$AccountPayload<ExtArgs>[]
    alumniProfile: Prisma.$AlumniProfilePayload<ExtArgs> | null
    Member: Prisma.$MemberPayload<ExtArgs>[]
    Invitation: Prisma.$InvitationPayload<ExtArgs>[]
    TwoFactor: Prisma.$TwoFactorPayload<ExtArgs>[]
    Passkey: Prisma.$PasskeyPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    name: string
    email: string
    emailVerified: boolean
    image: string | null
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["user"]>
  composites: {}
}

export type UserGetPayload<S extends boolean | null | undefined | UserDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$UserPayload, S>

export type UserCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<UserFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: UserCountAggregateInputType | true
  }

export interface UserDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['User'], meta: { name: 'User' } }
  /**
   * Find zero or one User that matches the filter.
   * @param {UserFindUniqueArgs} args - Arguments to find a User
   * @example
   * // Get one User
   * const user = await prisma.user.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends UserFindUniqueArgs>(args: Prisma.SelectSubset<T, UserFindUniqueArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one User that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {UserFindUniqueOrThrowArgs} args - Arguments to find a User
   * @example
   * // Get one User
   * const user = await prisma.user.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends UserFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, UserFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first User that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UserFindFirstArgs} args - Arguments to find a User
   * @example
   * // Get one User
   * const user = await prisma.user.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends UserFindFirstArgs>(args?: Prisma.SelectSubset<T, UserFindFirstArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first User that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UserFindFirstOrThrowArgs} args - Arguments to find a User
   * @example
   * // Get one User
   * const user = await prisma.user.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends UserFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, UserFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Users that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UserFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Users
   * const users = await prisma.user.findMany()
   * 
   * // Get first 10 Users
   * const users = await prisma.user.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const userWithIdOnly = await prisma.user.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends UserFindManyArgs>(args?: Prisma.SelectSubset<T, UserFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a User.
   * @param {UserCreateArgs} args - Arguments to create a User.
   * @example
   * // Create one User
   * const User = await prisma.user.create({
   *   data: {
   *     // ... data to create a User
   *   }
   * })
   * 
   */
  create<T extends UserCreateArgs>(args: Prisma.SelectSubset<T, UserCreateArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Users.
   * @param {UserCreateManyArgs} args - Arguments to create many Users.
   * @example
   * // Create many Users
   * const user = await prisma.user.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends UserCreateManyArgs>(args?: Prisma.SelectSubset<T, UserCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a User.
   * @param {UserDeleteArgs} args - Arguments to delete one User.
   * @example
   * // Delete one User
   * const User = await prisma.user.delete({
   *   where: {
   *     // ... filter to delete one User
   *   }
   * })
   * 
   */
  delete<T extends UserDeleteArgs>(args: Prisma.SelectSubset<T, UserDeleteArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one User.
   * @param {UserUpdateArgs} args - Arguments to update one User.
   * @example
   * // Update one User
   * const user = await prisma.user.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends UserUpdateArgs>(args: Prisma.SelectSubset<T, UserUpdateArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Users.
   * @param {UserDeleteManyArgs} args - Arguments to filter Users to delete.
   * @example
   * // Delete a few Users
   * const { count } = await prisma.user.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends UserDeleteManyArgs>(args?: Prisma.SelectSubset<T, UserDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Users.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UserUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Users
   * const user = await prisma.user.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends UserUpdateManyArgs>(args: Prisma.SelectSubset<T, UserUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one User.
   * @param {UserUpsertArgs} args - Arguments to update or create a User.
   * @example
   * // Update or create a User
   * const user = await prisma.user.upsert({
   *   create: {
   *     // ... data to create a User
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the User we want to update
   *   }
   * })
   */
  upsert<T extends UserUpsertArgs>(args: Prisma.SelectSubset<T, UserUpsertArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Users.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UserCountArgs} args - Arguments to filter Users to count.
   * @example
   * // Count the number of Users
   * const count = await prisma.user.count({
   *   where: {
   *     // ... the filter for the Users we want to count
   *   }
   * })
  **/
  count<T extends UserCountArgs>(
    args?: Prisma.Subset<T, UserCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], UserCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a User.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UserAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends UserAggregateArgs>(args: Prisma.Subset<T, UserAggregateArgs>): Prisma.PrismaPromise<GetUserAggregateType<T>>

  /**
   * Group by User.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UserGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends UserGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: UserGroupByArgs['orderBy'] }
      : { orderBy?: UserGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, UserGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetUserGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the User model
 */
readonly fields: UserFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for User.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__UserClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  sessions<T extends Prisma.User$sessionsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.User$sessionsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$SessionPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  accounts<T extends Prisma.User$accountsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.User$accountsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$AccountPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  alumniProfile<T extends Prisma.User$alumniProfileArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.User$alumniProfileArgs<ExtArgs>>): Prisma.Prisma__AlumniProfileClient<runtime.Types.Result.GetResult<Prisma.$AlumniProfilePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  Member<T extends Prisma.User$MemberArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.User$MemberArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$MemberPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  Invitation<T extends Prisma.User$InvitationArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.User$InvitationArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$InvitationPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  TwoFactor<T extends Prisma.User$TwoFactorArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.User$TwoFactorArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TwoFactorPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  Passkey<T extends Prisma.User$PasskeyArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.User$PasskeyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$PasskeyPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the User model
 */
export interface UserFieldRefs {
  readonly id: Prisma.FieldRef<"User", 'String'>
  readonly name: Prisma.FieldRef<"User", 'String'>
  readonly email: Prisma.FieldRef<"User", 'String'>
  readonly emailVerified: Prisma.FieldRef<"User", 'Boolean'>
  readonly image: Prisma.FieldRef<"User", 'String'>
  readonly createdAt: Prisma.FieldRef<"User", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"User", 'DateTime'>
}
    

// Custom InputTypes
/**
 * User findUnique
 */
export type UserFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * Filter, which User to fetch.
   */
  where: Prisma.UserWhereUniqueInput
}

/**
 * User findUniqueOrThrow
 */
export type UserFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * Filter, which User to fetch.
   */
  where: Prisma.UserWhereUniqueInput
}

/**
 * User findFirst
 */
export type UserFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * Filter, which User to fetch.
   */
  where?: Prisma.UserWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Users to fetch.
   */
  orderBy?: Prisma.UserOrderByWithRelationInput | Prisma.UserOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Users.
   */
  cursor?: Prisma.UserWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Users from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Users.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Users.
   */
  distinct?: Prisma.UserScalarFieldEnum | Prisma.UserScalarFieldEnum[]
}

/**
 * User findFirstOrThrow
 */
export type UserFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * Filter, which User to fetch.
   */
  where?: Prisma.UserWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Users to fetch.
   */
  orderBy?: Prisma.UserOrderByWithRelationInput | Prisma.UserOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Users.
   */
  cursor?: Prisma.UserWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Users from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Users.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Users.
   */
  distinct?: Prisma.UserScalarFieldEnum | Prisma.UserScalarFieldEnum[]
}

/**
 * User findMany
 */
export type UserFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * Filter, which Users to fetch.
   */
  where?: Prisma.UserWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Users to fetch.
   */
  orderBy?: Prisma.UserOrderByWithRelationInput | Prisma.UserOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Users.
   */
  cursor?: Prisma.UserWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Users from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Users.
   */
  skip?: number
  distinct?: Prisma.UserScalarFieldEnum | Prisma.UserScalarFieldEnum[]
}

/**
 * User create
 */
export type UserCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * The data needed to create a User.
   */
  data: Prisma.XOR<Prisma.UserCreateInput, Prisma.UserUncheckedCreateInput>
}

/**
 * User createMany
 */
export type UserCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Users.
   */
  data: Prisma.UserCreateManyInput | Prisma.UserCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * User update
 */
export type UserUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * The data needed to update a User.
   */
  data: Prisma.XOR<Prisma.UserUpdateInput, Prisma.UserUncheckedUpdateInput>
  /**
   * Choose, which User to update.
   */
  where: Prisma.UserWhereUniqueInput
}

/**
 * User updateMany
 */
export type UserUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Users.
   */
  data: Prisma.XOR<Prisma.UserUpdateManyMutationInput, Prisma.UserUncheckedUpdateManyInput>
  /**
   * Filter which Users to update
   */
  where?: Prisma.UserWhereInput
  /**
   * Limit how many Users to update.
   */
  limit?: number
}

/**
 * User upsert
 */
export type UserUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * The filter to search for the User to update in case it exists.
   */
  where: Prisma.UserWhereUniqueInput
  /**
   * In case the User found by the `where` argument doesn't exist, create a new User with this data.
   */
  create: Prisma.XOR<Prisma.UserCreateInput, Prisma.UserUncheckedCreateInput>
  /**
   * In case the User was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.UserUpdateInput, Prisma.UserUncheckedUpdateInput>
}

/**
 * User delete
 */
export type UserDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * Filter which User to delete.
   */
  where: Prisma.UserWhereUniqueInput
}

/**
 * User deleteMany
 */
export type UserDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Users to delete
   */
  where?: Prisma.UserWhereInput
  /**
   * Limit how many Users to delete.
   */
  limit?: number
}

/**
 * User.sessions
 */
export type User$sessionsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Session
   */
  select?: Prisma.SessionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Session
   */
  omit?: Prisma.SessionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SessionInclude<ExtArgs> | null
  where?: Prisma.SessionWhereInput
  orderBy?: Prisma.SessionOrderByWithRelationInput | Prisma.SessionOrderByWithRelationInput[]
  cursor?: Prisma.SessionWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.SessionScalarFieldEnum | Prisma.SessionScalarFieldEnum[]
}

/**
 * User.accounts
 */
export type User$accountsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Account
   */
  select?: Prisma.AccountSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Account
   */
  omit?: Prisma.AccountOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AccountInclude<ExtArgs> | null
  where?: Prisma.AccountWhereInput
  orderBy?: Prisma.AccountOrderByWithRelationInput | Prisma.AccountOrderByWithRelationInput[]
  cursor?: Prisma.AccountWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.AccountScalarFieldEnum | Prisma.AccountScalarFieldEnum[]
}

/**
 * User.alumniProfile
 */
export type User$alumniProfileArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AlumniProfile
   */
  select?: Prisma.AlumniProfileSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AlumniProfile
   */
  omit?: Prisma.AlumniProfileOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AlumniProfileInclude<ExtArgs> | null
  where?: Prisma.AlumniProfileWhereInput
}

/**
 * User.Member
 */
export type User$MemberArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Member
   */
  select?: Prisma.MemberSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Member
   */
  omit?: Prisma.MemberOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MemberInclude<ExtArgs> | null
  where?: Prisma.MemberWhereInput
  orderBy?: Prisma.MemberOrderByWithRelationInput | Prisma.MemberOrderByWithRelationInput[]
  cursor?: Prisma.MemberWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.MemberScalarFieldEnum | Prisma.MemberScalarFieldEnum[]
}

/**
 * User.Invitation
 */
export type User$InvitationArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Invitation
   */
  select?: Prisma.InvitationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Invitation
   */
  omit?: Prisma.InvitationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.InvitationInclude<ExtArgs> | null
  where?: Prisma.InvitationWhereInput
  orderBy?: Prisma.InvitationOrderByWithRelationInput | Prisma.InvitationOrderByWithRelationInput[]
  cursor?: Prisma.InvitationWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.InvitationScalarFieldEnum | Prisma.InvitationScalarFieldEnum[]
}

/**
 * User.TwoFactor
 */
export type User$TwoFactorArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TwoFactor
   */
  select?: Prisma.TwoFactorSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TwoFactor
   */
  omit?: Prisma.TwoFactorOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TwoFactorInclude<ExtArgs> | null
  where?: Prisma.TwoFactorWhereInput
  orderBy?: Prisma.TwoFactorOrderByWithRelationInput | Prisma.TwoFactorOrderByWithRelationInput[]
  cursor?: Prisma.TwoFactorWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.TwoFactorScalarFieldEnum | Prisma.TwoFactorScalarFieldEnum[]
}

/**
 * User.Passkey
 */
export type User$PasskeyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Passkey
   */
  select?: Prisma.PasskeySelect<ExtArgs> | null
  /**
   * Omit specific fields from the Passkey
   */
  omit?: Prisma.PasskeyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PasskeyInclude<ExtArgs> | null
  where?: Prisma.PasskeyWhereInput
  orderBy?: Prisma.PasskeyOrderByWithRelationInput | Prisma.PasskeyOrderByWithRelationInput[]
  cursor?: Prisma.PasskeyWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.PasskeyScalarFieldEnum | Prisma.PasskeyScalarFieldEnum[]
}

/**
 * User without action
 */
export type UserDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
}
