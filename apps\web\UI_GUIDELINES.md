## PROTEC Web UI Guidelines (shadcn-first, Linear + Notion quality)

These rules ensure a consistent, minimal, high-quality UI. We strictly use shadcn/ui defaults and Tailwind theme tokens. No custom radii, no custom hex colors, and no inline styles for colors.

### 1) Core Principles
- **Minimal and intentional**: Fewer surfaces, more whitespace, clear hierarchy.
- **Token-only styling**: Always use theme tokens and component props before custom classes.
- **Consistency over creativity**: Prefer established patterns over bespoke UI per-page.
- **Clarity first**: Legibility, contrast, and focus states are non-negotiable.

### 2) Colors
- **Allowed**: Use Tailwind classes bound to shadcn theme tokens only:
  - Backgrounds: `bg-background`, `bg-card`, `bg-popover`, `bg-muted`, `bg-accent`, `bg-primary`, `bg-secondary`, `bg-destructive`
  - Text: `text-foreground`, `text-muted-foreground`, `text-primary`, `text-secondary`, `text-destructive`
  - Borders: `border`, `border-border`, `border-input`
  - Rings: `ring`, `ring-ring`
  - States/overlays: opacity utilities with tokens (e.g. `bg-primary/10`)
- **Not allowed**:
  - Custom hex/rgb/hsl colors (e.g. `bg-[#0f0]`, `text-[rgb(...)]`)
  - Arbitrary Tailwind colors not mapped to tokens (e.g. `text-zinc-700`)
  - Inline style color overrides
- **Gradients**: Allowed only with token colors (e.g. `from-primary/10 to-secondary/10`).

### 3) Radius
- **Use component-provided radius** and utilities that map to the design token: `rounded`, `rounded-sm`, `rounded-md`, `rounded-lg`, `rounded-xl`.
- **Do NOT** use arbitrary radii (e.g. `rounded-[6px]`).
- Never override radius via inline styles.

### 4) Typography
- Use app fonts configured in `layout.tsx`.
- Headings: semantic tags (`h1`..`h4`), with weight `font-semibold` or `font-medium`.
- Body and meta: `text-sm`/`text-base`; muted copy uses `text-muted-foreground`.
- Avoid custom tracking/leading unless absolutely necessary; use defaults or small utilities (`leading-tight`) consistently.

### 5) Spacing & Layout
- Page container: `container mx-auto px-4 py-4` (desktop) unless a different pattern is required.
- Use Tailwind spacing scale only; no arbitrary pixel values.
- Use consistent section spacing: `space-y-6` between blocks; internal spacing `gap-2`/`gap-3`.
- Keep content widths readable; avoid full-bleed text.

### 6) Icons
- Use `lucide-react` only.
- Default sizes: 16–20px (`size-4` to `size-5`).
- Coloring: `text-muted-foreground` for default, semantic tokens for meaning (`text-primary`, `text-destructive`, etc.). No custom colors.
- Place icons to the left of labels with `gap-2` to `gap-3`.

### 7) Components (shadcn/ui)
- **Buttons**: Use `variant` and `size` props instead of custom classes. Variants: `default`, `secondary`, `outline`, `ghost`, `link`.
- **Inputs/Forms**: Use `Form`, `Input`, `Label`, `Textarea`, `Select`, `Checkbox`, `Switch`. Show validation and help text with `text-muted-foreground`. Error states should use destructive tokens.
- **Cards**: Use sparingly. Prefer simple containers with `border` + spacing. When used, keep content minimal and avoid nested cards.
- **Tables/Lists**: Prefer simple lists with dividers. Use `Table` for structured data only.
- **Badges**: Use for small status indicators or “new/soon” labels. Keep contrast readable.
- **Navigation**: Use provided `Sidebar` + `Breadcrumb`. Active states come from existing menu logic; avoid custom overrides.

### 8) Borders, Shadows, and Dividers
- Borders: `border` with token-controlled color. Avoid thick borders.
- Dividers: `Separator` with subtle usage; don’t over-separate.
- Shadows: Keep subtle; prefer component defaults. Avoid heavy or custom shadows.

### 9) States & Feedback
- Loading: Use `Skeleton` for content and `LoadingSpinner` for inline/loading button states.
- Empty State: Minimal illustration via icons, short message in `text-muted-foreground`, and a primary action (if applicable).
- Errors: Use `destructive` tokens; message should be concise and actionable.
- Success: Subtle confirmation using `text-primary` or a success badge; avoid toasts for trivial success.

### 10) Interactions & Motion
- Keep transitions subtle and fast (150–200ms). Use component defaults.
- Hover/Active states should rely on token-driven classes (e.g., `hover:bg-accent`).
- Avoid excessive animations and custom keyframes.

### 11) Dark Mode
- Always test light/dark. Rely on tokens to maintain contrast.
- Avoid hardcoding opacities that reduce contrast too far in dark mode.

### 12) Page Structure Pattern
- Header bar: title + optional actions; use Breadcrumb where context spans multiple levels.
- Content sections: simple grouped blocks with `space-y-6`.
- Example scaffold:

```tsx
export default function Page() {
  return (
    <div className="container mx-auto px-4 py-4 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold">Page Title</h1>
        <div className="flex items-center gap-2">{/* actions */}</div>
      </div>
      <section className="rounded-lg border p-4 bg-card">
        <h2 className="text-sm font-medium mb-2 text-muted-foreground">Section</h2>
        {/* content */}
      </section>
    </div>
  );
}
```

### 13) Do / Don’t
- Do: `bg-card`, `text-muted-foreground`, `rounded-lg`, `border`
- Don’t: `bg-[#fafafa]`, `text-[#222]`, `rounded-[10px]`, inline style overrides
- Do: `text-primary` for emphasized interactive content
- Don’t: random `text-blue-500`
- Do: `bg-primary/10` for subtle emphasis backgrounds
- Don’t: `bg-gradient-to-r from-[#123] to-[#456]`

### 14) Accessibility
- Ensure all interactive elements have visible focus states (tokens supply this).
- Use semantic HTML and aria-attributes for complex widgets.
- Maintain sufficient contrast per WCAG; tokens are tuned for this—don’t override.

### 15) Review Checklist (per PR)
- Colors use tokens only; no custom color utilities or inline styles
- Radius uses standard utilities; no arbitrary radii
- Components use `variant`/`size` props, not custom overrides
- Layout uses standard container and spacing scale
- Icons sized 16–20px, colored via tokens only
- States (loading/empty/error) implemented where applicable
- Light/dark visually verified

### 16) Naming & Structure
- Place page content within a `container mx-auto px-4 py-4` wrapper.
- Keep component-specific styles co-located; avoid global overrides.
- Prefer composing existing primitives over new custom components.

### 17) Applying to PROTEC
- Sidebar + App Bar pattern is standard across app pages.
- Avoid overusing cards on dashboard; prefer clean sections with subtle borders.
- Use icons as signposts for navigation and quick actions; color via tokens only.

