import type { Context as HonoContext } from "hono";
import type {
  User,
  Organization,
  Member,
  AlumniProfile,
} from "../../prisma/generated/client";
import { auth } from "./auth";
import { prisma } from "./database";

export type CreateContextOptions = {
  context: HonoContext;
};

export async function createContext({ context }: CreateContextOptions) {
  const session = await auth.api.getSession({
    headers: context.req.raw.headers,
  });

  return {
    session,
    user: session?.user || null,
  };
}

export type Context = Awaited<ReturnType<typeof createContext>>;

/**
 * Enhanced context with organization and member information
 */
export interface EnhancedContext extends Context {
  organization?: Organization;
  member?: Member;
  alumniProfile?: AlumniProfile;
}

/**
 * Get organization context for a user
 */
export async function getOrganizationContext(
  userId: string,
  organizationId?: string
): Promise<{
  organization?: Organization;
  member?: Member;
  alumniProfile?: AlumniProfile;
}> {
  try {
    // Get user's alumni profile
    const alumniProfile = await prisma.alumniProfile.findUnique({
      where: { userId },
      include: {
        primaryOrganization: true,
      },
    });

    // If specific organization requested, get that membership
    if (organizationId) {
      const member = await prisma.member.findFirst({
        where: {
          userId,
          organizationId,
        },
        include: {
          organization: true,
        },
      });

      return {
        organization: member?.organization,
        member,
        alumniProfile,
      };
    }

    // Otherwise, use primary organization
    if (alumniProfile?.primaryOrganization) {
      const member = await prisma.member.findFirst({
        where: {
          userId,
          organizationId: alumniProfile.primaryOrganization.id,
        },
      });

      return {
        organization: alumniProfile.primaryOrganization,
        member,
        alumniProfile,
      };
    }

    return { alumniProfile };
  } catch (error) {
    console.error("Error getting organization context:", error);
    return {};
  }
}
