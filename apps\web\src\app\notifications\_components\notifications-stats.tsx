"use client"

import { useQuery } from "@tanstack/react-query"
import { orpc } from "@/utils/orpc"

export default function NotificationsStats() {
  const stats = useQuery(orpc.notifications.stats.queryOptions({ input: {} as any }))
  const unread = useQuery(orpc.notifications.unreadCount.queryOptions({ input: {} as any }))

  return (
    <div className="rounded-lg border bg-card p-4">
      <div className="grid gap-4 md:grid-cols-3">
        <div>
          <div className="text-2xl font-semibold">{(stats.data as any)?.total ?? 0}</div>
          <div className="text-sm text-muted-foreground">Total</div>
        </div>
        <div>
          <div className="text-2xl font-semibold">{(stats.data as any)?.unread ?? 0}</div>
          <div className="text-sm text-muted-foreground">Unread (stats)</div>
        </div>
        <div>
          <div className="text-2xl font-semibold">{(unread.data as any)?.count ?? 0}</div>
          <div className="text-sm text-muted-foreground">Unread (count)</div>
        </div>
      </div>
    </div>
  )
}


