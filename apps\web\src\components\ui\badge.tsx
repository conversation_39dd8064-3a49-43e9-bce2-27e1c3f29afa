import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@/lib/utils";

const badgeVariants = cva(
  "inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90 dark:bg-primary/80 dark:text-primary-foreground dark:[a&]:hover:bg-primary/70",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90 dark:bg-secondary/80 dark:text-secondary-foreground dark:[a&]:hover:bg-secondary/70",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/80 dark:text-destructive-foreground dark:[a&]:hover:bg-destructive/70",
        success:
          "border-transparent bg-success text-success-foreground [a&]:hover:bg-success/90 dark:bg-success/80 dark:text-success-foreground dark:[a&]:hover:bg-success/70",
        warning:
          "border-transparent bg-warning text-warning-foreground [a&]:hover:bg-warning/90 dark:bg-warning/80 dark:text-warning-foreground dark:[a&]:hover:bg-warning/70",
        info: "border-transparent bg-info text-info-foreground [a&]:hover:bg-info/90 dark:bg-info/80 dark:text-info-foreground dark:[a&]:hover:bg-info/70",
        outline:
          "text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground dark:text-foreground dark:[a&]:hover:bg-accent/80 dark:[a&]:hover:text-accent-foreground",
        ghost:
          "border-transparent bg-background/40 text-foreground hover:bg-accent/50 dark:bg-background/40 dark:text-foreground dark:hover:bg-accent/30",
        dark: "border-transparent bg-zinc-900 text-zinc-50 [a&]:hover:bg-zinc-900/90 dark:bg-zinc-800 dark:text-zinc-50 dark:[a&]:hover:bg-zinc-800/90",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);

function Badge({
  className,
  variant,
  asChild = false,
  ...props
}: React.ComponentProps<"span"> &
  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {
  const Comp = asChild ? Slot : "span";

  return (
    <Comp
      data-slot="badge"
      className={cn(badgeVariants({ variant }), className)}
      {...props}
    />
  );
}

export { Badge, badgeVariants };
