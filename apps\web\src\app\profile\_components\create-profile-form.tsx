"use client"

import { useState, useMemo } from "react"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import { client } from "@/utils/orpc"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { useForm } from "react-hook-form"
import type { z } from "zod"
import { CalendarIcon, GraduationCap, Landmark, UserRound, Briefcase, MapPin, Linkedin, Phone, Binary } from "lucide-react"

type CreateAlumniProfileInput = {
  firstName: string
  lastName: string
  displayName?: string
  bio?: string
  profilePicture?: string
  graduationYear: number
  programType: string
  centerLocation: string
  achievements?: string
  currentPosition?: string
  currentCompany?: string
  industry?: string
  location?: string
  linkedInUrl?: string
  phoneNumber?: string
  mentorshipOffered?: boolean
  mentorshipSought?: boolean
  skillsOffered?: string
  skillsWanted?: string
  primaryOrganizationId?: string
}

export function CreateProfileForm() {
  const queryClient = useQueryClient()
  const [submitting, setSubmitting] = useState(false)

  const form = useForm<CreateAlumniProfileInput>({
    defaultValues: {
      firstName: "",
      lastName: "",
      displayName: "",
      bio: "",
      graduationYear: new Date().getFullYear(),
      programType: "",
      centerLocation: "",
      achievements: "",
      currentPosition: "",
      currentCompany: "",
      industry: "",
      location: "",
      linkedInUrl: "",
      phoneNumber: "",
      mentorshipOffered: false,
      mentorshipSought: false,
      skillsOffered: "",
      skillsWanted: "",
    },
  })

  const years = useMemo(() => {
    const current = new Date().getFullYear()
    const list: number[] = []
    for (let y = current; y >= 1980; y--) list.push(y)
    return list
  }, [])

  const createMutation = useMutation({
    mutationFn: async (data: CreateAlumniProfileInput) => client.profiles.create(data),
    onSuccess: async () => {
      await Promise.all([
        queryClient.invalidateQueries(),
      ])
    },
  })

  async function onSubmit(values: CreateAlumniProfileInput) {
    setSubmitting(true)
    try {
      await createMutation.mutateAsync({
        ...values,
        graduationYear: Number(values.graduationYear),
      })
    } finally {
      setSubmitting(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Create your alumni profile</h2>
      </div>
      <div className="rounded-lg border bg-card p-4">
        <Form {...form}>
          <form className="space-y-6" onSubmit={form.handleSubmit(onSubmit)}>
            <section className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div className="col-span-1 md:col-span-1 space-y-4">
                <div className="flex items-center gap-2 text-muted-foreground"><UserRound className="size-4" /><span className="text-sm font-medium">Basic Information</span></div>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <FormField name="firstName" control={form.control} render={({ field }) => (
                    <FormItem>
                      <FormLabel>First name</FormLabel>
                      <FormControl><Input placeholder="Thandi" {...field} /></FormControl>
                      <FormMessage />
                    </FormItem>
                  )} />
                  <FormField name="lastName" control={form.control} render={({ field }) => (
                    <FormItem>
                      <FormLabel>Last name</FormLabel>
                      <FormControl><Input placeholder="Nkosi" {...field} /></FormControl>
                      <FormMessage />
                    </FormItem>
                  )} />
                </div>
                <FormField name="displayName" control={form.control} render={({ field }) => (
                  <FormItem>
                    <FormLabel>Display name</FormLabel>
                    <FormControl><Input placeholder="Preferred name (optional)" {...field} /></FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
                <FormField name="bio" control={form.control} render={({ field }) => (
                  <FormItem>
                    <FormLabel>Bio</FormLabel>
                    <FormControl><Textarea placeholder="Tell us about yourself" className="min-h-24" {...field} /></FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
              </div>

              <div className="col-span-1 md:col-span-1 space-y-4">
                <div className="flex items-center gap-2 text-muted-foreground"><GraduationCap className="size-4" /><span className="text-sm font-medium">PROTEC History</span></div>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <FormField name="graduationYear" control={form.control} render={({ field }) => (
                    <FormItem>
                      <FormLabel>Graduation year</FormLabel>
                      <FormControl>
                        <Select onValueChange={(val) => field.onChange(Number(val))} value={String(field.value)}>
                          <SelectTrigger><SelectValue placeholder="Select year" /></SelectTrigger>
                          <SelectContent>
                            {years.map((y) => (
                              <SelectItem key={y} value={String(y)}>{y}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )} />
                  <FormField name="programType" control={form.control} render={({ field }) => (
                    <FormItem>
                      <FormLabel>Program type</FormLabel>
                      <FormControl><Input placeholder="e.g. Engineering" {...field} /></FormControl>
                      <FormMessage />
                    </FormItem>
                  )} />
                </div>
                <FormField name="centerLocation" control={form.control} render={({ field }) => (
                  <FormItem>
                    <FormLabel>Center location</FormLabel>
                    <FormControl><Input placeholder="e.g. Soweto" {...field} /></FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
                <FormField name="achievements" control={form.control} render={({ field }) => (
                  <FormItem>
                    <FormLabel>Achievements</FormLabel>
                    <FormControl><Textarea placeholder="Key milestones (optional)" className="min-h-20" {...field} /></FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
              </div>
            </section>

            <Separator />

            <section className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div className="space-y-4">
                <div className="flex items-center gap-2 text-muted-foreground"><Briefcase className="size-4" /><span className="text-sm font-medium">Career</span></div>
                <FormField name="currentPosition" control={form.control} render={({ field }) => (
                  <FormItem>
                    <FormLabel>Current position</FormLabel>
                    <FormControl><Input placeholder="Software Engineer" {...field} /></FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <FormField name="currentCompany" control={form.control} render={({ field }) => (
                    <FormItem>
                      <FormLabel>Company</FormLabel>
                      <FormControl><Input placeholder="Company" {...field} /></FormControl>
                      <FormMessage />
                    </FormItem>
                  )} />
                  <FormField name="industry" control={form.control} render={({ field }) => (
                    <FormItem>
                      <FormLabel>Industry</FormLabel>
                      <FormControl><Input placeholder="Technology" {...field} /></FormControl>
                      <FormMessage />
                    </FormItem>
                  )} />
                </div>
                <FormField name="location" control={form.control} render={({ field }) => (
                  <FormItem>
                    <FormLabel>Location</FormLabel>
                    <FormControl><Input placeholder="Johannesburg" {...field} /></FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
              </div>

              <div className="space-y-4">
                <div className="flex items-center gap-2 text-muted-foreground"><Binary className="size-4" /><span className="text-sm font-medium">Mentorship & Contact</span></div>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <FormField name="linkedInUrl" control={form.control} render={({ field }) => (
                    <FormItem>
                      <FormLabel>LinkedIn URL</FormLabel>
                      <FormControl><Input placeholder="https://linkedin.com/in/..." {...field} /></FormControl>
                      <FormMessage />
                    </FormItem>
                  )} />
                  <FormField name="phoneNumber" control={form.control} render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone number</FormLabel>
                      <FormControl><Input placeholder="+27..." {...field} /></FormControl>
                      <FormMessage />
                    </FormItem>
                  )} />
                </div>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <FormField name="mentorshipOffered" control={form.control} render={({ field }) => (
                    <FormItem>
                      <FormLabel>Offering mentorship</FormLabel>
                      <FormControl>
                        <div className="flex items-center gap-3">
                          <Switch checked={field.value} onCheckedChange={field.onChange} />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )} />
                  <FormField name="mentorshipSought" control={form.control} render={({ field }) => (
                    <FormItem>
                      <FormLabel>Seeking mentorship</FormLabel>
                      <FormControl>
                        <div className="flex items-center gap-3">
                          <Switch checked={field.value} onCheckedChange={field.onChange} />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )} />
                </div>
                <FormField name="skillsOffered" control={form.control} render={({ field }) => (
                  <FormItem>
                    <FormLabel>Skills offered</FormLabel>
                    <FormControl><Textarea placeholder="Comma-separated skills" className="min-h-20" {...field} /></FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
                <FormField name="skillsWanted" control={form.control} render={({ field }) => (
                  <FormItem>
                    <FormLabel>Skills wanted</FormLabel>
                    <FormControl><Textarea placeholder="Comma-separated skills" className="min-h-20" {...field} /></FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
              </div>
            </section>

            <div className="flex items-center justify-end gap-2">
              <Button type="submit" disabled={submitting}>
                {submitting ? "Creating..." : "Create profile"}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  )
}

export default CreateProfileForm


