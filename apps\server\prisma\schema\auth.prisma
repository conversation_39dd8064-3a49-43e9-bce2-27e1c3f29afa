model User {
  id            String         @id
  name          String         @db.Text
  email         String
  emailVerified Boolean
  image         String?        @db.Text
  createdAt     DateTime
  updatedAt     DateTime
  sessions      Session[]
  accounts      Account[]
  alumniProfile AlumniProfile?
  Member        Member[]
  Invitation    Invitation[]
  TwoFactor     TwoFactor[]
  Passkey       Passkey[]

  @@unique([email])
  @@map("user")
}

model Session {
  id        String   @id
  expiresAt DateTime
  token     String
  createdAt DateTime
  updatedAt DateTime
  ipAddress String?  @db.Text
  userAgent String?  @db.Text
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([token])
  @@map("session")
}

model Account {
  id                    String    @id
  accountId             String    @db.Text
  providerId            String    @db.Text
  userId                String
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  accessToken           String?   @db.Text
  refreshToken          String?   @db.Text
  idToken               String?   @db.Text
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?   @db.Text
  password              String?   @db.Text
  createdAt             DateTime
  updatedAt             DateTime

  @@map("account")
}

model Verification {
  id         String    @id
  identifier String    @db.Text
  value      String    @db.Text
  expiresAt  DateTime
  createdAt  DateTime?
  updatedAt  DateTime?

  @@map("verification")
}

model Organization {
  id            String         @id @default(uuid())
  name          String         @db.Text
  slug          String?
  logo          String?        @db.Text
  createdAt     DateTime
  metadata      String?        @db.Text
  members       Member[]
  invitations   Invitation[]
  notifications Notification[]

  // Alumni platform relationships
  alumniProfiles AlumniProfile[]
  events         Event[]
  newsArticles   NewsArticle[]
  donations      Donation[]
  posts          Post[]

  @@unique([slug])
  @@map("organization")
}

model Member {
  id             String       @id @default(uuid())
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  userId         String
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  role           String       @db.Text
  createdAt      DateTime     @default(now())

  // Events organized by this member
  organizedEvents Event[]

  @@map("member")
}

model Invitation {
  id             String       @id @default(uuid())
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  email          String       @db.Text
  role           String?      @db.Text
  status         String       @db.Text
  expiresAt      DateTime
  inviterId      String
  user           User         @relation(fields: [inviterId], references: [id], onDelete: Cascade)

  @@map("invitation")
}

model TwoFactor {
  id          String @id @default(uuid())
  secret      String @db.Text
  backupCodes String @db.Text
  userId      String
  user        User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("twoFactor")
}

model Passkey {
  id           String    @id @default(uuid())
  name         String?   @db.Text
  publicKey    String    @db.Text
  userId       String
  user         User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  credentialID String    @db.Text
  counter      Int
  deviceType   String    @db.Text
  backedUp     Boolean
  transports   String?   @db.Text
  createdAt    DateTime? @default(now())

  @@map("passkey")
}
