import { z } from "zod";
import { protectedProcedure } from "../lib/orpc";
import { prisma } from "../lib/database";
import {
  connectionRequestSchema,
  connectionActionSchema,
  sendMessageSchema,
  markMessageReadSchema,
  idSchema,
} from "../lib/validation";
import { getPaginationParams, createPaginationResult } from "../lib/utils";

export const connectionsRouter = {
  /**
   * Get user's connections
   */
  list: protectedProcedure
    .input(
      z.object({
        page: z.number().int().min(1).default(1),
        limit: z.number().int().min(1).max(100).default(20),
        status: z
          .enum(["PENDING", "ACCEPTED", "DECLINED", "BLOCKED"])
          .optional(),
      })
    )
    .handler(async ({ input, context }) => {
      const { page, limit, skip } = getPaginationParams(input);
      const profile = await prisma.alumniProfile.findUnique({ where: { userId: context.user!.id }, select: { id: true } });
      if (!profile) throw new Error("Alumni profile not found");
      const where: any = {
        OR: [{ requesterId: profile.id }, { requestedId: profile.id }],
      };
      if (input.status) where.status = input.status;
      const [data, total] = await Promise.all([
        prisma.connection.findMany({ where, orderBy: { requestedAt: "desc" }, skip, take: limit }),
        prisma.connection.count({ where }),
      ]);
      return createPaginationResult(data, total, page, limit);
    }),

  /**
   * Send connection request
   */
  sendRequest: protectedProcedure
    .input(connectionRequestSchema)
    .handler(async ({ input, context }) => {
      const requester = await prisma.alumniProfile.findUnique({ where: { userId: context.user!.id }, select: { id: true } });
      if (!requester) throw new Error("Create your alumni profile first");
      return prisma.connection.create({
        data: { requesterId: requester.id, requestedId: input.requestedId, status: "PENDING" },
      });
    }),

  /**
   * Accept connection request
   */
  accept: protectedProcedure
    .input(connectionActionSchema)
    .handler(async ({ input, context }) => {
      const profile = await prisma.alumniProfile.findUnique({ where: { userId: context.user!.id }, select: { id: true } });
      if (!profile) throw new Error("Alumni profile not found");
      const connection = await prisma.connection.findUnique({ where: { id: input.connectionId } });
      if (!connection || connection.requestedId !== profile.id) throw new Error("Not allowed");
      return prisma.connection.update({ where: { id: input.connectionId }, data: { status: "ACCEPTED", acceptedAt: new Date() } });
    }),

  /**
   * Decline connection request
   */
  decline: protectedProcedure
    .input(connectionActionSchema)
    .handler(async ({ input, context }) => {
      const profile = await prisma.alumniProfile.findUnique({ where: { userId: context.user!.id }, select: { id: true } });
      if (!profile) throw new Error("Alumni profile not found");
      const connection = await prisma.connection.findUnique({ where: { id: input.connectionId } });
      if (!connection || connection.requestedId !== profile.id) throw new Error("Not allowed");
      return prisma.connection.update({ where: { id: input.connectionId }, data: { status: "DECLINED" } });
    }),

  /**
   * Block user
   */
  block: protectedProcedure
    .input(connectionActionSchema)
    .handler(async ({ input, context }) => {
      const profile = await prisma.alumniProfile.findUnique({ where: { userId: context.user!.id }, select: { id: true } });
      if (!profile) throw new Error("Alumni profile not found");
      const connection = await prisma.connection.findUnique({ where: { id: input.connectionId } });
      if (!connection || (connection.requestedId !== profile.id && connection.requesterId !== profile.id)) throw new Error("Not allowed");
      return prisma.connection.update({ where: { id: input.connectionId }, data: { status: "BLOCKED" } });
    }),

  /**
   * Remove connection
   */
  remove: protectedProcedure
    .input(connectionActionSchema)
    .handler(async ({ input, context }) => {
      const profile = await prisma.alumniProfile.findUnique({ where: { userId: context.user!.id }, select: { id: true } });
      if (!profile) throw new Error("Alumni profile not found");
      const connection = await prisma.connection.findUnique({ where: { id: input.connectionId } });
      if (!connection || (connection.requestedId !== profile.id && connection.requesterId !== profile.id)) throw new Error("Not allowed");
      await prisma.connection.delete({ where: { id: input.connectionId } });
      return { success: true };
    }),

  /**
   * Get pending connection requests
   */
  requests: protectedProcedure
    .input(
      z.object({
        type: z.enum(["sent", "received", "all"]).default("all"),
        page: z.number().int().min(1).default(1),
        limit: z.number().int().min(1).max(100).default(20),
      })
    )
    .handler(async ({ input, context }) => {
      const { page, limit, skip } = getPaginationParams(input);
      const profile = await prisma.alumniProfile.findUnique({ where: { userId: context.user!.id }, select: { id: true } });
      if (!profile) throw new Error("Alumni profile not found");
      const baseWhere: any = { status: "PENDING" };
      if (input.type === "sent") baseWhere.requesterId = profile.id;
      else if (input.type === "received") baseWhere.requestedId = profile.id;
      else baseWhere.OR = [{ requesterId: profile.id }, { requestedId: profile.id }];
      const [data, total] = await Promise.all([
        prisma.connection.findMany({ where: baseWhere, orderBy: { requestedAt: "desc" }, skip, take: limit }),
        prisma.connection.count({ where: baseWhere }),
      ]);
      return createPaginationResult(data, total, page, limit);
    }),

  /**
   * Get connection status with another user
   */
  status: protectedProcedure
    .input(z.object({ userId: idSchema }))
    .handler(async ({ input, context }) => {
      const me = await prisma.alumniProfile.findUnique({ where: { userId: context.user!.id }, select: { id: true } });
      if (!me) throw new Error("Alumni profile not found");
      const other = await prisma.alumniProfile.findUnique({ where: { userId: input.userId }, select: { id: true } });
      if (!other) return { status: "NONE" } as any;
      const conn = await prisma.connection.findFirst({
        where: {
          OR: [
            { requesterId: me.id, requestedId: other.id },
            { requesterId: other.id, requestedId: me.id },
          ],
        },
      });
      return { status: conn?.status || "NONE" } as any;
    }),

  /**
   * Get connection statistics
   */
  stats: protectedProcedure.handler(async ({ context }) => {
    const profile = await prisma.alumniProfile.findUnique({ where: { userId: context.user!.id }, select: { id: true } });
    if (!profile) throw new Error("Alumni profile not found");
    const [total, pending, blocked] = await Promise.all([
      prisma.connection.count({ where: { OR: [{ requesterId: profile.id }, { requestedId: profile.id }], status: "ACCEPTED" } }),
      prisma.connection.count({ where: { requestedId: profile.id, status: "PENDING" } }),
      prisma.connection.count({ where: { OR: [{ requesterId: profile.id }, { requestedId: profile.id }], status: "BLOCKED" } }),
    ]);
    return { total, pending, blocked };
  }),

  // Messaging endpoints
  messages: {
    /**
     * Get conversations list
     */
    conversations: protectedProcedure
      .input(
        z.object({
          page: z.number().int().min(1).default(1),
          limit: z.number().int().min(1).max(100).default(20),
        })
      )
      .handler(async ({ input, context }) => {
        const { page, limit, skip } = getPaginationParams(input);
        const me = await prisma.alumniProfile.findUnique({ where: { userId: context.user!.id }, select: { id: true } });
        if (!me) throw new Error("Alumni profile not found");
        const conversations = await prisma.$queryRaw<any[]>`
          SELECT other.id as userId, COUNT(m.id) as count
          FROM message m
          JOIN alumni_profile me ON (m.senderId = me.id OR m.receiverId = me.id)
          JOIN alumni_profile other ON (CASE WHEN m.senderId = me.id THEN m.receiverId ELSE m.senderId END = other.id)
          WHERE me.userId = ${context.user!.id}
          GROUP BY other.id
          ORDER BY MAX(m.createdAt) DESC
          LIMIT ${limit} OFFSET ${skip}
        `;
        const total = conversations.length; // Approx for mock
        return createPaginationResult(conversations, total, page, limit);
      }),

    /**
     * Get messages with a specific connection
     */
    getWithUser: protectedProcedure
      .input(
        z.object({
          userId: idSchema,
          page: z.number().int().min(1).default(1),
          limit: z.number().int().min(1).max(100).default(20),
        })
      )
      .handler(async ({ input, context }) => {
        const { page, limit, skip } = getPaginationParams(input);
        const me = await prisma.alumniProfile.findUnique({ where: { userId: context.user!.id }, select: { id: true } });
        const other = await prisma.alumniProfile.findUnique({ where: { userId: input.userId }, select: { id: true } });
        if (!me || !other) throw new Error("Profiles not found");
        const where = {
          OR: [
            { senderId: me.id, receiverId: other.id },
            { senderId: other.id, receiverId: me.id },
          ],
        };
        const [data, total] = await Promise.all([
          prisma.message.findMany({ where, orderBy: { createdAt: "desc" }, skip, take: limit }),
          prisma.message.count({ where }),
        ]);
        return createPaginationResult(data, total, page, limit);
      }),

    /**
     * Send message to connection
     */
    send: protectedProcedure
      .input(sendMessageSchema)
      .handler(async ({ input, context }) => {
        const sender = await prisma.alumniProfile.findUnique({ where: { userId: context.user!.id }, select: { id: true } });
        if (!sender) throw new Error("Create your alumni profile first");
        return prisma.message.create({
          data: { senderId: sender.id, receiverId: input.receiverId, content: input.content },
        });
      }),

    /**
     * Mark message as read
     */
    markRead: protectedProcedure
      .input(markMessageReadSchema)
      .handler(async ({ input, context }) => {
        const me = await prisma.alumniProfile.findUnique({ where: { userId: context.user!.id }, select: { id: true } });
        const msg = await prisma.message.findUnique({ where: { id: input.messageId } });
        if (!me || !msg || msg.receiverId !== me.id) throw new Error("Not allowed");
        return prisma.message.update({ where: { id: input.messageId }, data: { isRead: true } });
      }),

    /**
     * Mark all messages from a user as read
     */
    markAllRead: protectedProcedure
      .input(z.object({ fromUserId: idSchema }))
      .handler(async ({ input, context }) => {
        const me = await prisma.alumniProfile.findUnique({ where: { userId: context.user!.id }, select: { id: true } });
        const other = await prisma.alumniProfile.findUnique({ where: { userId: input.fromUserId }, select: { id: true } });
        if (!me || !other) throw new Error("Profiles not found");
        await prisma.message.updateMany({
          where: { senderId: other.id, receiverId: me.id, isRead: false },
          data: { isRead: true },
        });
        return { success: true };
      }),

    /**
     * Get unread message count
     */
    unreadCount: protectedProcedure.handler(async ({ context }) => {
      const me = await prisma.alumniProfile.findUnique({ where: { userId: context.user!.id }, select: { id: true } });
      if (!me) throw new Error("Alumni profile not found");
      const count = await prisma.message.count({ where: { receiverId: me.id, isRead: false } });
      return { count } as any;
    }),

    /**
     * Delete message
     */
    delete: protectedProcedure
      .input(z.object({ messageId: idSchema }))
      .handler(async ({ input, context }) => {
        const me = await prisma.alumniProfile.findUnique({ where: { userId: context.user!.id }, select: { id: true } });
        const msg = await prisma.message.findUnique({ where: { id: input.messageId } });
        if (!me || !msg || (msg.senderId !== me.id && msg.receiverId !== me.id)) throw new Error("Not allowed");
        await prisma.message.delete({ where: { id: input.messageId } });
        return { success: true };
      }),

    /**
     * Get message statistics
     */
    stats: protectedProcedure.handler(async ({ context }) => {
      const me = await prisma.alumniProfile.findUnique({ where: { userId: context.user!.id }, select: { id: true } });
      if (!me) throw new Error("Alumni profile not found");
      const [sent, received, unread] = await Promise.all([
        prisma.message.count({ where: { senderId: me.id } }),
        prisma.message.count({ where: { receiverId: me.id } }),
        prisma.message.count({ where: { receiverId: me.id, isRead: false } }),
      ]);
      return { sent, received, unread };
    }),
  },
};
