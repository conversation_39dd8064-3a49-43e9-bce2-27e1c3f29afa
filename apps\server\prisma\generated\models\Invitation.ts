
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `Invitation` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model Invitation
 * 
 */
export type InvitationModel = runtime.Types.Result.DefaultSelection<Prisma.$InvitationPayload>

export type AggregateInvitation = {
  _count: InvitationCountAggregateOutputType | null
  _min: InvitationMinAggregateOutputType | null
  _max: InvitationMaxAggregateOutputType | null
}

export type InvitationMinAggregateOutputType = {
  id: string | null
  organizationId: string | null
  email: string | null
  role: string | null
  status: string | null
  expiresAt: Date | null
  inviterId: string | null
}

export type InvitationMaxAggregateOutputType = {
  id: string | null
  organizationId: string | null
  email: string | null
  role: string | null
  status: string | null
  expiresAt: Date | null
  inviterId: string | null
}

export type InvitationCountAggregateOutputType = {
  id: number
  organizationId: number
  email: number
  role: number
  status: number
  expiresAt: number
  inviterId: number
  _all: number
}


export type InvitationMinAggregateInputType = {
  id?: true
  organizationId?: true
  email?: true
  role?: true
  status?: true
  expiresAt?: true
  inviterId?: true
}

export type InvitationMaxAggregateInputType = {
  id?: true
  organizationId?: true
  email?: true
  role?: true
  status?: true
  expiresAt?: true
  inviterId?: true
}

export type InvitationCountAggregateInputType = {
  id?: true
  organizationId?: true
  email?: true
  role?: true
  status?: true
  expiresAt?: true
  inviterId?: true
  _all?: true
}

export type InvitationAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Invitation to aggregate.
   */
  where?: Prisma.InvitationWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Invitations to fetch.
   */
  orderBy?: Prisma.InvitationOrderByWithRelationInput | Prisma.InvitationOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.InvitationWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Invitations from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Invitations.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Invitations
  **/
  _count?: true | InvitationCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: InvitationMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: InvitationMaxAggregateInputType
}

export type GetInvitationAggregateType<T extends InvitationAggregateArgs> = {
      [P in keyof T & keyof AggregateInvitation]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateInvitation[P]>
    : Prisma.GetScalarType<T[P], AggregateInvitation[P]>
}




export type InvitationGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.InvitationWhereInput
  orderBy?: Prisma.InvitationOrderByWithAggregationInput | Prisma.InvitationOrderByWithAggregationInput[]
  by: Prisma.InvitationScalarFieldEnum[] | Prisma.InvitationScalarFieldEnum
  having?: Prisma.InvitationScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: InvitationCountAggregateInputType | true
  _min?: InvitationMinAggregateInputType
  _max?: InvitationMaxAggregateInputType
}

export type InvitationGroupByOutputType = {
  id: string
  organizationId: string
  email: string
  role: string | null
  status: string
  expiresAt: Date
  inviterId: string
  _count: InvitationCountAggregateOutputType | null
  _min: InvitationMinAggregateOutputType | null
  _max: InvitationMaxAggregateOutputType | null
}

type GetInvitationGroupByPayload<T extends InvitationGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<InvitationGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof InvitationGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], InvitationGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], InvitationGroupByOutputType[P]>
      }
    >
  >



export type InvitationWhereInput = {
  AND?: Prisma.InvitationWhereInput | Prisma.InvitationWhereInput[]
  OR?: Prisma.InvitationWhereInput[]
  NOT?: Prisma.InvitationWhereInput | Prisma.InvitationWhereInput[]
  id?: Prisma.StringFilter<"Invitation"> | string
  organizationId?: Prisma.StringFilter<"Invitation"> | string
  email?: Prisma.StringFilter<"Invitation"> | string
  role?: Prisma.StringNullableFilter<"Invitation"> | string | null
  status?: Prisma.StringFilter<"Invitation"> | string
  expiresAt?: Prisma.DateTimeFilter<"Invitation"> | Date | string
  inviterId?: Prisma.StringFilter<"Invitation"> | string
  organization?: Prisma.XOR<Prisma.OrganizationScalarRelationFilter, Prisma.OrganizationWhereInput>
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
}

export type InvitationOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  organizationId?: Prisma.SortOrder
  email?: Prisma.SortOrder
  role?: Prisma.SortOrderInput | Prisma.SortOrder
  status?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
  inviterId?: Prisma.SortOrder
  organization?: Prisma.OrganizationOrderByWithRelationInput
  user?: Prisma.UserOrderByWithRelationInput
  _relevance?: Prisma.InvitationOrderByRelevanceInput
}

export type InvitationWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  AND?: Prisma.InvitationWhereInput | Prisma.InvitationWhereInput[]
  OR?: Prisma.InvitationWhereInput[]
  NOT?: Prisma.InvitationWhereInput | Prisma.InvitationWhereInput[]
  organizationId?: Prisma.StringFilter<"Invitation"> | string
  email?: Prisma.StringFilter<"Invitation"> | string
  role?: Prisma.StringNullableFilter<"Invitation"> | string | null
  status?: Prisma.StringFilter<"Invitation"> | string
  expiresAt?: Prisma.DateTimeFilter<"Invitation"> | Date | string
  inviterId?: Prisma.StringFilter<"Invitation"> | string
  organization?: Prisma.XOR<Prisma.OrganizationScalarRelationFilter, Prisma.OrganizationWhereInput>
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
}, "id">

export type InvitationOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  organizationId?: Prisma.SortOrder
  email?: Prisma.SortOrder
  role?: Prisma.SortOrderInput | Prisma.SortOrder
  status?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
  inviterId?: Prisma.SortOrder
  _count?: Prisma.InvitationCountOrderByAggregateInput
  _max?: Prisma.InvitationMaxOrderByAggregateInput
  _min?: Prisma.InvitationMinOrderByAggregateInput
}

export type InvitationScalarWhereWithAggregatesInput = {
  AND?: Prisma.InvitationScalarWhereWithAggregatesInput | Prisma.InvitationScalarWhereWithAggregatesInput[]
  OR?: Prisma.InvitationScalarWhereWithAggregatesInput[]
  NOT?: Prisma.InvitationScalarWhereWithAggregatesInput | Prisma.InvitationScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"Invitation"> | string
  organizationId?: Prisma.StringWithAggregatesFilter<"Invitation"> | string
  email?: Prisma.StringWithAggregatesFilter<"Invitation"> | string
  role?: Prisma.StringNullableWithAggregatesFilter<"Invitation"> | string | null
  status?: Prisma.StringWithAggregatesFilter<"Invitation"> | string
  expiresAt?: Prisma.DateTimeWithAggregatesFilter<"Invitation"> | Date | string
  inviterId?: Prisma.StringWithAggregatesFilter<"Invitation"> | string
}

export type InvitationCreateInput = {
  id?: string
  email: string
  role?: string | null
  status: string
  expiresAt: Date | string
  organization: Prisma.OrganizationCreateNestedOneWithoutInvitationsInput
  user: Prisma.UserCreateNestedOneWithoutInvitationInput
}

export type InvitationUncheckedCreateInput = {
  id?: string
  organizationId: string
  email: string
  role?: string | null
  status: string
  expiresAt: Date | string
  inviterId: string
}

export type InvitationUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  status?: Prisma.StringFieldUpdateOperationsInput | string
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  organization?: Prisma.OrganizationUpdateOneRequiredWithoutInvitationsNestedInput
  user?: Prisma.UserUpdateOneRequiredWithoutInvitationNestedInput
}

export type InvitationUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  organizationId?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  status?: Prisma.StringFieldUpdateOperationsInput | string
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  inviterId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type InvitationCreateManyInput = {
  id?: string
  organizationId: string
  email: string
  role?: string | null
  status: string
  expiresAt: Date | string
  inviterId: string
}

export type InvitationUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  status?: Prisma.StringFieldUpdateOperationsInput | string
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type InvitationUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  organizationId?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  status?: Prisma.StringFieldUpdateOperationsInput | string
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  inviterId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type InvitationListRelationFilter = {
  every?: Prisma.InvitationWhereInput
  some?: Prisma.InvitationWhereInput
  none?: Prisma.InvitationWhereInput
}

export type InvitationOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type InvitationOrderByRelevanceInput = {
  fields: Prisma.InvitationOrderByRelevanceFieldEnum | Prisma.InvitationOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type InvitationCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  organizationId?: Prisma.SortOrder
  email?: Prisma.SortOrder
  role?: Prisma.SortOrder
  status?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
  inviterId?: Prisma.SortOrder
}

export type InvitationMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  organizationId?: Prisma.SortOrder
  email?: Prisma.SortOrder
  role?: Prisma.SortOrder
  status?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
  inviterId?: Prisma.SortOrder
}

export type InvitationMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  organizationId?: Prisma.SortOrder
  email?: Prisma.SortOrder
  role?: Prisma.SortOrder
  status?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
  inviterId?: Prisma.SortOrder
}

export type InvitationCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.InvitationCreateWithoutUserInput, Prisma.InvitationUncheckedCreateWithoutUserInput> | Prisma.InvitationCreateWithoutUserInput[] | Prisma.InvitationUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.InvitationCreateOrConnectWithoutUserInput | Prisma.InvitationCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.InvitationCreateManyUserInputEnvelope
  connect?: Prisma.InvitationWhereUniqueInput | Prisma.InvitationWhereUniqueInput[]
}

export type InvitationUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.InvitationCreateWithoutUserInput, Prisma.InvitationUncheckedCreateWithoutUserInput> | Prisma.InvitationCreateWithoutUserInput[] | Prisma.InvitationUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.InvitationCreateOrConnectWithoutUserInput | Prisma.InvitationCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.InvitationCreateManyUserInputEnvelope
  connect?: Prisma.InvitationWhereUniqueInput | Prisma.InvitationWhereUniqueInput[]
}

export type InvitationUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.InvitationCreateWithoutUserInput, Prisma.InvitationUncheckedCreateWithoutUserInput> | Prisma.InvitationCreateWithoutUserInput[] | Prisma.InvitationUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.InvitationCreateOrConnectWithoutUserInput | Prisma.InvitationCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.InvitationUpsertWithWhereUniqueWithoutUserInput | Prisma.InvitationUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.InvitationCreateManyUserInputEnvelope
  set?: Prisma.InvitationWhereUniqueInput | Prisma.InvitationWhereUniqueInput[]
  disconnect?: Prisma.InvitationWhereUniqueInput | Prisma.InvitationWhereUniqueInput[]
  delete?: Prisma.InvitationWhereUniqueInput | Prisma.InvitationWhereUniqueInput[]
  connect?: Prisma.InvitationWhereUniqueInput | Prisma.InvitationWhereUniqueInput[]
  update?: Prisma.InvitationUpdateWithWhereUniqueWithoutUserInput | Prisma.InvitationUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.InvitationUpdateManyWithWhereWithoutUserInput | Prisma.InvitationUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.InvitationScalarWhereInput | Prisma.InvitationScalarWhereInput[]
}

export type InvitationUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.InvitationCreateWithoutUserInput, Prisma.InvitationUncheckedCreateWithoutUserInput> | Prisma.InvitationCreateWithoutUserInput[] | Prisma.InvitationUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.InvitationCreateOrConnectWithoutUserInput | Prisma.InvitationCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.InvitationUpsertWithWhereUniqueWithoutUserInput | Prisma.InvitationUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.InvitationCreateManyUserInputEnvelope
  set?: Prisma.InvitationWhereUniqueInput | Prisma.InvitationWhereUniqueInput[]
  disconnect?: Prisma.InvitationWhereUniqueInput | Prisma.InvitationWhereUniqueInput[]
  delete?: Prisma.InvitationWhereUniqueInput | Prisma.InvitationWhereUniqueInput[]
  connect?: Prisma.InvitationWhereUniqueInput | Prisma.InvitationWhereUniqueInput[]
  update?: Prisma.InvitationUpdateWithWhereUniqueWithoutUserInput | Prisma.InvitationUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.InvitationUpdateManyWithWhereWithoutUserInput | Prisma.InvitationUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.InvitationScalarWhereInput | Prisma.InvitationScalarWhereInput[]
}

export type InvitationCreateNestedManyWithoutOrganizationInput = {
  create?: Prisma.XOR<Prisma.InvitationCreateWithoutOrganizationInput, Prisma.InvitationUncheckedCreateWithoutOrganizationInput> | Prisma.InvitationCreateWithoutOrganizationInput[] | Prisma.InvitationUncheckedCreateWithoutOrganizationInput[]
  connectOrCreate?: Prisma.InvitationCreateOrConnectWithoutOrganizationInput | Prisma.InvitationCreateOrConnectWithoutOrganizationInput[]
  createMany?: Prisma.InvitationCreateManyOrganizationInputEnvelope
  connect?: Prisma.InvitationWhereUniqueInput | Prisma.InvitationWhereUniqueInput[]
}

export type InvitationUncheckedCreateNestedManyWithoutOrganizationInput = {
  create?: Prisma.XOR<Prisma.InvitationCreateWithoutOrganizationInput, Prisma.InvitationUncheckedCreateWithoutOrganizationInput> | Prisma.InvitationCreateWithoutOrganizationInput[] | Prisma.InvitationUncheckedCreateWithoutOrganizationInput[]
  connectOrCreate?: Prisma.InvitationCreateOrConnectWithoutOrganizationInput | Prisma.InvitationCreateOrConnectWithoutOrganizationInput[]
  createMany?: Prisma.InvitationCreateManyOrganizationInputEnvelope
  connect?: Prisma.InvitationWhereUniqueInput | Prisma.InvitationWhereUniqueInput[]
}

export type InvitationUpdateManyWithoutOrganizationNestedInput = {
  create?: Prisma.XOR<Prisma.InvitationCreateWithoutOrganizationInput, Prisma.InvitationUncheckedCreateWithoutOrganizationInput> | Prisma.InvitationCreateWithoutOrganizationInput[] | Prisma.InvitationUncheckedCreateWithoutOrganizationInput[]
  connectOrCreate?: Prisma.InvitationCreateOrConnectWithoutOrganizationInput | Prisma.InvitationCreateOrConnectWithoutOrganizationInput[]
  upsert?: Prisma.InvitationUpsertWithWhereUniqueWithoutOrganizationInput | Prisma.InvitationUpsertWithWhereUniqueWithoutOrganizationInput[]
  createMany?: Prisma.InvitationCreateManyOrganizationInputEnvelope
  set?: Prisma.InvitationWhereUniqueInput | Prisma.InvitationWhereUniqueInput[]
  disconnect?: Prisma.InvitationWhereUniqueInput | Prisma.InvitationWhereUniqueInput[]
  delete?: Prisma.InvitationWhereUniqueInput | Prisma.InvitationWhereUniqueInput[]
  connect?: Prisma.InvitationWhereUniqueInput | Prisma.InvitationWhereUniqueInput[]
  update?: Prisma.InvitationUpdateWithWhereUniqueWithoutOrganizationInput | Prisma.InvitationUpdateWithWhereUniqueWithoutOrganizationInput[]
  updateMany?: Prisma.InvitationUpdateManyWithWhereWithoutOrganizationInput | Prisma.InvitationUpdateManyWithWhereWithoutOrganizationInput[]
  deleteMany?: Prisma.InvitationScalarWhereInput | Prisma.InvitationScalarWhereInput[]
}

export type InvitationUncheckedUpdateManyWithoutOrganizationNestedInput = {
  create?: Prisma.XOR<Prisma.InvitationCreateWithoutOrganizationInput, Prisma.InvitationUncheckedCreateWithoutOrganizationInput> | Prisma.InvitationCreateWithoutOrganizationInput[] | Prisma.InvitationUncheckedCreateWithoutOrganizationInput[]
  connectOrCreate?: Prisma.InvitationCreateOrConnectWithoutOrganizationInput | Prisma.InvitationCreateOrConnectWithoutOrganizationInput[]
  upsert?: Prisma.InvitationUpsertWithWhereUniqueWithoutOrganizationInput | Prisma.InvitationUpsertWithWhereUniqueWithoutOrganizationInput[]
  createMany?: Prisma.InvitationCreateManyOrganizationInputEnvelope
  set?: Prisma.InvitationWhereUniqueInput | Prisma.InvitationWhereUniqueInput[]
  disconnect?: Prisma.InvitationWhereUniqueInput | Prisma.InvitationWhereUniqueInput[]
  delete?: Prisma.InvitationWhereUniqueInput | Prisma.InvitationWhereUniqueInput[]
  connect?: Prisma.InvitationWhereUniqueInput | Prisma.InvitationWhereUniqueInput[]
  update?: Prisma.InvitationUpdateWithWhereUniqueWithoutOrganizationInput | Prisma.InvitationUpdateWithWhereUniqueWithoutOrganizationInput[]
  updateMany?: Prisma.InvitationUpdateManyWithWhereWithoutOrganizationInput | Prisma.InvitationUpdateManyWithWhereWithoutOrganizationInput[]
  deleteMany?: Prisma.InvitationScalarWhereInput | Prisma.InvitationScalarWhereInput[]
}

export type InvitationCreateWithoutUserInput = {
  id?: string
  email: string
  role?: string | null
  status: string
  expiresAt: Date | string
  organization: Prisma.OrganizationCreateNestedOneWithoutInvitationsInput
}

export type InvitationUncheckedCreateWithoutUserInput = {
  id?: string
  organizationId: string
  email: string
  role?: string | null
  status: string
  expiresAt: Date | string
}

export type InvitationCreateOrConnectWithoutUserInput = {
  where: Prisma.InvitationWhereUniqueInput
  create: Prisma.XOR<Prisma.InvitationCreateWithoutUserInput, Prisma.InvitationUncheckedCreateWithoutUserInput>
}

export type InvitationCreateManyUserInputEnvelope = {
  data: Prisma.InvitationCreateManyUserInput | Prisma.InvitationCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type InvitationUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.InvitationWhereUniqueInput
  update: Prisma.XOR<Prisma.InvitationUpdateWithoutUserInput, Prisma.InvitationUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.InvitationCreateWithoutUserInput, Prisma.InvitationUncheckedCreateWithoutUserInput>
}

export type InvitationUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.InvitationWhereUniqueInput
  data: Prisma.XOR<Prisma.InvitationUpdateWithoutUserInput, Prisma.InvitationUncheckedUpdateWithoutUserInput>
}

export type InvitationUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.InvitationScalarWhereInput
  data: Prisma.XOR<Prisma.InvitationUpdateManyMutationInput, Prisma.InvitationUncheckedUpdateManyWithoutUserInput>
}

export type InvitationScalarWhereInput = {
  AND?: Prisma.InvitationScalarWhereInput | Prisma.InvitationScalarWhereInput[]
  OR?: Prisma.InvitationScalarWhereInput[]
  NOT?: Prisma.InvitationScalarWhereInput | Prisma.InvitationScalarWhereInput[]
  id?: Prisma.StringFilter<"Invitation"> | string
  organizationId?: Prisma.StringFilter<"Invitation"> | string
  email?: Prisma.StringFilter<"Invitation"> | string
  role?: Prisma.StringNullableFilter<"Invitation"> | string | null
  status?: Prisma.StringFilter<"Invitation"> | string
  expiresAt?: Prisma.DateTimeFilter<"Invitation"> | Date | string
  inviterId?: Prisma.StringFilter<"Invitation"> | string
}

export type InvitationCreateWithoutOrganizationInput = {
  id?: string
  email: string
  role?: string | null
  status: string
  expiresAt: Date | string
  user: Prisma.UserCreateNestedOneWithoutInvitationInput
}

export type InvitationUncheckedCreateWithoutOrganizationInput = {
  id?: string
  email: string
  role?: string | null
  status: string
  expiresAt: Date | string
  inviterId: string
}

export type InvitationCreateOrConnectWithoutOrganizationInput = {
  where: Prisma.InvitationWhereUniqueInput
  create: Prisma.XOR<Prisma.InvitationCreateWithoutOrganizationInput, Prisma.InvitationUncheckedCreateWithoutOrganizationInput>
}

export type InvitationCreateManyOrganizationInputEnvelope = {
  data: Prisma.InvitationCreateManyOrganizationInput | Prisma.InvitationCreateManyOrganizationInput[]
  skipDuplicates?: boolean
}

export type InvitationUpsertWithWhereUniqueWithoutOrganizationInput = {
  where: Prisma.InvitationWhereUniqueInput
  update: Prisma.XOR<Prisma.InvitationUpdateWithoutOrganizationInput, Prisma.InvitationUncheckedUpdateWithoutOrganizationInput>
  create: Prisma.XOR<Prisma.InvitationCreateWithoutOrganizationInput, Prisma.InvitationUncheckedCreateWithoutOrganizationInput>
}

export type InvitationUpdateWithWhereUniqueWithoutOrganizationInput = {
  where: Prisma.InvitationWhereUniqueInput
  data: Prisma.XOR<Prisma.InvitationUpdateWithoutOrganizationInput, Prisma.InvitationUncheckedUpdateWithoutOrganizationInput>
}

export type InvitationUpdateManyWithWhereWithoutOrganizationInput = {
  where: Prisma.InvitationScalarWhereInput
  data: Prisma.XOR<Prisma.InvitationUpdateManyMutationInput, Prisma.InvitationUncheckedUpdateManyWithoutOrganizationInput>
}

export type InvitationCreateManyUserInput = {
  id?: string
  organizationId: string
  email: string
  role?: string | null
  status: string
  expiresAt: Date | string
}

export type InvitationUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  status?: Prisma.StringFieldUpdateOperationsInput | string
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  organization?: Prisma.OrganizationUpdateOneRequiredWithoutInvitationsNestedInput
}

export type InvitationUncheckedUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  organizationId?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  status?: Prisma.StringFieldUpdateOperationsInput | string
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type InvitationUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  organizationId?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  status?: Prisma.StringFieldUpdateOperationsInput | string
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type InvitationCreateManyOrganizationInput = {
  id?: string
  email: string
  role?: string | null
  status: string
  expiresAt: Date | string
  inviterId: string
}

export type InvitationUpdateWithoutOrganizationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  status?: Prisma.StringFieldUpdateOperationsInput | string
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.UserUpdateOneRequiredWithoutInvitationNestedInput
}

export type InvitationUncheckedUpdateWithoutOrganizationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  status?: Prisma.StringFieldUpdateOperationsInput | string
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  inviterId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type InvitationUncheckedUpdateManyWithoutOrganizationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  status?: Prisma.StringFieldUpdateOperationsInput | string
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  inviterId?: Prisma.StringFieldUpdateOperationsInput | string
}



export type InvitationSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  organizationId?: boolean
  email?: boolean
  role?: boolean
  status?: boolean
  expiresAt?: boolean
  inviterId?: boolean
  organization?: boolean | Prisma.OrganizationDefaultArgs<ExtArgs>
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}, ExtArgs["result"]["invitation"]>



export type InvitationSelectScalar = {
  id?: boolean
  organizationId?: boolean
  email?: boolean
  role?: boolean
  status?: boolean
  expiresAt?: boolean
  inviterId?: boolean
}

export type InvitationOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "organizationId" | "email" | "role" | "status" | "expiresAt" | "inviterId", ExtArgs["result"]["invitation"]>
export type InvitationInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  organization?: boolean | Prisma.OrganizationDefaultArgs<ExtArgs>
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}

export type $InvitationPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Invitation"
  objects: {
    organization: Prisma.$OrganizationPayload<ExtArgs>
    user: Prisma.$UserPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    organizationId: string
    email: string
    role: string | null
    status: string
    expiresAt: Date
    inviterId: string
  }, ExtArgs["result"]["invitation"]>
  composites: {}
}

export type InvitationGetPayload<S extends boolean | null | undefined | InvitationDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$InvitationPayload, S>

export type InvitationCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<InvitationFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: InvitationCountAggregateInputType | true
  }

export interface InvitationDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Invitation'], meta: { name: 'Invitation' } }
  /**
   * Find zero or one Invitation that matches the filter.
   * @param {InvitationFindUniqueArgs} args - Arguments to find a Invitation
   * @example
   * // Get one Invitation
   * const invitation = await prisma.invitation.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends InvitationFindUniqueArgs>(args: Prisma.SelectSubset<T, InvitationFindUniqueArgs<ExtArgs>>): Prisma.Prisma__InvitationClient<runtime.Types.Result.GetResult<Prisma.$InvitationPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Invitation that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {InvitationFindUniqueOrThrowArgs} args - Arguments to find a Invitation
   * @example
   * // Get one Invitation
   * const invitation = await prisma.invitation.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends InvitationFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, InvitationFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__InvitationClient<runtime.Types.Result.GetResult<Prisma.$InvitationPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Invitation that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {InvitationFindFirstArgs} args - Arguments to find a Invitation
   * @example
   * // Get one Invitation
   * const invitation = await prisma.invitation.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends InvitationFindFirstArgs>(args?: Prisma.SelectSubset<T, InvitationFindFirstArgs<ExtArgs>>): Prisma.Prisma__InvitationClient<runtime.Types.Result.GetResult<Prisma.$InvitationPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Invitation that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {InvitationFindFirstOrThrowArgs} args - Arguments to find a Invitation
   * @example
   * // Get one Invitation
   * const invitation = await prisma.invitation.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends InvitationFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, InvitationFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__InvitationClient<runtime.Types.Result.GetResult<Prisma.$InvitationPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Invitations that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {InvitationFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Invitations
   * const invitations = await prisma.invitation.findMany()
   * 
   * // Get first 10 Invitations
   * const invitations = await prisma.invitation.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const invitationWithIdOnly = await prisma.invitation.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends InvitationFindManyArgs>(args?: Prisma.SelectSubset<T, InvitationFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$InvitationPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Invitation.
   * @param {InvitationCreateArgs} args - Arguments to create a Invitation.
   * @example
   * // Create one Invitation
   * const Invitation = await prisma.invitation.create({
   *   data: {
   *     // ... data to create a Invitation
   *   }
   * })
   * 
   */
  create<T extends InvitationCreateArgs>(args: Prisma.SelectSubset<T, InvitationCreateArgs<ExtArgs>>): Prisma.Prisma__InvitationClient<runtime.Types.Result.GetResult<Prisma.$InvitationPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Invitations.
   * @param {InvitationCreateManyArgs} args - Arguments to create many Invitations.
   * @example
   * // Create many Invitations
   * const invitation = await prisma.invitation.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends InvitationCreateManyArgs>(args?: Prisma.SelectSubset<T, InvitationCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Invitation.
   * @param {InvitationDeleteArgs} args - Arguments to delete one Invitation.
   * @example
   * // Delete one Invitation
   * const Invitation = await prisma.invitation.delete({
   *   where: {
   *     // ... filter to delete one Invitation
   *   }
   * })
   * 
   */
  delete<T extends InvitationDeleteArgs>(args: Prisma.SelectSubset<T, InvitationDeleteArgs<ExtArgs>>): Prisma.Prisma__InvitationClient<runtime.Types.Result.GetResult<Prisma.$InvitationPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Invitation.
   * @param {InvitationUpdateArgs} args - Arguments to update one Invitation.
   * @example
   * // Update one Invitation
   * const invitation = await prisma.invitation.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends InvitationUpdateArgs>(args: Prisma.SelectSubset<T, InvitationUpdateArgs<ExtArgs>>): Prisma.Prisma__InvitationClient<runtime.Types.Result.GetResult<Prisma.$InvitationPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Invitations.
   * @param {InvitationDeleteManyArgs} args - Arguments to filter Invitations to delete.
   * @example
   * // Delete a few Invitations
   * const { count } = await prisma.invitation.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends InvitationDeleteManyArgs>(args?: Prisma.SelectSubset<T, InvitationDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Invitations.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {InvitationUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Invitations
   * const invitation = await prisma.invitation.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends InvitationUpdateManyArgs>(args: Prisma.SelectSubset<T, InvitationUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Invitation.
   * @param {InvitationUpsertArgs} args - Arguments to update or create a Invitation.
   * @example
   * // Update or create a Invitation
   * const invitation = await prisma.invitation.upsert({
   *   create: {
   *     // ... data to create a Invitation
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Invitation we want to update
   *   }
   * })
   */
  upsert<T extends InvitationUpsertArgs>(args: Prisma.SelectSubset<T, InvitationUpsertArgs<ExtArgs>>): Prisma.Prisma__InvitationClient<runtime.Types.Result.GetResult<Prisma.$InvitationPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Invitations.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {InvitationCountArgs} args - Arguments to filter Invitations to count.
   * @example
   * // Count the number of Invitations
   * const count = await prisma.invitation.count({
   *   where: {
   *     // ... the filter for the Invitations we want to count
   *   }
   * })
  **/
  count<T extends InvitationCountArgs>(
    args?: Prisma.Subset<T, InvitationCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], InvitationCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Invitation.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {InvitationAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends InvitationAggregateArgs>(args: Prisma.Subset<T, InvitationAggregateArgs>): Prisma.PrismaPromise<GetInvitationAggregateType<T>>

  /**
   * Group by Invitation.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {InvitationGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends InvitationGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: InvitationGroupByArgs['orderBy'] }
      : { orderBy?: InvitationGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, InvitationGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetInvitationGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Invitation model
 */
readonly fields: InvitationFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Invitation.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__InvitationClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  organization<T extends Prisma.OrganizationDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.OrganizationDefaultArgs<ExtArgs>>): Prisma.Prisma__OrganizationClient<runtime.Types.Result.GetResult<Prisma.$OrganizationPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  user<T extends Prisma.UserDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.UserDefaultArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Invitation model
 */
export interface InvitationFieldRefs {
  readonly id: Prisma.FieldRef<"Invitation", 'String'>
  readonly organizationId: Prisma.FieldRef<"Invitation", 'String'>
  readonly email: Prisma.FieldRef<"Invitation", 'String'>
  readonly role: Prisma.FieldRef<"Invitation", 'String'>
  readonly status: Prisma.FieldRef<"Invitation", 'String'>
  readonly expiresAt: Prisma.FieldRef<"Invitation", 'DateTime'>
  readonly inviterId: Prisma.FieldRef<"Invitation", 'String'>
}
    

// Custom InputTypes
/**
 * Invitation findUnique
 */
export type InvitationFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Invitation
   */
  select?: Prisma.InvitationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Invitation
   */
  omit?: Prisma.InvitationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.InvitationInclude<ExtArgs> | null
  /**
   * Filter, which Invitation to fetch.
   */
  where: Prisma.InvitationWhereUniqueInput
}

/**
 * Invitation findUniqueOrThrow
 */
export type InvitationFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Invitation
   */
  select?: Prisma.InvitationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Invitation
   */
  omit?: Prisma.InvitationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.InvitationInclude<ExtArgs> | null
  /**
   * Filter, which Invitation to fetch.
   */
  where: Prisma.InvitationWhereUniqueInput
}

/**
 * Invitation findFirst
 */
export type InvitationFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Invitation
   */
  select?: Prisma.InvitationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Invitation
   */
  omit?: Prisma.InvitationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.InvitationInclude<ExtArgs> | null
  /**
   * Filter, which Invitation to fetch.
   */
  where?: Prisma.InvitationWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Invitations to fetch.
   */
  orderBy?: Prisma.InvitationOrderByWithRelationInput | Prisma.InvitationOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Invitations.
   */
  cursor?: Prisma.InvitationWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Invitations from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Invitations.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Invitations.
   */
  distinct?: Prisma.InvitationScalarFieldEnum | Prisma.InvitationScalarFieldEnum[]
}

/**
 * Invitation findFirstOrThrow
 */
export type InvitationFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Invitation
   */
  select?: Prisma.InvitationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Invitation
   */
  omit?: Prisma.InvitationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.InvitationInclude<ExtArgs> | null
  /**
   * Filter, which Invitation to fetch.
   */
  where?: Prisma.InvitationWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Invitations to fetch.
   */
  orderBy?: Prisma.InvitationOrderByWithRelationInput | Prisma.InvitationOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Invitations.
   */
  cursor?: Prisma.InvitationWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Invitations from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Invitations.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Invitations.
   */
  distinct?: Prisma.InvitationScalarFieldEnum | Prisma.InvitationScalarFieldEnum[]
}

/**
 * Invitation findMany
 */
export type InvitationFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Invitation
   */
  select?: Prisma.InvitationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Invitation
   */
  omit?: Prisma.InvitationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.InvitationInclude<ExtArgs> | null
  /**
   * Filter, which Invitations to fetch.
   */
  where?: Prisma.InvitationWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Invitations to fetch.
   */
  orderBy?: Prisma.InvitationOrderByWithRelationInput | Prisma.InvitationOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Invitations.
   */
  cursor?: Prisma.InvitationWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Invitations from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Invitations.
   */
  skip?: number
  distinct?: Prisma.InvitationScalarFieldEnum | Prisma.InvitationScalarFieldEnum[]
}

/**
 * Invitation create
 */
export type InvitationCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Invitation
   */
  select?: Prisma.InvitationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Invitation
   */
  omit?: Prisma.InvitationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.InvitationInclude<ExtArgs> | null
  /**
   * The data needed to create a Invitation.
   */
  data: Prisma.XOR<Prisma.InvitationCreateInput, Prisma.InvitationUncheckedCreateInput>
}

/**
 * Invitation createMany
 */
export type InvitationCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Invitations.
   */
  data: Prisma.InvitationCreateManyInput | Prisma.InvitationCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Invitation update
 */
export type InvitationUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Invitation
   */
  select?: Prisma.InvitationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Invitation
   */
  omit?: Prisma.InvitationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.InvitationInclude<ExtArgs> | null
  /**
   * The data needed to update a Invitation.
   */
  data: Prisma.XOR<Prisma.InvitationUpdateInput, Prisma.InvitationUncheckedUpdateInput>
  /**
   * Choose, which Invitation to update.
   */
  where: Prisma.InvitationWhereUniqueInput
}

/**
 * Invitation updateMany
 */
export type InvitationUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Invitations.
   */
  data: Prisma.XOR<Prisma.InvitationUpdateManyMutationInput, Prisma.InvitationUncheckedUpdateManyInput>
  /**
   * Filter which Invitations to update
   */
  where?: Prisma.InvitationWhereInput
  /**
   * Limit how many Invitations to update.
   */
  limit?: number
}

/**
 * Invitation upsert
 */
export type InvitationUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Invitation
   */
  select?: Prisma.InvitationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Invitation
   */
  omit?: Prisma.InvitationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.InvitationInclude<ExtArgs> | null
  /**
   * The filter to search for the Invitation to update in case it exists.
   */
  where: Prisma.InvitationWhereUniqueInput
  /**
   * In case the Invitation found by the `where` argument doesn't exist, create a new Invitation with this data.
   */
  create: Prisma.XOR<Prisma.InvitationCreateInput, Prisma.InvitationUncheckedCreateInput>
  /**
   * In case the Invitation was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.InvitationUpdateInput, Prisma.InvitationUncheckedUpdateInput>
}

/**
 * Invitation delete
 */
export type InvitationDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Invitation
   */
  select?: Prisma.InvitationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Invitation
   */
  omit?: Prisma.InvitationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.InvitationInclude<ExtArgs> | null
  /**
   * Filter which Invitation to delete.
   */
  where: Prisma.InvitationWhereUniqueInput
}

/**
 * Invitation deleteMany
 */
export type InvitationDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Invitations to delete
   */
  where?: Prisma.InvitationWhereInput
  /**
   * Limit how many Invitations to delete.
   */
  limit?: number
}

/**
 * Invitation without action
 */
export type InvitationDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Invitation
   */
  select?: Prisma.InvitationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Invitation
   */
  omit?: Prisma.InvitationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.InvitationInclude<ExtArgs> | null
}
