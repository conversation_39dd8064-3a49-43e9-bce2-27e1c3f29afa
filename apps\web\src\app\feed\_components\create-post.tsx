"use client"

import { useState } from "react"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import { client } from "@/utils/orpc"
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { useForm } from "react-hook-form"

type CreatePostInput = {
  title?: string
  content: string
  imageUrl?: string
  postType?: "GENERAL" | "SUCCESS_STORY" | "JOB_OPPORTUNITY" | "MENTORSHIP" | "ANNOUNCEMENT"
}

export default function CreatePost() {
  const queryClient = useQueryClient()
  const [submitting, setSubmitting] = useState(false)
  const form = useForm<CreatePostInput>({ defaultValues: { title: "", content: "", imageUrl: "", postType: "GENERAL" } })

  const create = useMutation({
    mutationFn: async (values: CreatePostInput) => client.posts.create({ ...values, postType: values.postType ?? "GENERAL" }),
    onSuccess: async () => {
      await queryClient.invalidateQueries()
      form.reset({ title: "", content: "", imageUrl: "", postType: "GENERAL" })
    },
  })

  async function onSubmit(values: CreatePostInput) {
    setSubmitting(true)
    try {
      await create.mutateAsync(values)
    } finally {
      setSubmitting(false)
    }
  }

  return (
    <div className="rounded-lg border bg-card p-4">
      <Form {...form}>
        <form className="grid gap-4" onSubmit={form.handleSubmit(onSubmit)}>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            <FormField name="title" control={form.control} render={({ field }) => (
              <FormItem>
                <FormLabel>Title (optional)</FormLabel>
                <FormControl><Input placeholder="Brief title" {...field} /></FormControl>
              </FormItem>
            )} />
            <FormField name="postType" control={form.control} render={({ field }) => (
              <FormItem>
                <FormLabel>Type</FormLabel>
                <FormControl>
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger><SelectValue /></SelectTrigger>
                    <SelectContent>
                      <SelectItem value="GENERAL">General</SelectItem>
                      <SelectItem value="SUCCESS_STORY">Success story</SelectItem>
                      <SelectItem value="JOB_OPPORTUNITY">Job opportunity</SelectItem>
                      <SelectItem value="MENTORSHIP">Mentorship</SelectItem>
                      <SelectItem value="ANNOUNCEMENT">Announcement</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
              </FormItem>
            )} />
            <FormField name="imageUrl" control={form.control} render={({ field }) => (
              <FormItem>
                <FormLabel>Image URL (optional)</FormLabel>
                <FormControl><Input placeholder="https://..." {...field} /></FormControl>
              </FormItem>
            )} />
          </div>
          <FormField name="content" control={form.control} rules={{ required: true }} render={({ field }) => (
            <FormItem>
              <FormLabel>Content</FormLabel>
              <FormControl><Textarea placeholder="Share an update..." className="min-h-28" {...field} /></FormControl>
            </FormItem>
          )} />
          <div className="flex items-center justify-end">
            <Button type="submit" disabled={submitting}>{submitting ? "Posting..." : "Post"}</Button>
          </div>
        </form>
      </Form>
    </div>
  )
}


