"use client"

import { useState } from "react"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { orpc, client } from "@/utils/orpc"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Button } from "@/components/ui/button"
import { useForm } from "react-hook-form"

type PrivacyInput = {
  profileVisibility: "PUBLIC" | "ALUMNI_ONLY" | "PRIVATE"
  showEmail: boolean
  showPhone: boolean
  showLocation: boolean
}

export function PrivacySettingsForm() {
  const queryClient = useQueryClient()
  const me = useQuery(orpc.profiles.getMe.queryOptions())
  const [saving, setSaving] = useState(false)

  const form = useForm<PrivacyInput>({
    defaultValues: {
      profileVisibility: "PUBLIC",
      showEmail: false,
      showPhone: false,
      showLocation: true,
    },
  })

  const updatePrivacy = useMutation({
    mutationFn: async (data: PrivacyInput) => client.profiles.updatePrivacy(data),
    onSuccess: async () => {
      await Promise.all([queryClient.invalidateQueries()])
    },
  })

  function syncDefaults() {
    if (!me.data) return
    const { profileVisibility, showEmail, showPhone, showLocation } = me.data as any
    form.reset({ profileVisibility, showEmail, showPhone, showLocation })
  }

  // Initialize defaults once data is in
  if (me.data && form.getValues("profileVisibility") === "PUBLIC" && me.data.profileVisibility !== "PUBLIC") {
    syncDefaults()
  }

  async function onSubmit(values: PrivacyInput) {
    setSaving(true)
    try {
      await updatePrivacy.mutateAsync(values)
    } finally {
      setSaving(false)
    }
  }

  return (
    <div className="space-y-4">
      <div className="rounded-lg border bg-card p-4">
        <Form {...form}>
          <form className="space-y-6" onSubmit={form.handleSubmit(onSubmit)}>
            <FormField name="profileVisibility" control={form.control} render={({ field }) => (
              <FormItem>
                <FormLabel>Profile visibility</FormLabel>
                <FormControl>
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger><SelectValue /></SelectTrigger>
                    <SelectContent>
                      <SelectItem value="PUBLIC">Public</SelectItem>
                      <SelectItem value="ALUMNI_ONLY">Alumni only</SelectItem>
                      <SelectItem value="PRIVATE">Private</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )} />

            <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
              <FormField name="showEmail" control={form.control} render={({ field }) => (
                <FormItem>
                  <FormLabel>Show email</FormLabel>
                  <FormControl><div className="flex items-center gap-3"><Switch checked={field.value} onCheckedChange={field.onChange} /></div></FormControl>
                  <FormMessage />
                </FormItem>
              )} />
              <FormField name="showPhone" control={form.control} render={({ field }) => (
                <FormItem>
                  <FormLabel>Show phone</FormLabel>
                  <FormControl><div className="flex items-center gap-3"><Switch checked={field.value} onCheckedChange={field.onChange} /></div></FormControl>
                  <FormMessage />
                </FormItem>
              )} />
              <FormField name="showLocation" control={form.control} render={({ field }) => (
                <FormItem>
                  <FormLabel>Show location</FormLabel>
                  <FormControl><div className="flex items-center gap-3"><Switch checked={field.value} onCheckedChange={field.onChange} /></div></FormControl>
                  <FormMessage />
                </FormItem>
              )} />
            </div>

            <div className="flex items-center justify-end">
              <Button type="submit" disabled={saving}>{saving ? "Saving..." : "Save privacy"}</Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  )
}

export default PrivacySettingsForm


