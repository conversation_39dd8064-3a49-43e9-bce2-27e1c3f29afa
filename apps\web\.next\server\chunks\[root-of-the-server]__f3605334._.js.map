{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Work/protec/apps/web/src/app/manifest.ts"], "sourcesContent": ["import type { MetadataRoute } from \"next\";\n\nexport default function manifest(): MetadataRoute.Manifest {\n\treturn {\n\t\tname: \"protec\",\n\t\tshort_name: \"protec\",\n\t\tdescription:\n\t\t\t\"my pwa app\",\n\t\tstart_url: \"/new\",\n\t\tdisplay: \"standalone\",\n\t\tbackground_color: \"#ffffff\",\n\t\ttheme_color: \"#000000\",\n\t\ticons: [\n\t\t\t{\n\t\t\t\tsrc: \"/favicon/web-app-manifest-192x192.png\",\n\t\t\t\tsizes: \"192x192\",\n\t\t\t\ttype: \"image/png\",\n\t\t\t},\n\t\t\t{\n\t\t\t\tsrc: \"/favicon/web-app-manifest-512x512.png\",\n\t\t\t\tsizes: \"512x512\",\n\t\t\t\ttype: \"image/png\",\n\t\t\t},\n\t\t],\n\t};\n}\n"], "names": [], "mappings": ";;;AAEe,SAAS;IACvB,OAAO;QACN,MAAM;QACN,YAAY;QACZ,aACC;QACD,WAAW;QACX,SAAS;QACT,kBAAkB;QAClB,aAAa;QACb,OAAO;YACN;gBACC,KAAK;gBACL,OAAO;gBACP,MAAM;YACP;YACA;gBACC,KAAK;gBACL,OAAO;gBACP,MAAM;YACP;SACA;IACF;AACD", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Work/protec/apps/web/src/app/manifest--route-entry.js"], "sourcesContent": ["            import { NextResponse } from 'next/server'\n            import handler from \"./manifest.ts\"\n            import { resolveRouteData } from\n'next/dist/build/webpack/loaders/metadata/resolve-route-data'\n\n            const contentType = \"application/manifest+json\"\n            const cacheControl = \"public, max-age=0, must-revalidate\"\n            const fileType = \"manifest\"\n\n            if (typeof handler !== 'function') {\n                throw new Error('Default export is missing in \"./manifest.ts\"')\n            }\n\n            export async function GET() {\n              const data = await handler()\n              const content = resolveRouteData(data, fileType)\n\n              return new NextResponse(content, {\n                headers: {\n                  'Content-Type': contentType,\n                  'Cache-Control': cacheControl,\n                },\n              })\n            }\n\n            export * from \"./manifest.ts\"\n        "], "names": [], "mappings": ";;;AAAY;AACA;AACA;;;;AAGA,MAAM,cAAc;AACpB,MAAM,eAAe;AACrB,MAAM,WAAW;AAEjB,IAAI,OAAO,qIAAA,CAAA,UAAO,KAAK,YAAY;IAC/B,MAAM,IAAI,MAAM;AACpB;AAEO,eAAe;IACpB,MAAM,OAAO,MAAM,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IACzB,MAAM,UAAU,CAAA,GAAA,mMAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM;IAEvC,OAAO,IAAI,8HAAA,CAAA,eAAY,CAAC,SAAS;QAC/B,SAAS;YACP,gBAAgB;YAChB,iBAAiB;QACnB;IACF;AACF", "debugId": null}}]}