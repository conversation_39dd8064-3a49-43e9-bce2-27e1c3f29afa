
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `Member` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model Member
 * 
 */
export type MemberModel = runtime.Types.Result.DefaultSelection<Prisma.$MemberPayload>

export type AggregateMember = {
  _count: MemberCountAggregateOutputType | null
  _min: MemberMinAggregateOutputType | null
  _max: MemberMaxAggregateOutputType | null
}

export type MemberMinAggregateOutputType = {
  id: string | null
  organizationId: string | null
  userId: string | null
  role: string | null
  createdAt: Date | null
}

export type MemberMaxAggregateOutputType = {
  id: string | null
  organizationId: string | null
  userId: string | null
  role: string | null
  createdAt: Date | null
}

export type MemberCountAggregateOutputType = {
  id: number
  organizationId: number
  userId: number
  role: number
  createdAt: number
  _all: number
}


export type MemberMinAggregateInputType = {
  id?: true
  organizationId?: true
  userId?: true
  role?: true
  createdAt?: true
}

export type MemberMaxAggregateInputType = {
  id?: true
  organizationId?: true
  userId?: true
  role?: true
  createdAt?: true
}

export type MemberCountAggregateInputType = {
  id?: true
  organizationId?: true
  userId?: true
  role?: true
  createdAt?: true
  _all?: true
}

export type MemberAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Member to aggregate.
   */
  where?: Prisma.MemberWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Members to fetch.
   */
  orderBy?: Prisma.MemberOrderByWithRelationInput | Prisma.MemberOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.MemberWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Members from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Members.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Members
  **/
  _count?: true | MemberCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: MemberMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: MemberMaxAggregateInputType
}

export type GetMemberAggregateType<T extends MemberAggregateArgs> = {
      [P in keyof T & keyof AggregateMember]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateMember[P]>
    : Prisma.GetScalarType<T[P], AggregateMember[P]>
}




export type MemberGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.MemberWhereInput
  orderBy?: Prisma.MemberOrderByWithAggregationInput | Prisma.MemberOrderByWithAggregationInput[]
  by: Prisma.MemberScalarFieldEnum[] | Prisma.MemberScalarFieldEnum
  having?: Prisma.MemberScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: MemberCountAggregateInputType | true
  _min?: MemberMinAggregateInputType
  _max?: MemberMaxAggregateInputType
}

export type MemberGroupByOutputType = {
  id: string
  organizationId: string
  userId: string
  role: string
  createdAt: Date
  _count: MemberCountAggregateOutputType | null
  _min: MemberMinAggregateOutputType | null
  _max: MemberMaxAggregateOutputType | null
}

type GetMemberGroupByPayload<T extends MemberGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<MemberGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof MemberGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], MemberGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], MemberGroupByOutputType[P]>
      }
    >
  >



export type MemberWhereInput = {
  AND?: Prisma.MemberWhereInput | Prisma.MemberWhereInput[]
  OR?: Prisma.MemberWhereInput[]
  NOT?: Prisma.MemberWhereInput | Prisma.MemberWhereInput[]
  id?: Prisma.StringFilter<"Member"> | string
  organizationId?: Prisma.StringFilter<"Member"> | string
  userId?: Prisma.StringFilter<"Member"> | string
  role?: Prisma.StringFilter<"Member"> | string
  createdAt?: Prisma.DateTimeFilter<"Member"> | Date | string
  organization?: Prisma.XOR<Prisma.OrganizationScalarRelationFilter, Prisma.OrganizationWhereInput>
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
  organizedEvents?: Prisma.EventListRelationFilter
}

export type MemberOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  organizationId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  role?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  organization?: Prisma.OrganizationOrderByWithRelationInput
  user?: Prisma.UserOrderByWithRelationInput
  organizedEvents?: Prisma.EventOrderByRelationAggregateInput
  _relevance?: Prisma.MemberOrderByRelevanceInput
}

export type MemberWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  AND?: Prisma.MemberWhereInput | Prisma.MemberWhereInput[]
  OR?: Prisma.MemberWhereInput[]
  NOT?: Prisma.MemberWhereInput | Prisma.MemberWhereInput[]
  organizationId?: Prisma.StringFilter<"Member"> | string
  userId?: Prisma.StringFilter<"Member"> | string
  role?: Prisma.StringFilter<"Member"> | string
  createdAt?: Prisma.DateTimeFilter<"Member"> | Date | string
  organization?: Prisma.XOR<Prisma.OrganizationScalarRelationFilter, Prisma.OrganizationWhereInput>
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
  organizedEvents?: Prisma.EventListRelationFilter
}, "id">

export type MemberOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  organizationId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  role?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  _count?: Prisma.MemberCountOrderByAggregateInput
  _max?: Prisma.MemberMaxOrderByAggregateInput
  _min?: Prisma.MemberMinOrderByAggregateInput
}

export type MemberScalarWhereWithAggregatesInput = {
  AND?: Prisma.MemberScalarWhereWithAggregatesInput | Prisma.MemberScalarWhereWithAggregatesInput[]
  OR?: Prisma.MemberScalarWhereWithAggregatesInput[]
  NOT?: Prisma.MemberScalarWhereWithAggregatesInput | Prisma.MemberScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"Member"> | string
  organizationId?: Prisma.StringWithAggregatesFilter<"Member"> | string
  userId?: Prisma.StringWithAggregatesFilter<"Member"> | string
  role?: Prisma.StringWithAggregatesFilter<"Member"> | string
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"Member"> | Date | string
}

export type MemberCreateInput = {
  id?: string
  role: string
  createdAt?: Date | string
  organization: Prisma.OrganizationCreateNestedOneWithoutMembersInput
  user: Prisma.UserCreateNestedOneWithoutMemberInput
  organizedEvents?: Prisma.EventCreateNestedManyWithoutOrganizerInput
}

export type MemberUncheckedCreateInput = {
  id?: string
  organizationId: string
  userId: string
  role: string
  createdAt?: Date | string
  organizedEvents?: Prisma.EventUncheckedCreateNestedManyWithoutOrganizerInput
}

export type MemberUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  organization?: Prisma.OrganizationUpdateOneRequiredWithoutMembersNestedInput
  user?: Prisma.UserUpdateOneRequiredWithoutMemberNestedInput
  organizedEvents?: Prisma.EventUpdateManyWithoutOrganizerNestedInput
}

export type MemberUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  organizationId?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  organizedEvents?: Prisma.EventUncheckedUpdateManyWithoutOrganizerNestedInput
}

export type MemberCreateManyInput = {
  id?: string
  organizationId: string
  userId: string
  role: string
  createdAt?: Date | string
}

export type MemberUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type MemberUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  organizationId?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type MemberListRelationFilter = {
  every?: Prisma.MemberWhereInput
  some?: Prisma.MemberWhereInput
  none?: Prisma.MemberWhereInput
}

export type MemberOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type MemberOrderByRelevanceInput = {
  fields: Prisma.MemberOrderByRelevanceFieldEnum | Prisma.MemberOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type MemberCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  organizationId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  role?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
}

export type MemberMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  organizationId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  role?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
}

export type MemberMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  organizationId?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  role?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
}

export type MemberNullableScalarRelationFilter = {
  is?: Prisma.MemberWhereInput | null
  isNot?: Prisma.MemberWhereInput | null
}

export type MemberCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.MemberCreateWithoutUserInput, Prisma.MemberUncheckedCreateWithoutUserInput> | Prisma.MemberCreateWithoutUserInput[] | Prisma.MemberUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.MemberCreateOrConnectWithoutUserInput | Prisma.MemberCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.MemberCreateManyUserInputEnvelope
  connect?: Prisma.MemberWhereUniqueInput | Prisma.MemberWhereUniqueInput[]
}

export type MemberUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.MemberCreateWithoutUserInput, Prisma.MemberUncheckedCreateWithoutUserInput> | Prisma.MemberCreateWithoutUserInput[] | Prisma.MemberUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.MemberCreateOrConnectWithoutUserInput | Prisma.MemberCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.MemberCreateManyUserInputEnvelope
  connect?: Prisma.MemberWhereUniqueInput | Prisma.MemberWhereUniqueInput[]
}

export type MemberUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.MemberCreateWithoutUserInput, Prisma.MemberUncheckedCreateWithoutUserInput> | Prisma.MemberCreateWithoutUserInput[] | Prisma.MemberUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.MemberCreateOrConnectWithoutUserInput | Prisma.MemberCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.MemberUpsertWithWhereUniqueWithoutUserInput | Prisma.MemberUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.MemberCreateManyUserInputEnvelope
  set?: Prisma.MemberWhereUniqueInput | Prisma.MemberWhereUniqueInput[]
  disconnect?: Prisma.MemberWhereUniqueInput | Prisma.MemberWhereUniqueInput[]
  delete?: Prisma.MemberWhereUniqueInput | Prisma.MemberWhereUniqueInput[]
  connect?: Prisma.MemberWhereUniqueInput | Prisma.MemberWhereUniqueInput[]
  update?: Prisma.MemberUpdateWithWhereUniqueWithoutUserInput | Prisma.MemberUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.MemberUpdateManyWithWhereWithoutUserInput | Prisma.MemberUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.MemberScalarWhereInput | Prisma.MemberScalarWhereInput[]
}

export type MemberUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.MemberCreateWithoutUserInput, Prisma.MemberUncheckedCreateWithoutUserInput> | Prisma.MemberCreateWithoutUserInput[] | Prisma.MemberUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.MemberCreateOrConnectWithoutUserInput | Prisma.MemberCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.MemberUpsertWithWhereUniqueWithoutUserInput | Prisma.MemberUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.MemberCreateManyUserInputEnvelope
  set?: Prisma.MemberWhereUniqueInput | Prisma.MemberWhereUniqueInput[]
  disconnect?: Prisma.MemberWhereUniqueInput | Prisma.MemberWhereUniqueInput[]
  delete?: Prisma.MemberWhereUniqueInput | Prisma.MemberWhereUniqueInput[]
  connect?: Prisma.MemberWhereUniqueInput | Prisma.MemberWhereUniqueInput[]
  update?: Prisma.MemberUpdateWithWhereUniqueWithoutUserInput | Prisma.MemberUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.MemberUpdateManyWithWhereWithoutUserInput | Prisma.MemberUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.MemberScalarWhereInput | Prisma.MemberScalarWhereInput[]
}

export type MemberCreateNestedManyWithoutOrganizationInput = {
  create?: Prisma.XOR<Prisma.MemberCreateWithoutOrganizationInput, Prisma.MemberUncheckedCreateWithoutOrganizationInput> | Prisma.MemberCreateWithoutOrganizationInput[] | Prisma.MemberUncheckedCreateWithoutOrganizationInput[]
  connectOrCreate?: Prisma.MemberCreateOrConnectWithoutOrganizationInput | Prisma.MemberCreateOrConnectWithoutOrganizationInput[]
  createMany?: Prisma.MemberCreateManyOrganizationInputEnvelope
  connect?: Prisma.MemberWhereUniqueInput | Prisma.MemberWhereUniqueInput[]
}

export type MemberUncheckedCreateNestedManyWithoutOrganizationInput = {
  create?: Prisma.XOR<Prisma.MemberCreateWithoutOrganizationInput, Prisma.MemberUncheckedCreateWithoutOrganizationInput> | Prisma.MemberCreateWithoutOrganizationInput[] | Prisma.MemberUncheckedCreateWithoutOrganizationInput[]
  connectOrCreate?: Prisma.MemberCreateOrConnectWithoutOrganizationInput | Prisma.MemberCreateOrConnectWithoutOrganizationInput[]
  createMany?: Prisma.MemberCreateManyOrganizationInputEnvelope
  connect?: Prisma.MemberWhereUniqueInput | Prisma.MemberWhereUniqueInput[]
}

export type MemberUpdateManyWithoutOrganizationNestedInput = {
  create?: Prisma.XOR<Prisma.MemberCreateWithoutOrganizationInput, Prisma.MemberUncheckedCreateWithoutOrganizationInput> | Prisma.MemberCreateWithoutOrganizationInput[] | Prisma.MemberUncheckedCreateWithoutOrganizationInput[]
  connectOrCreate?: Prisma.MemberCreateOrConnectWithoutOrganizationInput | Prisma.MemberCreateOrConnectWithoutOrganizationInput[]
  upsert?: Prisma.MemberUpsertWithWhereUniqueWithoutOrganizationInput | Prisma.MemberUpsertWithWhereUniqueWithoutOrganizationInput[]
  createMany?: Prisma.MemberCreateManyOrganizationInputEnvelope
  set?: Prisma.MemberWhereUniqueInput | Prisma.MemberWhereUniqueInput[]
  disconnect?: Prisma.MemberWhereUniqueInput | Prisma.MemberWhereUniqueInput[]
  delete?: Prisma.MemberWhereUniqueInput | Prisma.MemberWhereUniqueInput[]
  connect?: Prisma.MemberWhereUniqueInput | Prisma.MemberWhereUniqueInput[]
  update?: Prisma.MemberUpdateWithWhereUniqueWithoutOrganizationInput | Prisma.MemberUpdateWithWhereUniqueWithoutOrganizationInput[]
  updateMany?: Prisma.MemberUpdateManyWithWhereWithoutOrganizationInput | Prisma.MemberUpdateManyWithWhereWithoutOrganizationInput[]
  deleteMany?: Prisma.MemberScalarWhereInput | Prisma.MemberScalarWhereInput[]
}

export type MemberUncheckedUpdateManyWithoutOrganizationNestedInput = {
  create?: Prisma.XOR<Prisma.MemberCreateWithoutOrganizationInput, Prisma.MemberUncheckedCreateWithoutOrganizationInput> | Prisma.MemberCreateWithoutOrganizationInput[] | Prisma.MemberUncheckedCreateWithoutOrganizationInput[]
  connectOrCreate?: Prisma.MemberCreateOrConnectWithoutOrganizationInput | Prisma.MemberCreateOrConnectWithoutOrganizationInput[]
  upsert?: Prisma.MemberUpsertWithWhereUniqueWithoutOrganizationInput | Prisma.MemberUpsertWithWhereUniqueWithoutOrganizationInput[]
  createMany?: Prisma.MemberCreateManyOrganizationInputEnvelope
  set?: Prisma.MemberWhereUniqueInput | Prisma.MemberWhereUniqueInput[]
  disconnect?: Prisma.MemberWhereUniqueInput | Prisma.MemberWhereUniqueInput[]
  delete?: Prisma.MemberWhereUniqueInput | Prisma.MemberWhereUniqueInput[]
  connect?: Prisma.MemberWhereUniqueInput | Prisma.MemberWhereUniqueInput[]
  update?: Prisma.MemberUpdateWithWhereUniqueWithoutOrganizationInput | Prisma.MemberUpdateWithWhereUniqueWithoutOrganizationInput[]
  updateMany?: Prisma.MemberUpdateManyWithWhereWithoutOrganizationInput | Prisma.MemberUpdateManyWithWhereWithoutOrganizationInput[]
  deleteMany?: Prisma.MemberScalarWhereInput | Prisma.MemberScalarWhereInput[]
}

export type MemberCreateNestedOneWithoutOrganizedEventsInput = {
  create?: Prisma.XOR<Prisma.MemberCreateWithoutOrganizedEventsInput, Prisma.MemberUncheckedCreateWithoutOrganizedEventsInput>
  connectOrCreate?: Prisma.MemberCreateOrConnectWithoutOrganizedEventsInput
  connect?: Prisma.MemberWhereUniqueInput
}

export type MemberUpdateOneWithoutOrganizedEventsNestedInput = {
  create?: Prisma.XOR<Prisma.MemberCreateWithoutOrganizedEventsInput, Prisma.MemberUncheckedCreateWithoutOrganizedEventsInput>
  connectOrCreate?: Prisma.MemberCreateOrConnectWithoutOrganizedEventsInput
  upsert?: Prisma.MemberUpsertWithoutOrganizedEventsInput
  disconnect?: Prisma.MemberWhereInput | boolean
  delete?: Prisma.MemberWhereInput | boolean
  connect?: Prisma.MemberWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.MemberUpdateToOneWithWhereWithoutOrganizedEventsInput, Prisma.MemberUpdateWithoutOrganizedEventsInput>, Prisma.MemberUncheckedUpdateWithoutOrganizedEventsInput>
}

export type MemberCreateWithoutUserInput = {
  id?: string
  role: string
  createdAt?: Date | string
  organization: Prisma.OrganizationCreateNestedOneWithoutMembersInput
  organizedEvents?: Prisma.EventCreateNestedManyWithoutOrganizerInput
}

export type MemberUncheckedCreateWithoutUserInput = {
  id?: string
  organizationId: string
  role: string
  createdAt?: Date | string
  organizedEvents?: Prisma.EventUncheckedCreateNestedManyWithoutOrganizerInput
}

export type MemberCreateOrConnectWithoutUserInput = {
  where: Prisma.MemberWhereUniqueInput
  create: Prisma.XOR<Prisma.MemberCreateWithoutUserInput, Prisma.MemberUncheckedCreateWithoutUserInput>
}

export type MemberCreateManyUserInputEnvelope = {
  data: Prisma.MemberCreateManyUserInput | Prisma.MemberCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type MemberUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.MemberWhereUniqueInput
  update: Prisma.XOR<Prisma.MemberUpdateWithoutUserInput, Prisma.MemberUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.MemberCreateWithoutUserInput, Prisma.MemberUncheckedCreateWithoutUserInput>
}

export type MemberUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.MemberWhereUniqueInput
  data: Prisma.XOR<Prisma.MemberUpdateWithoutUserInput, Prisma.MemberUncheckedUpdateWithoutUserInput>
}

export type MemberUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.MemberScalarWhereInput
  data: Prisma.XOR<Prisma.MemberUpdateManyMutationInput, Prisma.MemberUncheckedUpdateManyWithoutUserInput>
}

export type MemberScalarWhereInput = {
  AND?: Prisma.MemberScalarWhereInput | Prisma.MemberScalarWhereInput[]
  OR?: Prisma.MemberScalarWhereInput[]
  NOT?: Prisma.MemberScalarWhereInput | Prisma.MemberScalarWhereInput[]
  id?: Prisma.StringFilter<"Member"> | string
  organizationId?: Prisma.StringFilter<"Member"> | string
  userId?: Prisma.StringFilter<"Member"> | string
  role?: Prisma.StringFilter<"Member"> | string
  createdAt?: Prisma.DateTimeFilter<"Member"> | Date | string
}

export type MemberCreateWithoutOrganizationInput = {
  id?: string
  role: string
  createdAt?: Date | string
  user: Prisma.UserCreateNestedOneWithoutMemberInput
  organizedEvents?: Prisma.EventCreateNestedManyWithoutOrganizerInput
}

export type MemberUncheckedCreateWithoutOrganizationInput = {
  id?: string
  userId: string
  role: string
  createdAt?: Date | string
  organizedEvents?: Prisma.EventUncheckedCreateNestedManyWithoutOrganizerInput
}

export type MemberCreateOrConnectWithoutOrganizationInput = {
  where: Prisma.MemberWhereUniqueInput
  create: Prisma.XOR<Prisma.MemberCreateWithoutOrganizationInput, Prisma.MemberUncheckedCreateWithoutOrganizationInput>
}

export type MemberCreateManyOrganizationInputEnvelope = {
  data: Prisma.MemberCreateManyOrganizationInput | Prisma.MemberCreateManyOrganizationInput[]
  skipDuplicates?: boolean
}

export type MemberUpsertWithWhereUniqueWithoutOrganizationInput = {
  where: Prisma.MemberWhereUniqueInput
  update: Prisma.XOR<Prisma.MemberUpdateWithoutOrganizationInput, Prisma.MemberUncheckedUpdateWithoutOrganizationInput>
  create: Prisma.XOR<Prisma.MemberCreateWithoutOrganizationInput, Prisma.MemberUncheckedCreateWithoutOrganizationInput>
}

export type MemberUpdateWithWhereUniqueWithoutOrganizationInput = {
  where: Prisma.MemberWhereUniqueInput
  data: Prisma.XOR<Prisma.MemberUpdateWithoutOrganizationInput, Prisma.MemberUncheckedUpdateWithoutOrganizationInput>
}

export type MemberUpdateManyWithWhereWithoutOrganizationInput = {
  where: Prisma.MemberScalarWhereInput
  data: Prisma.XOR<Prisma.MemberUpdateManyMutationInput, Prisma.MemberUncheckedUpdateManyWithoutOrganizationInput>
}

export type MemberCreateWithoutOrganizedEventsInput = {
  id?: string
  role: string
  createdAt?: Date | string
  organization: Prisma.OrganizationCreateNestedOneWithoutMembersInput
  user: Prisma.UserCreateNestedOneWithoutMemberInput
}

export type MemberUncheckedCreateWithoutOrganizedEventsInput = {
  id?: string
  organizationId: string
  userId: string
  role: string
  createdAt?: Date | string
}

export type MemberCreateOrConnectWithoutOrganizedEventsInput = {
  where: Prisma.MemberWhereUniqueInput
  create: Prisma.XOR<Prisma.MemberCreateWithoutOrganizedEventsInput, Prisma.MemberUncheckedCreateWithoutOrganizedEventsInput>
}

export type MemberUpsertWithoutOrganizedEventsInput = {
  update: Prisma.XOR<Prisma.MemberUpdateWithoutOrganizedEventsInput, Prisma.MemberUncheckedUpdateWithoutOrganizedEventsInput>
  create: Prisma.XOR<Prisma.MemberCreateWithoutOrganizedEventsInput, Prisma.MemberUncheckedCreateWithoutOrganizedEventsInput>
  where?: Prisma.MemberWhereInput
}

export type MemberUpdateToOneWithWhereWithoutOrganizedEventsInput = {
  where?: Prisma.MemberWhereInput
  data: Prisma.XOR<Prisma.MemberUpdateWithoutOrganizedEventsInput, Prisma.MemberUncheckedUpdateWithoutOrganizedEventsInput>
}

export type MemberUpdateWithoutOrganizedEventsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  organization?: Prisma.OrganizationUpdateOneRequiredWithoutMembersNestedInput
  user?: Prisma.UserUpdateOneRequiredWithoutMemberNestedInput
}

export type MemberUncheckedUpdateWithoutOrganizedEventsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  organizationId?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type MemberCreateManyUserInput = {
  id?: string
  organizationId: string
  role: string
  createdAt?: Date | string
}

export type MemberUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  organization?: Prisma.OrganizationUpdateOneRequiredWithoutMembersNestedInput
  organizedEvents?: Prisma.EventUpdateManyWithoutOrganizerNestedInput
}

export type MemberUncheckedUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  organizationId?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  organizedEvents?: Prisma.EventUncheckedUpdateManyWithoutOrganizerNestedInput
}

export type MemberUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  organizationId?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type MemberCreateManyOrganizationInput = {
  id?: string
  userId: string
  role: string
  createdAt?: Date | string
}

export type MemberUpdateWithoutOrganizationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.UserUpdateOneRequiredWithoutMemberNestedInput
  organizedEvents?: Prisma.EventUpdateManyWithoutOrganizerNestedInput
}

export type MemberUncheckedUpdateWithoutOrganizationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  organizedEvents?: Prisma.EventUncheckedUpdateManyWithoutOrganizerNestedInput
}

export type MemberUncheckedUpdateManyWithoutOrganizationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}


/**
 * Count Type MemberCountOutputType
 */

export type MemberCountOutputType = {
  organizedEvents: number
}

export type MemberCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  organizedEvents?: boolean | MemberCountOutputTypeCountOrganizedEventsArgs
}

/**
 * MemberCountOutputType without action
 */
export type MemberCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the MemberCountOutputType
   */
  select?: Prisma.MemberCountOutputTypeSelect<ExtArgs> | null
}

/**
 * MemberCountOutputType without action
 */
export type MemberCountOutputTypeCountOrganizedEventsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.EventWhereInput
}


export type MemberSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  organizationId?: boolean
  userId?: boolean
  role?: boolean
  createdAt?: boolean
  organization?: boolean | Prisma.OrganizationDefaultArgs<ExtArgs>
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  organizedEvents?: boolean | Prisma.Member$organizedEventsArgs<ExtArgs>
  _count?: boolean | Prisma.MemberCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["member"]>



export type MemberSelectScalar = {
  id?: boolean
  organizationId?: boolean
  userId?: boolean
  role?: boolean
  createdAt?: boolean
}

export type MemberOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "organizationId" | "userId" | "role" | "createdAt", ExtArgs["result"]["member"]>
export type MemberInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  organization?: boolean | Prisma.OrganizationDefaultArgs<ExtArgs>
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  organizedEvents?: boolean | Prisma.Member$organizedEventsArgs<ExtArgs>
  _count?: boolean | Prisma.MemberCountOutputTypeDefaultArgs<ExtArgs>
}

export type $MemberPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Member"
  objects: {
    organization: Prisma.$OrganizationPayload<ExtArgs>
    user: Prisma.$UserPayload<ExtArgs>
    organizedEvents: Prisma.$EventPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    organizationId: string
    userId: string
    role: string
    createdAt: Date
  }, ExtArgs["result"]["member"]>
  composites: {}
}

export type MemberGetPayload<S extends boolean | null | undefined | MemberDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$MemberPayload, S>

export type MemberCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<MemberFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: MemberCountAggregateInputType | true
  }

export interface MemberDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Member'], meta: { name: 'Member' } }
  /**
   * Find zero or one Member that matches the filter.
   * @param {MemberFindUniqueArgs} args - Arguments to find a Member
   * @example
   * // Get one Member
   * const member = await prisma.member.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends MemberFindUniqueArgs>(args: Prisma.SelectSubset<T, MemberFindUniqueArgs<ExtArgs>>): Prisma.Prisma__MemberClient<runtime.Types.Result.GetResult<Prisma.$MemberPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Member that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {MemberFindUniqueOrThrowArgs} args - Arguments to find a Member
   * @example
   * // Get one Member
   * const member = await prisma.member.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends MemberFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, MemberFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__MemberClient<runtime.Types.Result.GetResult<Prisma.$MemberPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Member that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {MemberFindFirstArgs} args - Arguments to find a Member
   * @example
   * // Get one Member
   * const member = await prisma.member.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends MemberFindFirstArgs>(args?: Prisma.SelectSubset<T, MemberFindFirstArgs<ExtArgs>>): Prisma.Prisma__MemberClient<runtime.Types.Result.GetResult<Prisma.$MemberPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Member that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {MemberFindFirstOrThrowArgs} args - Arguments to find a Member
   * @example
   * // Get one Member
   * const member = await prisma.member.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends MemberFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, MemberFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__MemberClient<runtime.Types.Result.GetResult<Prisma.$MemberPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Members that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {MemberFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Members
   * const members = await prisma.member.findMany()
   * 
   * // Get first 10 Members
   * const members = await prisma.member.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const memberWithIdOnly = await prisma.member.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends MemberFindManyArgs>(args?: Prisma.SelectSubset<T, MemberFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$MemberPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Member.
   * @param {MemberCreateArgs} args - Arguments to create a Member.
   * @example
   * // Create one Member
   * const Member = await prisma.member.create({
   *   data: {
   *     // ... data to create a Member
   *   }
   * })
   * 
   */
  create<T extends MemberCreateArgs>(args: Prisma.SelectSubset<T, MemberCreateArgs<ExtArgs>>): Prisma.Prisma__MemberClient<runtime.Types.Result.GetResult<Prisma.$MemberPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Members.
   * @param {MemberCreateManyArgs} args - Arguments to create many Members.
   * @example
   * // Create many Members
   * const member = await prisma.member.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends MemberCreateManyArgs>(args?: Prisma.SelectSubset<T, MemberCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Member.
   * @param {MemberDeleteArgs} args - Arguments to delete one Member.
   * @example
   * // Delete one Member
   * const Member = await prisma.member.delete({
   *   where: {
   *     // ... filter to delete one Member
   *   }
   * })
   * 
   */
  delete<T extends MemberDeleteArgs>(args: Prisma.SelectSubset<T, MemberDeleteArgs<ExtArgs>>): Prisma.Prisma__MemberClient<runtime.Types.Result.GetResult<Prisma.$MemberPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Member.
   * @param {MemberUpdateArgs} args - Arguments to update one Member.
   * @example
   * // Update one Member
   * const member = await prisma.member.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends MemberUpdateArgs>(args: Prisma.SelectSubset<T, MemberUpdateArgs<ExtArgs>>): Prisma.Prisma__MemberClient<runtime.Types.Result.GetResult<Prisma.$MemberPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Members.
   * @param {MemberDeleteManyArgs} args - Arguments to filter Members to delete.
   * @example
   * // Delete a few Members
   * const { count } = await prisma.member.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends MemberDeleteManyArgs>(args?: Prisma.SelectSubset<T, MemberDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Members.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {MemberUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Members
   * const member = await prisma.member.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends MemberUpdateManyArgs>(args: Prisma.SelectSubset<T, MemberUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Member.
   * @param {MemberUpsertArgs} args - Arguments to update or create a Member.
   * @example
   * // Update or create a Member
   * const member = await prisma.member.upsert({
   *   create: {
   *     // ... data to create a Member
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Member we want to update
   *   }
   * })
   */
  upsert<T extends MemberUpsertArgs>(args: Prisma.SelectSubset<T, MemberUpsertArgs<ExtArgs>>): Prisma.Prisma__MemberClient<runtime.Types.Result.GetResult<Prisma.$MemberPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Members.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {MemberCountArgs} args - Arguments to filter Members to count.
   * @example
   * // Count the number of Members
   * const count = await prisma.member.count({
   *   where: {
   *     // ... the filter for the Members we want to count
   *   }
   * })
  **/
  count<T extends MemberCountArgs>(
    args?: Prisma.Subset<T, MemberCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], MemberCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Member.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {MemberAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends MemberAggregateArgs>(args: Prisma.Subset<T, MemberAggregateArgs>): Prisma.PrismaPromise<GetMemberAggregateType<T>>

  /**
   * Group by Member.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {MemberGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends MemberGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: MemberGroupByArgs['orderBy'] }
      : { orderBy?: MemberGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, MemberGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetMemberGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Member model
 */
readonly fields: MemberFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Member.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__MemberClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  organization<T extends Prisma.OrganizationDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.OrganizationDefaultArgs<ExtArgs>>): Prisma.Prisma__OrganizationClient<runtime.Types.Result.GetResult<Prisma.$OrganizationPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  user<T extends Prisma.UserDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.UserDefaultArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  organizedEvents<T extends Prisma.Member$organizedEventsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Member$organizedEventsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$EventPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Member model
 */
export interface MemberFieldRefs {
  readonly id: Prisma.FieldRef<"Member", 'String'>
  readonly organizationId: Prisma.FieldRef<"Member", 'String'>
  readonly userId: Prisma.FieldRef<"Member", 'String'>
  readonly role: Prisma.FieldRef<"Member", 'String'>
  readonly createdAt: Prisma.FieldRef<"Member", 'DateTime'>
}
    

// Custom InputTypes
/**
 * Member findUnique
 */
export type MemberFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Member
   */
  select?: Prisma.MemberSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Member
   */
  omit?: Prisma.MemberOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MemberInclude<ExtArgs> | null
  /**
   * Filter, which Member to fetch.
   */
  where: Prisma.MemberWhereUniqueInput
}

/**
 * Member findUniqueOrThrow
 */
export type MemberFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Member
   */
  select?: Prisma.MemberSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Member
   */
  omit?: Prisma.MemberOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MemberInclude<ExtArgs> | null
  /**
   * Filter, which Member to fetch.
   */
  where: Prisma.MemberWhereUniqueInput
}

/**
 * Member findFirst
 */
export type MemberFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Member
   */
  select?: Prisma.MemberSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Member
   */
  omit?: Prisma.MemberOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MemberInclude<ExtArgs> | null
  /**
   * Filter, which Member to fetch.
   */
  where?: Prisma.MemberWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Members to fetch.
   */
  orderBy?: Prisma.MemberOrderByWithRelationInput | Prisma.MemberOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Members.
   */
  cursor?: Prisma.MemberWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Members from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Members.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Members.
   */
  distinct?: Prisma.MemberScalarFieldEnum | Prisma.MemberScalarFieldEnum[]
}

/**
 * Member findFirstOrThrow
 */
export type MemberFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Member
   */
  select?: Prisma.MemberSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Member
   */
  omit?: Prisma.MemberOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MemberInclude<ExtArgs> | null
  /**
   * Filter, which Member to fetch.
   */
  where?: Prisma.MemberWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Members to fetch.
   */
  orderBy?: Prisma.MemberOrderByWithRelationInput | Prisma.MemberOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Members.
   */
  cursor?: Prisma.MemberWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Members from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Members.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Members.
   */
  distinct?: Prisma.MemberScalarFieldEnum | Prisma.MemberScalarFieldEnum[]
}

/**
 * Member findMany
 */
export type MemberFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Member
   */
  select?: Prisma.MemberSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Member
   */
  omit?: Prisma.MemberOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MemberInclude<ExtArgs> | null
  /**
   * Filter, which Members to fetch.
   */
  where?: Prisma.MemberWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Members to fetch.
   */
  orderBy?: Prisma.MemberOrderByWithRelationInput | Prisma.MemberOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Members.
   */
  cursor?: Prisma.MemberWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Members from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Members.
   */
  skip?: number
  distinct?: Prisma.MemberScalarFieldEnum | Prisma.MemberScalarFieldEnum[]
}

/**
 * Member create
 */
export type MemberCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Member
   */
  select?: Prisma.MemberSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Member
   */
  omit?: Prisma.MemberOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MemberInclude<ExtArgs> | null
  /**
   * The data needed to create a Member.
   */
  data: Prisma.XOR<Prisma.MemberCreateInput, Prisma.MemberUncheckedCreateInput>
}

/**
 * Member createMany
 */
export type MemberCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Members.
   */
  data: Prisma.MemberCreateManyInput | Prisma.MemberCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Member update
 */
export type MemberUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Member
   */
  select?: Prisma.MemberSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Member
   */
  omit?: Prisma.MemberOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MemberInclude<ExtArgs> | null
  /**
   * The data needed to update a Member.
   */
  data: Prisma.XOR<Prisma.MemberUpdateInput, Prisma.MemberUncheckedUpdateInput>
  /**
   * Choose, which Member to update.
   */
  where: Prisma.MemberWhereUniqueInput
}

/**
 * Member updateMany
 */
export type MemberUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Members.
   */
  data: Prisma.XOR<Prisma.MemberUpdateManyMutationInput, Prisma.MemberUncheckedUpdateManyInput>
  /**
   * Filter which Members to update
   */
  where?: Prisma.MemberWhereInput
  /**
   * Limit how many Members to update.
   */
  limit?: number
}

/**
 * Member upsert
 */
export type MemberUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Member
   */
  select?: Prisma.MemberSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Member
   */
  omit?: Prisma.MemberOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MemberInclude<ExtArgs> | null
  /**
   * The filter to search for the Member to update in case it exists.
   */
  where: Prisma.MemberWhereUniqueInput
  /**
   * In case the Member found by the `where` argument doesn't exist, create a new Member with this data.
   */
  create: Prisma.XOR<Prisma.MemberCreateInput, Prisma.MemberUncheckedCreateInput>
  /**
   * In case the Member was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.MemberUpdateInput, Prisma.MemberUncheckedUpdateInput>
}

/**
 * Member delete
 */
export type MemberDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Member
   */
  select?: Prisma.MemberSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Member
   */
  omit?: Prisma.MemberOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MemberInclude<ExtArgs> | null
  /**
   * Filter which Member to delete.
   */
  where: Prisma.MemberWhereUniqueInput
}

/**
 * Member deleteMany
 */
export type MemberDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Members to delete
   */
  where?: Prisma.MemberWhereInput
  /**
   * Limit how many Members to delete.
   */
  limit?: number
}

/**
 * Member.organizedEvents
 */
export type Member$organizedEventsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Event
   */
  select?: Prisma.EventSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Event
   */
  omit?: Prisma.EventOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.EventInclude<ExtArgs> | null
  where?: Prisma.EventWhereInput
  orderBy?: Prisma.EventOrderByWithRelationInput | Prisma.EventOrderByWithRelationInput[]
  cursor?: Prisma.EventWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.EventScalarFieldEnum | Prisma.EventScalarFieldEnum[]
}

/**
 * Member without action
 */
export type MemberDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Member
   */
  select?: Prisma.MemberSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Member
   */
  omit?: Prisma.MemberOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MemberInclude<ExtArgs> | null
}
