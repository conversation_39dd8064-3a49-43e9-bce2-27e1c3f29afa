"use client"

import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { useEffect } from "react"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { orpc, client } from "@/utils/orpc"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form"
import { Textarea } from "@/components/ui/textarea"
import { useForm } from "react-hook-form"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

type CommentInput = { content: string }
type EditPostInput = { title?: string; content: string; imageUrl?: string; postType?: "GENERAL" | "SUCCESS_STORY" | "JOB_OPPORTUNITY" | "MENTORSHIP" | "ANNOUNCEMENT" }

export default function PostDetail() {
  const params = useParams()
  const id = params?.id as string
  const router = useRouter()
  const queryClient = useQueryClient()

  const post = useQuery(orpc.posts.getById.queryOptions({ input: { id } }))

  const toggleLike = useMutation({
    mutationFn: async () => client.posts.toggleLike({ postId: id }),
    onSuccess: async () => queryClient.invalidateQueries(),
  })

  const del = useMutation({
    mutationFn: async () => client.posts.delete({ id }),
    onSuccess: async () => { await queryClient.invalidateQueries(); router.push("/feed") },
  })

  const form = useForm<CommentInput>({ defaultValues: { content: "" } })
  const addComment = useMutation({
    mutationFn: async (values: CommentInput) => client.posts.comments.create({ postId: id, content: values.content }),
    onSuccess: async () => { await queryClient.invalidateQueries(); form.reset({ content: "" }) },
  })

  // Edit post
  const editForm = useForm<EditPostInput>({ defaultValues: { title: "", content: "", imageUrl: "", postType: "GENERAL" } })
  useEffect(() => {
    const p: any = post.data
    if (p) {
      editForm.reset({ title: p.title ?? "", content: p.content ?? "", imageUrl: p.imageUrl ?? "", postType: p.postType ?? "GENERAL" })
    }
  }, [post.data])
  const update = useMutation({
    mutationFn: async (v: EditPostInput) => client.posts.update({ id, ...v }),
    onSuccess: async () => queryClient.invalidateQueries(),
  })

  if (post.isLoading) return <div className="text-sm text-muted-foreground">Loading...</div>
  if (post.isError) return <div className="text-sm text-destructive">Failed to load post.</div>
  const p = post.data as any

  return (
    <div className="space-y-4">
      <div className="rounded-lg border bg-card p-4">
        <div className="flex items-center justify-between">
          <div className="font-medium text-lg">{p.title || "Post"}</div>
          <div className="text-xs text-muted-foreground">{new Date(p.createdAt).toLocaleString()}</div>
        </div>
        <div className="mt-2 text-sm whitespace-pre-wrap">{p.content}</div>
        <div className="mt-3 flex items-center gap-2">
          <Button size="sm" variant="outline" onClick={() => toggleLike.mutate()} disabled={toggleLike.isPending}>Like ({p._count?.likes ?? 0})</Button>
          <Button size="sm" variant="destructive" onClick={() => del.mutate()} disabled={del.isPending}>Delete</Button>
        </div>
      </div>

      <div className="rounded-lg border bg-card p-4">
        <div className="text-sm font-medium mb-2 text-muted-foreground">Edit post</div>
        <Form {...editForm}>
          <form className="grid gap-3" onSubmit={editForm.handleSubmit((v) => update.mutate(v))}>
            <div className="grid gap-3 md:grid-cols-2">
              <FormField name="title" control={editForm.control} render={({ field }) => (
                <FormItem>
                  <FormLabel>Title</FormLabel>
                  <FormControl><Input {...field} /></FormControl>
                </FormItem>
              )} />
              <FormField name="postType" control={editForm.control} render={({ field }) => (
                <FormItem>
                  <FormLabel>Type</FormLabel>
                  <FormControl>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <SelectTrigger><SelectValue /></SelectTrigger>
                      <SelectContent>
                        <SelectItem value="GENERAL">General</SelectItem>
                        <SelectItem value="SUCCESS_STORY">Success story</SelectItem>
                        <SelectItem value="JOB_OPPORTUNITY">Job opportunity</SelectItem>
                        <SelectItem value="MENTORSHIP">Mentorship</SelectItem>
                        <SelectItem value="ANNOUNCEMENT">Announcement</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                </FormItem>
              )} />
            </div>
            <FormField name="imageUrl" control={editForm.control} render={({ field }) => (
              <FormItem>
                <FormLabel>Image URL</FormLabel>
                <FormControl><Input placeholder="https://..." {...field} /></FormControl>
              </FormItem>
            )} />
            <FormField name="content" control={editForm.control} render={({ field }) => (
              <FormItem>
                <FormLabel>Content</FormLabel>
                <FormControl><Textarea className="min-h-28" {...field} /></FormControl>
              </FormItem>
            )} />
            <div className="flex items-center justify-end"><Button type="submit" disabled={update.isPending}>{update.isPending ? "Saving..." : "Save changes"}</Button></div>
          </form>
        </Form>
      </div>

      <div className="rounded-lg border bg-card p-4">
        <div className="text-sm font-medium mb-2">Comments ({p._count?.comments ?? p.comments?.length ?? 0})</div>
        <div className="grid gap-3">
          {p.comments?.map((c: any) => (
            <CommentRow key={c.id} comment={c} onChanged={() => queryClient.invalidateQueries()} />
          ))}
        </div>
        <div className="mt-3">
          <Form {...form}>
            <form className="grid gap-2" onSubmit={form.handleSubmit((v) => addComment.mutate(v))}>
              <FormField name="content" control={form.control} render={({ field }) => (
                <FormItem>
                  <FormLabel>Add a comment</FormLabel>
                  <FormControl><Textarea className="min-h-20" placeholder="Write a comment..." {...field} /></FormControl>
                </FormItem>
              )} />
              <div className="flex items-center justify-end">
                <Button type="submit" disabled={addComment.isPending}>{addComment.isPending ? "Posting..." : "Comment"}</Button>
              </div>
            </form>
          </Form>
        </div>
      </div>
    </div>
  )
}

function CommentRow({ comment, onChanged }: { comment: any; onChanged: () => void }) {
  const queryClient = useQueryClient()
  const form = useForm<{ content: string }>({ defaultValues: { content: comment.content } })

  const update = useMutation({
    mutationFn: async (values: { content: string }) => client.posts.comments.update({ id: comment.id, content: values.content }),
    onSuccess: async () => { await queryClient.invalidateQueries(); onChanged() },
  })
  const del = useMutation({
    mutationFn: async () => client.posts.comments.delete({ id: comment.id }),
    onSuccess: async () => { await queryClient.invalidateQueries(); onChanged() },
  })

  return (
    <div className="rounded-md border p-3">
      <div className="text-xs text-muted-foreground">{new Date(comment.createdAt).toLocaleString()}</div>
      <Form {...form}>
        <form className="grid gap-2 mt-2" onSubmit={form.handleSubmit((v) => update.mutate(v))}>
          <FormField name="content" control={form.control} render={({ field }) => (
            <FormItem>
              <FormControl><Textarea className="min-h-16" {...field} /></FormControl>
            </FormItem>
          )} />
          <div className="flex items-center justify-end gap-2">
            <Button size="sm" variant="destructive" type="button" onClick={() => del.mutate()} disabled={del.isPending}>Delete</Button>
            <Button size="sm" type="submit" disabled={update.isPending}>{update.isPending ? "Saving..." : "Save"}</Button>
          </div>
        </form>
      </Form>
    </div>
  )
}


