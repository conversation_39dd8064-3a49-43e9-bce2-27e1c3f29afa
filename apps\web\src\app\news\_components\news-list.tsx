"use client"

import { useMemo, useState } from "react"
import { useQuery } from "@tanstack/react-query"
import { orpc } from "@/utils/orpc"
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Pagination, PaginationContent, PaginationItem, PaginationNext, PaginationPrevious } from "@/components/ui/pagination"
import Link from "next/link"
import { useForm } from "react-hook-form"

type FilterInput = {
  category?: "GENERAL" | "EVENTS" | "SUCCESS_STORIES" | "OPPORTUNITIES" | "ANNOUNCEMENTS"
  authorName?: string
}

export default function NewsList() {
  const [page, setPage] = useState(1)
  const [filters, setFilters] = useState<FilterInput>({})
  const form = useForm<FilterInput>({ defaultValues: filters })

  const list = useQuery(orpc.news.list.queryOptions({ input: { page, limit: 10, isPublished: true, ...filters } }))

  const totalPages = useMemo(() => {
    const total = (list.data as any)?.total ?? 0
    const limit = (list.data as any)?.limit ?? 10
    return Math.max(1, Math.ceil(total / limit))
  }, [list.data])

  return (
    <div className="space-y-4">
      <div className="rounded-lg border bg-card p-4">
        <Form {...form}>
          <form className="grid gap-4 md:grid-cols-4" onSubmit={form.handleSubmit((v) => { setPage(1); setFilters(v) })}>
            <FormField name="category" control={form.control} render={({ field }) => (
              <FormItem>
                <FormLabel>Category</FormLabel>
                <FormControl>
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger><SelectValue placeholder="All" /></SelectTrigger>
                    <SelectContent>
                      <SelectItem value="GENERAL">General</SelectItem>
                      <SelectItem value="EVENTS">Events</SelectItem>
                      <SelectItem value="SUCCESS_STORIES">Success stories</SelectItem>
                      <SelectItem value="OPPORTUNITIES">Opportunities</SelectItem>
                      <SelectItem value="ANNOUNCEMENTS">Announcements</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
              </FormItem>
            )} />
            <FormField name="authorName" control={form.control} render={({ field }) => (
              <FormItem>
                <FormLabel>Author</FormLabel>
                <FormControl><Input placeholder="Name" {...field} /></FormControl>
              </FormItem>
            )} />
            <div className="md:col-span-2 flex items-end justify-end">
              <Button type="submit">Apply</Button>
            </div>
          </form>
        </Form>
      </div>

      <div className="grid gap-3">
        {list.isLoading && (<div className="text-sm text-muted-foreground">Loading...</div>)}
        {(list.data as any)?.data?.map((a: any) => (
          <div key={a.id} className="rounded-lg border bg-card p-4">
            <div className="flex items-center justify-between">
              <div className="font-medium truncate">{a.title}</div>
              <div className="text-xs text-muted-foreground">{new Date(a.createdAt).toLocaleDateString()}</div>
            </div>
            <div className="text-sm text-muted-foreground truncate">{a.authorName} • {a.category}</div>
            {a.excerpt && <div className="mt-2 text-sm line-clamp-3">{a.excerpt}</div>}
            <div className="mt-3"><Button size="sm" asChild><Link href={`/news/${a.id}`}>Read</Link></Button></div>
          </div>
        ))}
      </div>

      <div className="flex items-center justify-end">
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious href="#" onClick={(e) => { e.preventDefault(); setPage((p) => Math.max(1, p - 1)) }} />
            </PaginationItem>
            <PaginationItem>
              <span className="text-sm text-muted-foreground px-3 py-2">Page {page} of {totalPages}</span>
            </PaginationItem>
            <PaginationItem>
              <PaginationNext href="#" onClick={(e) => { e.preventDefault(); setPage((p) => Math.min(totalPages, p + 1)) }} />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </div>
    </div>
  )
}


