{"name": "native", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"dev": "expo start --clear", "android": "expo run:android", "ios": "expo run:ios", "prebuild": "expo prebuild", "web": "expo start --web"}, "dependencies": {"@better-auth/expo": "^1.3.4", "@expo/vector-icons": "^14.1.0", "@orpc/client": "^1.7.10", "@orpc/server": "^1.7.10", "@orpc/tanstack-query": "^1.7.10", "@react-navigation/bottom-tabs": "^7.4.5", "@react-navigation/drawer": "^7.5.6", "@react-navigation/native": "^7.1.17", "@stardazed/streams-text-encoding": "^1.0.2", "@tanstack/react-form": "^1.19.0", "@tanstack/react-query": "^5.84.1", "@ungap/structured-clone": "^1.3.0", "better-auth": "^1.3.4", "expo": "^53.0.20", "expo-constants": "~17.1.7", "expo-linking": "~7.1.7", "expo-navigation-bar": "~4.2.7", "expo-router": "~5.1.4", "expo-secure-store": "~14.2.3", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.10", "expo-web-browser": "~14.2.0", "nativewind": "^4.1.23", "react": "19.1.1", "react-dom": "19.1.1", "react-native": "0.80.2", "react-native-gesture-handler": "~2.28.0", "react-native-reanimated": "~4.0.1", "react-native-safe-area-context": "5.6.0", "react-native-screens": "~4.13.1", "react-native-web": "^0.21.0"}, "devDependencies": {"@babel/core": "^7.28.0", "@types/react": "~19.1.9", "tailwindcss": "^3.4.0", "typescript": "~5.9.2"}, "private": true}