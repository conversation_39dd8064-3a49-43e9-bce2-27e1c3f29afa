import { z } from "zod";
import { protectedProcedure } from "../lib/orpc";
import { prisma } from "../lib/database";
import {
  createDonationSchema,
  recurringDonationSchema,
  idSchema,
} from "../lib/validation";
import { createPaginationResult, getPaginationParams } from "../lib/utils";

export const donationsRouter = {
  /**
   * Create donation with payment processing
   */
  create: protectedProcedure
    .input(createDonationSchema)
    .handler(async ({ input, context }) => {
      const donorProfile = await prisma.alumniProfile.findUnique({
        where: { userId: context.user!.id },
        select: { id: true },
      });
      if (!donorProfile) throw new Error("Create your alumni profile first");
      const donation = await prisma.donation.create({
        data: {
          donorId: donorProfile.id,
          amount: input.amount,
          currency: input.currency || "ZAR",
          donationType: input.donationType,
          organizationId: input.organizationId,
          paymentMethod: input.paymentMethod,
          purpose: input.purpose,
          isAnonymous: input.isAnonymous ?? false,
        },
      });
      // Mock payment processing
      const payment = {
        status: "COMPLETED",
        transactionId: `tx_${Date.now()}`,
      } as const;
      await prisma.donation.update({
        where: { id: donation.id },
        data: { paymentStatus: "COMPLETED", transactionId: payment.transactionId },
      });
      return { donation: { ...donation, paymentStatus: "COMPLETED", transactionId: payment.transactionId }, payment };
    }),

  /**
   * Get user's donation history
   */
  myDonations: protectedProcedure
    .input(
      z.object({
        page: z.number().int().min(1).default(1),
        limit: z.number().int().min(1).max(100).default(20),
        organizationId: idSchema.optional(),
        donationType: z
          .enum(["ONE_TIME", "MONTHLY", "QUARTERLY", "ANNUAL"])
          .optional(),
        paymentStatus: z
          .enum(["PENDING", "COMPLETED", "FAILED", "REFUNDED", "CANCELLED"])
          .optional(),
        startDate: z.date().optional(),
        endDate: z.date().optional(),
        minAmount: z.number().positive().optional(),
        maxAmount: z.number().positive().optional(),
      })
    )
    .handler(async ({ input, context }) => {
      const profile = await prisma.alumniProfile.findUnique({
        where: { userId: context.user!.id },
        select: { id: true },
      });
      if (!profile) throw new Error("Alumni profile not found");
      const { page, limit, skip } = getPaginationParams(input);
      const where: any = { donorId: profile.id };
      if (input.organizationId) where.organizationId = input.organizationId;
      if (input.donationType) where.donationType = input.donationType;
      if (input.paymentStatus) where.paymentStatus = input.paymentStatus;
      if (input.startDate || input.endDate) {
        where.createdAt = {} as any;
        if (input.startDate) where.createdAt.gte = input.startDate;
        if (input.endDate) where.createdAt.lte = input.endDate;
      }
      if (input.minAmount || input.maxAmount) {
        where.amount = {} as any;
        if (input.minAmount) where.amount.gte = input.minAmount as any;
        if (input.maxAmount) where.amount.lte = input.maxAmount as any;
      }
      const [data, total] = await Promise.all([
        prisma.donation.findMany({ where, orderBy: { createdAt: "desc" }, skip, take: limit }),
        prisma.donation.count({ where }),
      ]);
      return createPaginationResult(data, total, page, limit);
    }),

  /**
   * Get specific donation details
   */
  getById: protectedProcedure
    .input(z.object({ id: idSchema }))
    .handler(async ({ input, context }) => {
      const profile = await prisma.alumniProfile.findUnique({
        where: { userId: context.user!.id },
        select: { id: true },
      });
      if (!profile) throw new Error("Alumni profile not found");
      const donation = await prisma.donation.findFirst({
        where: { id: input.id, donorId: profile.id },
      });
      if (!donation) throw new Error("Donation not found");
      return donation;
    }),

  // Recurring donation management
  recurring: {
    /**
     * Set up recurring donation
     */
    setup: protectedProcedure
      .input(
        z.object({
          donationId: idSchema,
          donationType: z.enum(["MONTHLY", "QUARTERLY", "ANNUAL"]),
          nextDueDate: z.date(),
        })
      )
      .handler(async ({ input, context }) => {
        const profile = await prisma.alumniProfile.findUnique({ where: { userId: context.user!.id }, select: { id: true } });
        if (!profile) throw new Error("Alumni profile not found");
        const donation = await prisma.donation.findFirst({ where: { id: input.donationId, donorId: profile.id } });
        if (!donation) throw new Error("Donation not found");
        return prisma.donation.update({
          where: { id: input.donationId },
          data: { donationType: input.donationType, nextDueDate: input.nextDueDate, recurringId: input.donationId },
        });
      }),

    /**
     * Update recurring donation
     */
    update: protectedProcedure
      .input(
        z.object({
          recurringId: idSchema,
          donationType: z.enum(["MONTHLY", "QUARTERLY", "ANNUAL"]).optional(),
          nextDueDate: z.date().optional(),
        })
      )
      .handler(async ({ input, context }) => {
        const profile = await prisma.alumniProfile.findUnique({ where: { userId: context.user!.id }, select: { id: true } });
        if (!profile) throw new Error("Alumni profile not found");
        const donation = await prisma.donation.findFirst({ where: { id: input.recurringId, donorId: profile.id } });
        if (!donation) throw new Error("Recurring donation not found");
        const { recurringId, ...updateData } = input as any;
        return prisma.donation.update({ where: { id: input.recurringId }, data: updateData });
      }),

    /**
     * Cancel recurring donation
     */
    cancel: protectedProcedure
      .input(z.object({ recurringId: idSchema }))
      .handler(async ({ input, context }) => {
        const profile = await prisma.alumniProfile.findUnique({ where: { userId: context.user!.id }, select: { id: true } });
        if (!profile) throw new Error("Alumni profile not found");
        const donation = await prisma.donation.findFirst({ where: { id: input.recurringId, donorId: profile.id } });
        if (!donation) throw new Error("Recurring donation not found");
        await prisma.donation.update({ where: { id: input.recurringId }, data: { nextDueDate: null, recurringId: null } });
        return { success: true };
      }),
  },

  // Admin endpoints for organization donation management
  admin: {
    /**
     * Get organization donations (admin only)
     */
    getOrganizationDonations: protectedProcedure
      .input(
        z.object({
          page: z.number().int().min(1).default(1),
          limit: z.number().int().min(1).max(100).default(20),
          donationType: z
            .enum(["ONE_TIME", "MONTHLY", "QUARTERLY", "ANNUAL"])
            .optional(),
          paymentStatus: z
            .enum(["PENDING", "COMPLETED", "FAILED", "REFUNDED", "CANCELLED"])
            .optional(),
          startDate: z.date().optional(),
          endDate: z.date().optional(),
          minAmount: z.number().positive().optional(),
          maxAmount: z.number().positive().optional(),
        })
      )
      .handler(async ({ input, context }) => {
        const orgId = context.session?.session.activeOrganizationId;
        if (!orgId) throw new Error("Organization not set");
        const { page, limit, skip } = getPaginationParams(input);
        const where: any = { organizationId: orgId };
        if (input.donationType) where.donationType = input.donationType;
        if (input.paymentStatus) where.paymentStatus = input.paymentStatus;
        if (input.startDate || input.endDate) {
          where.createdAt = {} as any;
          if (input.startDate) where.createdAt.gte = input.startDate;
          if (input.endDate) where.createdAt.lte = input.endDate;
        }
        if (input.minAmount || input.maxAmount) {
          where.amount = {} as any;
          if (input.minAmount) where.amount.gte = input.minAmount as any;
          if (input.maxAmount) where.amount.lte = input.maxAmount as any;
        }
        const [data, total] = await Promise.all([
          prisma.donation.findMany({ where, orderBy: { createdAt: "desc" }, skip, take: limit }),
          prisma.donation.count({ where }),
        ]);
        return createPaginationResult(data, total, page, limit);
      }),

    /**
     * Get donation analytics (admin only)
     */
    getAnalytics: protectedProcedure.handler(async ({ context }) => {
      const orgId = context.session?.session.activeOrganizationId;
      if (!orgId) throw new Error("Organization not set");
      const [total, sumCompleted, recurring] = await Promise.all([
        prisma.donation.count({ where: { organizationId: orgId } }),
        prisma.donation.aggregate({
          where: { organizationId: orgId, paymentStatus: "COMPLETED" },
          _sum: { amount: true },
        }),
        prisma.donation.count({ where: { organizationId: orgId, NOT: { recurringId: null } } }),
      ]);
      return { total, totalCompletedAmount: sumCompleted._sum.amount, recurring };
    }),

    /**
     * Update payment status (admin only)
     */
    updatePaymentStatus: protectedProcedure
      .input(
        z.object({
          donationId: idSchema,
          status: z.enum([
            "PENDING",
            "COMPLETED",
            "FAILED",
            "REFUNDED",
            "CANCELLED",
          ]),
          transactionId: z.string().optional(),
        })
      )
      .handler(async ({ input }) => {
        return prisma.donation.update({
          where: { id: input.donationId },
          data: { paymentStatus: input.status, transactionId: input.transactionId ?? undefined },
        });
      }),

    /**
     * Process due recurring donations (admin only)
     */
    processRecurring: protectedProcedure.handler(async () => {
      // Mock: mark due recurring donations as completed and advance nextDueDate by 30 days
      const now = new Date();
      const due = await prisma.donation.findMany({ where: { nextDueDate: { lte: now } } });
      for (const d of due) {
        await prisma.donation.update({ where: { id: d.id }, data: { paymentStatus: "COMPLETED", nextDueDate: new Date(now.getTime() + 30*24*60*60*1000) } });
      }
      return { processed: due.length };
    }),
  },
};
