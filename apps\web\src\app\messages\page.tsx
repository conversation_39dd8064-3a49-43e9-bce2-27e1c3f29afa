"use client"

import { use<PERSON>em<PERSON>, useState } from "react"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { orpc, client } from "@/utils/orpc"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"

export default function MessagesPage() {
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null)
  const [page, setPage] = useState(1)
  const [message, setMessage] = useState("")
  const queryClient = useQueryClient()

  const me = useQuery(orpc.getUser.queryOptions())
  const conversations = useQuery(orpc.connections.messages.conversations.queryOptions({ input: { page: 1, limit: 30 } }))

  const threadOptions = orpc.connections.messages.getWithUser.queryOptions({ input: { userId: selectedUserId ?? "", page, limit: 20 } })
  const thread = useQuery({ ...threadOptions, enabled: !!selectedUserId })

  const sendMutation = useMutation({
    mutationFn: async () => client.connections.messages.send({ receiverId: selectedUserId!, content: message }),
    onSuccess: async () => {
      setMessage("")
      await queryClient.invalidateQueries()
    },
  })

  const canSend = !!selectedUserId && message.trim().length > 0

  return (
    <div className="container mx-auto px-4 py-4 space-y-4">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold">Messages</h1>
        <div className="flex items-center gap-2"></div>
      </div>

      <div className="grid gap-4 md:grid-cols-3">
        <div className="rounded-lg border bg-card p-3 md:col-span-1">
          <div className="text-sm font-medium mb-2 text-muted-foreground">Conversations</div>
          <div className="grid gap-2">
            {conversations.isLoading && (
              <div className="text-sm text-muted-foreground">Loading...</div>
            )}
            {(conversations.data as any)?.data?.map((c: any) => (
              <button
                key={c.userId}
                className={`w-full text-left rounded-md border px-3 py-2 transition-colors ${selectedUserId === c.userId ? "bg-accent" : "hover:bg-accent"}`}
                onClick={() => { setSelectedUserId(c.userId); setPage(1) }}
              >
                <div className="flex items-center gap-3">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={undefined} alt="User" />
                    <AvatarFallback>AL</AvatarFallback>
                  </Avatar>
                  <div className="min-w-0">
                    <div className="text-sm font-medium truncate">User {c.userId.slice(0, 6)}</div>
                    <div className="text-xs text-muted-foreground">{c.count} messages</div>
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>

        <div className="rounded-lg border bg-card p-3 md:col-span-2 flex flex-col min-h-[420px]">
          {!selectedUserId ? (
            <div className="text-sm text-muted-foreground">Select a conversation to start messaging.</div>
          ) : (
            <>
              <div className="text-sm font-medium mb-2">Conversation</div>
              <div className="flex-1 space-y-2 overflow-y-auto pr-1">
                {thread.isLoading && (
                  <div className="text-sm text-muted-foreground">Loading...</div>
                )}
                {(thread.data as any)?.data?.map((m: any) => (
                  <div key={m.id} className={`rounded-md border px-3 py-2 ${m.senderId === m.receiverId ? "" : ""}`}>
                    <div className="text-xs text-muted-foreground">{new Date(m.createdAt).toLocaleString()}</div>
                    <div className="text-sm whitespace-pre-wrap">{m.content}</div>
                  </div>
                ))}
              </div>
              <Separator className="my-2" />
              <form
                onSubmit={(e) => { e.preventDefault(); if (canSend) sendMutation.mutate() }}
                className="flex items-center gap-2"
              >
                <Input
                  placeholder="Type a message..."
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                />
                <Button type="submit" disabled={!canSend || sendMutation.isPending}>Send</Button>
              </form>
            </>
          )}
        </div>
      </div>
    </div>
  )
}

