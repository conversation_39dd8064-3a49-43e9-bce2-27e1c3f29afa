# PROTEC Alumni Platform - Backend API Documentation

## Overview

The PROTEC Alumni Platform backend provides a comprehensive REST API built with Hono, oRPC, and Prisma. The API supports multi-organization functionality with role-based access control and covers all aspects of alumni networking, content management, events, donations, and more.

## Base URL
```
http://localhost:3000/rpc
```

## Authentication

The API uses Better Auth for authentication with session-based authentication. Include the session cookie in requests to authenticated endpoints.

### Authentication Levels
- **Public**: No authentication required
- **Protected**: Requires valid user session
- **Admin**: Requires organization administrator role
- **Super Admin**: Requires super administrator role

## API Structure

All endpoints are organized under the `/rpc` prefix with the following structure:

```
/rpc/{router}/{endpoint}
```

## Core Routers

### 1. Profiles Router (`/rpc/profiles`)

Manages alumni profile information with privacy controls and advanced search.

#### Public Endpoints

**Search Profiles**
```http
POST /rpc/profiles/search
```
Search alumni profiles with filtering and privacy controls.

Request Body:
```json
{
  "search": "string (optional)",
  "graduationYear": "number (optional)",
  "programType": "string (optional)", 
  "industry": "string (optional)",
  "location": "string (optional)",
  "centerLocation": "string (optional)",
  "mentorshipOffered": "boolean (optional)",
  "mentorshipSought": "boolean (optional)",
  "organizationId": "string (optional)",
  "page": "number (default: 1)",
  "limit": "number (default: 20, max: 100)"
}
```

**Get Profile by ID**
```http
POST /rpc/profiles/getById
```
Get specific profile with privacy filtering.

Request Body:
```json
{
  "id": "string (required)"
}
```

#### Protected Endpoints

**Get Current User Profile**
```http
POST /rpc/profiles/getMe
```
Returns current user's complete profile.

**Create Profile**
```http
POST /rpc/profiles/create
```
Create alumni profile for current user.

Request Body:
```json
{
  "firstName": "string (required)",
  "lastName": "string (required)",
  "displayName": "string (optional)",
  "bio": "string (optional)",
  "profilePicture": "string (optional)",
  "graduationYear": "number (required)",
  "programType": "string (required)",
  "centerLocation": "string (required)",
  "achievements": "string (optional)",
  "currentPosition": "string (optional)",
  "currentCompany": "string (optional)",
  "industry": "string (optional)",
  "location": "string (optional)",
  "linkedInUrl": "string (optional)",
  "phoneNumber": "string (optional)",
  "mentorshipOffered": "boolean (default: false)",
  "mentorshipSought": "boolean (default: false)",
  "skillsOffered": "string (optional)",
  "skillsWanted": "string (optional)",
  "primaryOrganizationId": "string (optional)"
}
```

**Update Profile**
```http
POST /rpc/profiles/updateMe
```
Update current user's profile (partial update supported).

**Update Privacy Settings**
```http
POST /rpc/profiles/updatePrivacy
```
Update profile privacy settings.

Request Body:
```json
{
  "profileVisibility": "PUBLIC | ALUMNI_ONLY | PRIVATE",
  "showEmail": "boolean",
  "showPhone": "boolean", 
  "showLocation": "boolean"
}
```

**Find Mentorship Opportunities**
```http
POST /rpc/profiles/mentorship
```
Find mentorship opportunities based on skills.

Request Body:
```json
{
  "type": "offered | sought (default: offered)",
  "skills": "string (optional)",
  "page": "number (default: 1)",
  "limit": "number (default: 20)"
}
```

### 2. Connections Router (`/rpc/connections`)

Manages alumni connections and messaging system.

#### Protected Endpoints

**List Connections**
```http
POST /rpc/connections/list
```
Get user's connections with filtering.

**Send Connection Request**
```http
POST /rpc/connections/sendRequest
```
Send connection request to another alumni.

Request Body:
```json
{
  "requestedId": "string (required)",
  "message": "string (optional, max: 500)"
}
```

**Accept Connection Request**
```http
POST /rpc/connections/accept
```
Accept a connection request.

**Decline Connection Request**
```http
POST /rpc/connections/decline
```
Decline a connection request.

**Block User**
```http
POST /rpc/connections/block
```
Block a user connection.

**Remove Connection**
```http
POST /rpc/connections/remove
```
Remove an existing connection.

#### Messaging Endpoints

**Get Conversations**
```http
POST /rpc/connections/messages/conversations
```
Get list of conversations with unread counts.

**Get Messages with User**
```http
POST /rpc/connections/messages/getWithUser
```
Get message history with specific user.

**Send Message**
```http
POST /rpc/connections/messages/send
```
Send message to connected user.

Request Body:
```json
{
  "receiverId": "string (required)",
  "content": "string (required, max: 2000)"
}
```

### 3. Posts Router (`/rpc/posts`)

Manages user-generated content with engagement features.

#### Public Endpoints

**List Posts**
```http
POST /rpc/posts/list
```
Get posts with filtering and pagination.

Request Body:
```json
{
  "postType": "GENERAL | SUCCESS_STORY | JOB_OPPORTUNITY | MENTORSHIP | ANNOUNCEMENT (optional)",
  "organizationId": "string (optional)",
  "authorId": "string (optional)",
  "page": "number (default: 1)",
  "limit": "number (default: 20)"
}
```

**Get Post by ID**
```http
POST /rpc/posts/getById
```
Get specific post with engagement data.

#### Protected Endpoints

**Create Post**
```http
POST /rpc/posts/create
```
Create new post.

Request Body:
```json
{
  "title": "string (optional, max: 200)",
  "content": "string (required, max: 5000)",
  "imageUrl": "string (optional)",
  "postType": "GENERAL | SUCCESS_STORY | JOB_OPPORTUNITY | MENTORSHIP | ANNOUNCEMENT",
  "organizationId": "string (optional)"
}
```

**Update Post**
```http
POST /rpc/posts/update
```
Update own post.

**Delete Post**
```http
POST /rpc/posts/delete
```
Delete own post.

**Toggle Like**
```http
POST /rpc/posts/toggleLike
```
Like or unlike a post.

#### Comment Management

**Create Comment**
```http
POST /rpc/posts/comments/create
```
Add comment to post.

**Update Comment**
```http
POST /rpc/posts/comments/update
```
Update own comment.

**Delete Comment**
```http
POST /rpc/posts/comments/delete
```
Delete own comment.

### 4. Events Router (`/rpc/events`)

Manages events and registrations.

#### Public Endpoints

**List Events**
```http
POST /rpc/events/list
```
Get published events with filtering.

Request Body:
```json
{
  "eventType": "WORKSHOP | NETWORKING | CONFERENCE | WEBINAR | SOCIAL | FUNDRAISING | MENTORSHIP (optional)",
  "organizationId": "string (optional)",
  "isVirtual": "boolean (optional)",
  "startDate": "date (optional)",
  "endDate": "date (optional)",
  "page": "number (default: 1)",
  "limit": "number (default: 20)"
}
```

**Get Event by ID**
```http
POST /rpc/events/getById
```
Get specific event details.

#### Protected Endpoints

**Register for Event**
```http
POST /rpc/events/register
```
Register for an event.

**Update Registration**
```http
POST /rpc/events/updateRegistration
```
Update registration status.

**Cancel Registration**
```http
POST /rpc/events/cancelRegistration
```
Cancel event registration.

**Get My Registrations**
```http
POST /rpc/events/myRegistrations
```
Get user's event registrations.

#### Admin Endpoints

**Create Event**
```http
POST /rpc/events/admin/create
```
Create new event (admin only).

**Update Event**
```http
POST /rpc/events/admin/update
```
Update event (admin only).

**Get Event Registrations**
```http
POST /rpc/events/admin/getRegistrations
```
Get event registrations (admin only).

### 5. Donations Router (`/rpc/donations`)

Manages donations and payment processing.

#### Protected Endpoints

**Create Donation**
```http
POST /rpc/donations/create
```
Create donation with payment processing.

Request Body:
```json
{
  "amount": "number (required, positive)",
  "currency": "string (default: ZAR)",
  "donationType": "ONE_TIME | MONTHLY | QUARTERLY | ANNUAL",
  "organizationId": "string (required)",
  "purpose": "string (optional, max: 200)",
  "isAnonymous": "boolean (default: false)",
  "paymentMethod": "string (optional)"
}
```

**Get My Donations**
```http
POST /rpc/donations/myDonations
```
Get user's donation history with filtering.

#### Recurring Donations

**Setup Recurring Donation**
```http
POST /rpc/donations/recurring/setup
```
Set up recurring donation.

**Update Recurring Donation**
```http
POST /rpc/donations/recurring/update
```
Update recurring donation settings.

**Cancel Recurring Donation**
```http
POST /rpc/donations/recurring/cancel
```
Cancel recurring donation.

#### Admin Endpoints

**Get Organization Donations**
```http
POST /rpc/donations/admin/getOrganizationDonations
```
Get organization donations (admin only).

**Get Analytics**
```http
POST /rpc/donations/admin/getAnalytics
```
Get donation analytics (admin only).

### 6. News Router (`/rpc/news`)

Manages news articles and announcements.

#### Public Endpoints

**List News**
```http
POST /rpc/news/list
```
Get published news articles.

Request Body:
```json
{
  "category": "GENERAL | EVENTS | SUCCESS_STORIES | OPPORTUNITIES | ANNOUNCEMENTS (optional)",
  "organizationId": "string (optional)",
  "authorName": "string (optional)",
  "isPublished": "boolean (optional)",
  "page": "number (default: 1)",
  "limit": "number (default: 20)"
}
```

**Get Article by ID**
```http
POST /rpc/news/getById
```
Get specific news article.

**Get by Category**
```http
POST /rpc/news/getByCategory
```
Get articles by category.

**Get Recent Articles**
```http
POST /rpc/news/getRecent
```
Get recent articles.

**Get Featured Articles**
```http
POST /rpc/news/getFeatured
```
Get featured articles.

#### Admin Endpoints

**Create Article**
```http
POST /rpc/news/admin/create
```
Create news article (admin only).

**Update Article**
```http
POST /rpc/news/admin/update
```
Update news article (admin only).

**Toggle Publication**
```http
POST /rpc/news/admin/togglePublication
```
Publish/unpublish article (admin only).

### 7. Notifications Router (`/rpc/notifications`)

Manages user notifications.

#### Protected Endpoints

**List Notifications**
```http
POST /rpc/notifications/list
```
Get user notifications with filtering.

Request Body:
```json
{
  "type": "CONNECTION_REQUEST | CONNECTION_ACCEPTED | EVENT_REMINDER | EVENT_REGISTRATION | NEW_MESSAGE | POST_LIKE | POST_COMMENT | DONATION_CONFIRMATION | NEWS_ARTICLE | SYSTEM_ANNOUNCEMENT (optional)",
  "isRead": "boolean (optional)",
  "page": "number (default: 1)",
  "limit": "number (default: 20)"
}
```

**Mark as Read**
```http
POST /rpc/notifications/markRead
```
Mark notification as read.

**Mark All as Read**
```http
POST /rpc/notifications/markAllRead
```
Mark all notifications as read.

**Delete Notification**
```http
POST /rpc/notifications/delete
```
Delete notification.

**Get Unread Count**
```http
POST /rpc/notifications/unreadCount
```
Get unread notification count.

## Data Models

### Alumni Profile
```typescript
interface AlumniProfile {
  id: string;
  userId: string;
  firstName: string;
  lastName: string;
  displayName?: string;
  bio?: string;
  profilePicture?: string;
  graduationYear: number;
  programType: string;
  centerLocation: string;
  achievements?: string;
  currentPosition?: string;
  currentCompany?: string;
  industry?: string;
  location?: string;
  linkedInUrl?: string;
  phoneNumber?: string;
  mentorshipOffered: boolean;
  mentorshipSought: boolean;
  skillsOffered?: string;
  skillsWanted?: string;
  profileVisibility: "PUBLIC" | "ALUMNI_ONLY" | "PRIVATE";
  showEmail: boolean;
  showPhone: boolean;
  showLocation: boolean;
  primaryOrganizationId?: string;
  createdAt: Date;
  updatedAt: Date;
}
```

### Connection
```typescript
interface Connection {
  id: string;
  requesterId: string;
  requestedId: string;
  status: "PENDING" | "ACCEPTED" | "DECLINED" | "BLOCKED";
  requestedAt: Date;
  acceptedAt?: Date;
}
```

### Post
```typescript
interface Post {
  id: string;
  authorId: string;
  title?: string;
  content: string;
  imageUrl?: string;
  postType: "GENERAL" | "SUCCESS_STORY" | "JOB_OPPORTUNITY" | "MENTORSHIP" | "ANNOUNCEMENT";
  isPublished: boolean;
  organizationId?: string;
  createdAt: Date;
  updatedAt: Date;
}
```

### Event
```typescript
interface Event {
  id: string;
  title: string;
  description: string;
  eventType: "WORKSHOP" | "NETWORKING" | "CONFERENCE" | "WEBINAR" | "SOCIAL" | "FUNDRAISING" | "MENTORSHIP";
  location?: string;
  isVirtual: boolean;
  virtualLink?: string;
  startDateTime: Date;
  endDateTime: Date;
  maxAttendees?: number;
  imageUrl?: string;
  organizationId?: string;
  organizerId?: string;
  isPublished: boolean;
  createdAt: Date;
  updatedAt: Date;
}
```

### Donation
```typescript
interface Donation {
  id: string;
  donorId: string;
  amount: number;
  currency: string;
  donationType: "ONE_TIME" | "MONTHLY" | "QUARTERLY" | "ANNUAL";
  organizationId: string;
  paymentMethod?: string;
  transactionId?: string;
  paymentStatus: "PENDING" | "COMPLETED" | "FAILED" | "REFUNDED" | "CANCELLED";
  purpose?: string;
  isAnonymous: boolean;
  recurringId?: string;
  nextDueDate?: Date;
  createdAt: Date;
  updatedAt: Date;
}
```

## Error Handling

All endpoints return consistent error responses:

```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": {}, // Optional additional details
    "timestamp": "2024-01-01T00:00:00.000Z",
    "requestId": "req_123456789"
  }
}
```

### Common Error Codes
- `BAD_REQUEST`: Invalid input data
- `UNAUTHORIZED`: Authentication required
- `FORBIDDEN`: Insufficient permissions
- `NOT_FOUND`: Resource not found
- `CONFLICT`: Resource conflict (e.g., duplicate)
- `INTERNAL_SERVER_ERROR`: Server error

## Rate Limiting

API endpoints are rate limited to prevent abuse:
- Public endpoints: 100 requests per minute
- Protected endpoints: 200 requests per minute
- Admin endpoints: 500 requests per minute

## Pagination

List endpoints support pagination with the following parameters:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20, max: 100)

Response format:
```json
{
  "data": [...],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "totalPages": 5,
    "hasNext": true,
    "hasPrev": false
  }
}
```

## Organization Scoping

Many endpoints support organization scoping:
- Public content is visible to all users
- Organization content is visible to organization members
- Private content is visible only to the owner

## Privacy Controls

Profile visibility levels:
- `PUBLIC`: Visible to everyone
- `ALUMNI_ONLY`: Visible to authenticated alumni
- `PRIVATE`: Visible only to the profile owner

## Notification Types

The system generates automatic notifications for:
- Connection requests and acceptances
- New messages
- Event registrations and reminders
- Post likes and comments
- Donation confirmations
- News article publications
- System announcements

## Development

### Local Setup
1. Install dependencies: `pnpm install`
2. Set up database: `pnpm db:push`
3. Start development server: `pnpm dev:server`

### Environment Variables
```env
DATABASE_URL="mysql://user:password@localhost:3306/protec_alumni"
CORS_ORIGIN="http://localhost:3001"
```

### Testing
Run tests with: `pnpm test`

## Support

For API support and questions, please contact the development team or refer to the project documentation.