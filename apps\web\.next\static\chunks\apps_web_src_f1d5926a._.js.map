{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Work/protec/apps/web/src/components/loader.tsx"], "sourcesContent": ["import { Loader2 } from \"lucide-react\";\n\nexport default function Loader() {\n  return (\n    <div className=\"flex h-full items-center justify-center pt-8\">\n      <Loader2 className=\"animate-spin\" />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,oNAAA,CAAA,UAAO;YAAC,WAAU;;;;;;;;;;;AAGzB;KANwB", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Work/protec/apps/web/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,KAGoC;QAHpC,EACb,SAAS,EACT,GAAG,OAC8C,GAHpC;IAIb,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,qIAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 77, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Work/protec/apps/web/src/components/sign-in-form.tsx"], "sourcesContent": ["import { authClient } from \"@/lib/auth-client\";\nimport { useForm } from \"@tanstack/react-form\";\n  import { toast } from \"react-toastify\";\nimport z from \"zod\";\nimport Loader from \"./loader\";\nimport { Button } from \"./ui/button\";\nimport { Input } from \"./ui/input\";\nimport { Label } from \"./ui/label\";\nimport { useRouter } from \"next/navigation\";\n\nexport default function SignInForm({\n  onSwitchToSignUp,\n}: {\n  onSwitchToSignUp: () => void;\n}) {\n  const router = useRouter()\n  const { isPending } = authClient.useSession();\n\n  const form = useForm({\n    defaultValues: {\n      email: \"\",\n      password: \"\",\n    },\n    onSubmit: async ({ value }) => {\n      await authClient.signIn.email(\n        {\n          email: value.email,\n          password: value.password,\n        },\n        {\n          onSuccess: () => {\n            router.push(\"/dashboard\")\n            toast.success(\"Sign in successful\");\n          },\n          onError: (error) => {\n            toast.error(error.error.message);\n          },\n        },\n      );\n    },\n    validators: {\n      onSubmit: z.object({\n        email: z.email(\"Invalid email address\"),\n        password: z.string().min(8, \"Password must be at least 8 characters\"),\n      }),\n    },\n  });\n\n  if (isPending) {\n    return <Loader />;\n  }\n\n  return (\n    <div className=\"mx-auto w-full mt-10 max-w-md p-6\">\n      <h1 className=\"mb-6 text-center text-3xl font-bold\">Welcome Back</h1>\n\n      <form\n        onSubmit={(e) => {\n          e.preventDefault();\n          e.stopPropagation();\n          form.handleSubmit();\n        }}\n        className=\"space-y-4\"\n      >\n        <div>\n          <form.Field name=\"email\">\n            {(field) => (\n              <div className=\"space-y-2\">\n                <Label htmlFor={field.name}>Email</Label>\n                <Input\n                  id={field.name}\n                  name={field.name}\n                  type=\"email\"\n                  value={field.state.value}\n                  onBlur={field.handleBlur}\n                  onChange={(e) => field.handleChange(e.target.value)}\n                />\n                {field.state.meta.errors.map((error) => (\n                  <p key={error?.message} className=\"text-red-500\">\n                    {error?.message}\n                  </p>\n                ))}\n              </div>\n            )}\n          </form.Field>\n        </div>\n\n        <div>\n          <form.Field name=\"password\">\n            {(field) => (\n              <div className=\"space-y-2\">\n                <Label htmlFor={field.name}>Password</Label>\n                <Input\n                  id={field.name}\n                  name={field.name}\n                  type=\"password\"\n                  value={field.state.value}\n                  onBlur={field.handleBlur}\n                  onChange={(e) => field.handleChange(e.target.value)}\n                />\n                {field.state.meta.errors.map((error) => (\n                  <p key={error?.message} className=\"text-red-500\">\n                    {error?.message}\n                  </p>\n                ))}\n              </div>\n            )}\n          </form.Field>\n        </div>\n\n        <form.Subscribe>\n          {(state) => (\n            <Button\n              type=\"submit\"\n              className=\"w-full\"\n              disabled={!state.canSubmit || state.isSubmitting}\n            >\n              {state.isSubmitting ? \"Submitting...\" : \"Sign In\"}\n            </Button>\n          )}\n        </form.Subscribe>\n      </form>\n\n      <div className=\"mt-4 text-center\">\n        <Button\n          variant=\"link\"\n          onClick={onSwitchToSignUp}\n          className=\"text-indigo-600 hover:text-indigo-800\"\n        >\n          Need an account? Sign Up\n        </Button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACE;AACF;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AAEe,SAAS,WAAW,KAIlC;QAJkC,EACjC,gBAAgB,EAGjB,GAJkC;;IAKjC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,SAAS,EAAE,GAAG,8IAAA,CAAA,aAAU,CAAC,UAAU;IAE3C,MAAM,OAAO,CAAA,GAAA,wKAAA,CAAA,UAAO,AAAD,EAAE;QACnB,eAAe;YACb,OAAO;YACP,UAAU;QACZ;QACA,QAAQ;wCAAE;oBAAO,EAAE,KAAK,EAAE;gBACxB,MAAM,8IAAA,CAAA,aAAU,CAAC,MAAM,CAAC,KAAK,CAC3B;oBACE,OAAO,MAAM,KAAK;oBAClB,UAAU,MAAM,QAAQ;gBAC1B,GACA;oBACE,SAAS;oDAAE;4BACT,OAAO,IAAI,CAAC;4BACZ,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;wBAChB;;oBACA,OAAO;oDAAE,CAAC;4BACR,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,OAAO;wBACjC;;gBACF;YAEJ;;QACA,YAAY;YACV,UAAU,+IAAA,CAAA,UAAC,CAAC,MAAM,CAAC;gBACjB,OAAO,+IAAA,CAAA,UAAC,CAAC,KAAK,CAAC;gBACf,UAAU,+IAAA,CAAA,UAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;YAC9B;QACF;IACF;IAEA,IAAI,WAAW;QACb,qBAAO,6LAAC,8IAAA,CAAA,UAAM;;;;;IAChB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAAsC;;;;;;0BAEpD,6LAAC;gBACC,UAAU,CAAC;oBACT,EAAE,cAAc;oBAChB,EAAE,eAAe;oBACjB,KAAK,YAAY;gBACnB;gBACA,WAAU;;kCAEV,6LAAC;kCACC,cAAA,6LAAC,KAAK,KAAK;4BAAC,MAAK;sCACd,CAAC,sBACA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mJAAA,CAAA,QAAK;4CAAC,SAAS,MAAM,IAAI;sDAAE;;;;;;sDAC5B,6LAAC,mJAAA,CAAA,QAAK;4CACJ,IAAI,MAAM,IAAI;4CACd,MAAM,MAAM,IAAI;4CAChB,MAAK;4CACL,OAAO,MAAM,KAAK,CAAC,KAAK;4CACxB,QAAQ,MAAM,UAAU;4CACxB,UAAU,CAAC,IAAM,MAAM,YAAY,CAAC,EAAE,MAAM,CAAC,KAAK;;;;;;wCAEnD,MAAM,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,sBAC5B,6LAAC;gDAAuB,WAAU;0DAC/B,kBAAA,4BAAA,MAAO,OAAO;+CADT,kBAAA,4BAAA,MAAO,OAAO;;;;;;;;;;;;;;;;;;;;;kCAShC,6LAAC;kCACC,cAAA,6LAAC,KAAK,KAAK;4BAAC,MAAK;sCACd,CAAC,sBACA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mJAAA,CAAA,QAAK;4CAAC,SAAS,MAAM,IAAI;sDAAE;;;;;;sDAC5B,6LAAC,mJAAA,CAAA,QAAK;4CACJ,IAAI,MAAM,IAAI;4CACd,MAAM,MAAM,IAAI;4CAChB,MAAK;4CACL,OAAO,MAAM,KAAK,CAAC,KAAK;4CACxB,QAAQ,MAAM,UAAU;4CACxB,UAAU,CAAC,IAAM,MAAM,YAAY,CAAC,EAAE,MAAM,CAAC,KAAK;;;;;;wCAEnD,MAAM,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,sBAC5B,6LAAC;gDAAuB,WAAU;0DAC/B,kBAAA,4BAAA,MAAO,OAAO;+CADT,kBAAA,4BAAA,MAAO,OAAO;;;;;;;;;;;;;;;;;;;;;kCAShC,6LAAC,KAAK,SAAS;kCACZ,CAAC,sBACA,6LAAC,oJAAA,CAAA,SAAM;gCACL,MAAK;gCACL,WAAU;gCACV,UAAU,CAAC,MAAM,SAAS,IAAI,MAAM,YAAY;0CAE/C,MAAM,YAAY,GAAG,kBAAkB;;;;;;;;;;;;;;;;;0BAMhD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,oJAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,SAAS;oBACT,WAAU;8BACX;;;;;;;;;;;;;;;;;AAMT;GA5HwB;;QAKP,qIAAA,CAAA,YAAS;QACF,8IAAA,CAAA,aAAU,CAAC;QAEpB,wKAAA,CAAA,UAAO;;;KARE", "debugId": null}}, {"offset": {"line": 330, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Work/protec/apps/web/src/components/sign-up-form.tsx"], "sourcesContent": ["import { authClient } from \"@/lib/auth-client\";\nimport { useForm } from \"@tanstack/react-form\";\nimport { toast } from \"sonner\";\nimport z from \"zod\";\nimport Loader from \"./loader\";\nimport { Button } from \"./ui/button\";\nimport { Input } from \"./ui/input\";\nimport { Label } from \"./ui/label\";\nimport { useRouter } from \"next/navigation\";\n\nexport default function SignUpForm({\n  onSwitchToSignIn,\n}: {\n  onSwitchToSignIn: () => void;\n}) {\n  const router = useRouter();\n  const { isPending } = authClient.useSession();\n\n  const form = useForm({\n    defaultValues: {\n      email: \"\",\n      password: \"\",\n      name: \"\",\n    },\n    onSubmit: async ({ value }) => {\n      await authClient.signUp.email(\n        {\n          email: value.email,\n          password: value.password,\n          name: value.name,\n        },\n        {\n          onSuccess: () => {\n            router.push(\"/dashboard\");\n            toast.success(\"Sign up successful\");\n          },\n          onError: (error) => {\n            toast.error(error.error.message);\n          },\n        },\n      );\n    },\n    validators: {\n      onSubmit: z.object({\n        name: z.string().min(2, \"Name must be at least 2 characters\"),\n        email: z.email(\"Invalid email address\"),\n        password: z.string().min(8, \"Password must be at least 8 characters\"),\n      }),\n    },\n  });\n\n  if (isPending) {\n    return <Loader />;\n  }\n\n  return (\n    <div className=\"mx-auto w-full mt-10 max-w-md p-6\">\n      <h1 className=\"mb-6 text-center text-3xl font-bold\">Create Account</h1>\n\n      <form\n        onSubmit={(e) => {\n          e.preventDefault();\n          e.stopPropagation();\n          form.handleSubmit();\n        }}\n        className=\"space-y-4\"\n      >\n        <div>\n          <form.Field name=\"name\">\n            {(field) => (\n              <div className=\"space-y-2\">\n                <Label htmlFor={field.name}>Name</Label>\n                <Input\n                  id={field.name}\n                  name={field.name}\n                  value={field.state.value}\n                  onBlur={field.handleBlur}\n                  onChange={(e) => field.handleChange(e.target.value)}\n                />\n                {field.state.meta.errors.map((error) => (\n                  <p key={error?.message} className=\"text-red-500\">\n                    {error?.message}\n                  </p>\n                ))}\n              </div>\n            )}\n          </form.Field>\n        </div>\n\n        <div>\n          <form.Field name=\"email\">\n            {(field) => (\n              <div className=\"space-y-2\">\n                <Label htmlFor={field.name}>Email</Label>\n                <Input\n                  id={field.name}\n                  name={field.name}\n                  type=\"email\"\n                  value={field.state.value}\n                  onBlur={field.handleBlur}\n                  onChange={(e) => field.handleChange(e.target.value)}\n                />\n                {field.state.meta.errors.map((error) => (\n                  <p key={error?.message} className=\"text-red-500\">\n                    {error?.message}\n                  </p>\n                ))}\n              </div>\n            )}\n          </form.Field>\n        </div>\n\n        <div>\n          <form.Field name=\"password\">\n            {(field) => (\n              <div className=\"space-y-2\">\n                <Label htmlFor={field.name}>Password</Label>\n                <Input\n                  id={field.name}\n                  name={field.name}\n                  type=\"password\"\n                  value={field.state.value}\n                  onBlur={field.handleBlur}\n                  onChange={(e) => field.handleChange(e.target.value)}\n                />\n                {field.state.meta.errors.map((error) => (\n                  <p key={error?.message} className=\"text-red-500\">\n                    {error?.message}\n                  </p>\n                ))}\n              </div>\n            )}\n          </form.Field>\n        </div>\n\n        <form.Subscribe>\n          {(state) => (\n            <Button\n              type=\"submit\"\n              className=\"w-full\"\n              disabled={!state.canSubmit || state.isSubmitting}\n            >\n              {state.isSubmitting ? \"Submitting...\" : \"Sign Up\"}\n            </Button>\n          )}\n        </form.Subscribe>\n      </form>\n\n      <div className=\"mt-4 text-center\">\n        <Button\n          variant=\"link\"\n          onClick={onSwitchToSignIn}\n          className=\"text-indigo-600 hover:text-indigo-800\"\n        >\n          Already have an account? Sign In\n        </Button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AAEe,SAAS,WAAW,KAIlC;QAJkC,EACjC,gBAAgB,EAGjB,GAJkC;;IAKjC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,SAAS,EAAE,GAAG,8IAAA,CAAA,aAAU,CAAC,UAAU;IAE3C,MAAM,OAAO,CAAA,GAAA,wKAAA,CAAA,UAAO,AAAD,EAAE;QACnB,eAAe;YACb,OAAO;YACP,UAAU;YACV,MAAM;QACR;QACA,QAAQ;wCAAE;oBAAO,EAAE,KAAK,EAAE;gBACxB,MAAM,8IAAA,CAAA,aAAU,CAAC,MAAM,CAAC,KAAK,CAC3B;oBACE,OAAO,MAAM,KAAK;oBAClB,UAAU,MAAM,QAAQ;oBACxB,MAAM,MAAM,IAAI;gBAClB,GACA;oBACE,SAAS;oDAAE;4BACT,OAAO,IAAI,CAAC;4BACZ,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;wBAChB;;oBACA,OAAO;oDAAE,CAAC;4BACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,OAAO;wBACjC;;gBACF;YAEJ;;QACA,YAAY;YACV,UAAU,+IAAA,CAAA,UAAC,CAAC,MAAM,CAAC;gBACjB,MAAM,+IAAA,CAAA,UAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;gBACxB,OAAO,+IAAA,CAAA,UAAC,CAAC,KAAK,CAAC;gBACf,UAAU,+IAAA,CAAA,UAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;YAC9B;QACF;IACF;IAEA,IAAI,WAAW;QACb,qBAAO,6LAAC,8IAAA,CAAA,UAAM;;;;;IAChB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAAsC;;;;;;0BAEpD,6LAAC;gBACC,UAAU,CAAC;oBACT,EAAE,cAAc;oBAChB,EAAE,eAAe;oBACjB,KAAK,YAAY;gBACnB;gBACA,WAAU;;kCAEV,6LAAC;kCACC,cAAA,6LAAC,KAAK,KAAK;4BAAC,MAAK;sCACd,CAAC,sBACA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mJAAA,CAAA,QAAK;4CAAC,SAAS,MAAM,IAAI;sDAAE;;;;;;sDAC5B,6LAAC,mJAAA,CAAA,QAAK;4CACJ,IAAI,MAAM,IAAI;4CACd,MAAM,MAAM,IAAI;4CAChB,OAAO,MAAM,KAAK,CAAC,KAAK;4CACxB,QAAQ,MAAM,UAAU;4CACxB,UAAU,CAAC,IAAM,MAAM,YAAY,CAAC,EAAE,MAAM,CAAC,KAAK;;;;;;wCAEnD,MAAM,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,sBAC5B,6LAAC;gDAAuB,WAAU;0DAC/B,kBAAA,4BAAA,MAAO,OAAO;+CADT,kBAAA,4BAAA,MAAO,OAAO;;;;;;;;;;;;;;;;;;;;;kCAShC,6LAAC;kCACC,cAAA,6LAAC,KAAK,KAAK;4BAAC,MAAK;sCACd,CAAC,sBACA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mJAAA,CAAA,QAAK;4CAAC,SAAS,MAAM,IAAI;sDAAE;;;;;;sDAC5B,6LAAC,mJAAA,CAAA,QAAK;4CACJ,IAAI,MAAM,IAAI;4CACd,MAAM,MAAM,IAAI;4CAChB,MAAK;4CACL,OAAO,MAAM,KAAK,CAAC,KAAK;4CACxB,QAAQ,MAAM,UAAU;4CACxB,UAAU,CAAC,IAAM,MAAM,YAAY,CAAC,EAAE,MAAM,CAAC,KAAK;;;;;;wCAEnD,MAAM,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,sBAC5B,6LAAC;gDAAuB,WAAU;0DAC/B,kBAAA,4BAAA,MAAO,OAAO;+CADT,kBAAA,4BAAA,MAAO,OAAO;;;;;;;;;;;;;;;;;;;;;kCAShC,6LAAC;kCACC,cAAA,6LAAC,KAAK,KAAK;4BAAC,MAAK;sCACd,CAAC,sBACA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mJAAA,CAAA,QAAK;4CAAC,SAAS,MAAM,IAAI;sDAAE;;;;;;sDAC5B,6LAAC,mJAAA,CAAA,QAAK;4CACJ,IAAI,MAAM,IAAI;4CACd,MAAM,MAAM,IAAI;4CAChB,MAAK;4CACL,OAAO,MAAM,KAAK,CAAC,KAAK;4CACxB,QAAQ,MAAM,UAAU;4CACxB,UAAU,CAAC,IAAM,MAAM,YAAY,CAAC,EAAE,MAAM,CAAC,KAAK;;;;;;wCAEnD,MAAM,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,sBAC5B,6LAAC;gDAAuB,WAAU;0DAC/B,kBAAA,4BAAA,MAAO,OAAO;+CADT,kBAAA,4BAAA,MAAO,OAAO;;;;;;;;;;;;;;;;;;;;;kCAShC,6LAAC,KAAK,SAAS;kCACZ,CAAC,sBACA,6LAAC,oJAAA,CAAA,SAAM;gCACL,MAAK;gCACL,WAAU;gCACV,UAAU,CAAC,MAAM,SAAS,IAAI,MAAM,YAAY;0CAE/C,MAAM,YAAY,GAAG,kBAAkB;;;;;;;;;;;;;;;;;0BAMhD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,oJAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,SAAS;oBACT,WAAU;8BACX;;;;;;;;;;;;;;;;;AAMT;GArJwB;;QAKP,qIAAA,CAAA,YAAS;QACF,8IAAA,CAAA,aAAU,CAAC;QAEpB,wKAAA,CAAA,UAAO;;;KARE", "debugId": null}}, {"offset": {"line": 635, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Work/protec/apps/web/src/app/login/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport SignInForm from \"@/components/sign-in-form\";\nimport SignUpForm from \"@/components/sign-up-form\";\nimport { useState } from \"react\";\n\n\nexport default function LoginPage() {\n  const [showSignIn, setShowSignIn] = useState(false);\n\n  return showSignIn ? (\n    <SignInForm onSwitchToSignUp={() => setShowSignIn(false)} />\n  ) : (\n    <SignUpForm onSwitchToSignIn={() => setShowSignIn(true)} />\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,OAAO,2BACL,6LAAC,0JAAA,CAAA,UAAU;QAAC,kBAAkB,IAAM,cAAc;;;;;6BAElD,6LAAC,0JAAA,CAAA,UAAU;QAAC,kBAAkB,IAAM,cAAc;;;;;;AAEtD;GARwB;KAAA", "debugId": null}}]}