"use client"

import { useEffect } from "react"
import { useForm } from "react-hook-form"
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"

export type DirectoryFilter = {
  search?: string
  graduationYear?: number
  programType?: string
  industry?: string
  location?: string
  centerLocation?: string
  mentorshipOffered?: boolean
  mentorshipSought?: boolean
}

export function SearchFilters({ value, onChange }: { value: DirectoryFilter; onChange: (v: DirectoryFilter) => void }) {
  const form = useForm<DirectoryFilter>({ defaultValues: value })

  useEffect(() => {
    form.reset(value)
  }, [value])

  function submitLocal(data: DirectoryFilter) {
    onChange({ ...data, graduationYear: data.graduationYear ? Number(data.graduationYear) : undefined })
  }

  return (
    <div className="rounded-lg border bg-card p-4">
      <Form {...form}>
        <form className="grid gap-4" onSubmit={form.handleSubmit(submitLocal)}>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            <FormField name="search" control={form.control} render={({ field }) => (
              <FormItem>
                <FormLabel>Search</FormLabel>
                <FormControl><Input placeholder="Name, skill, company..." {...field} /></FormControl>
              </FormItem>
            )} />
            <FormField name="graduationYear" control={form.control} render={({ field }) => (
              <FormItem>
                <FormLabel>Graduation year</FormLabel>
                <FormControl>
                  <Input type="number" min={1980} max={new Date().getFullYear()} placeholder="e.g. 2018" {...field} />
                </FormControl>
              </FormItem>
            )} />
            <FormField name="programType" control={form.control} render={({ field }) => (
              <FormItem>
                <FormLabel>Program type</FormLabel>
                <FormControl><Input placeholder="e.g. Engineering" {...field} /></FormControl>
              </FormItem>
            )} />
          </div>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            <FormField name="industry" control={form.control} render={({ field }) => (
              <FormItem>
                <FormLabel>Industry</FormLabel>
                <FormControl><Input placeholder="e.g. Technology" {...field} /></FormControl>
              </FormItem>
            )} />
            <FormField name="location" control={form.control} render={({ field }) => (
              <FormItem>
                <FormLabel>Location</FormLabel>
                <FormControl><Input placeholder="e.g. Johannesburg" {...field} /></FormControl>
              </FormItem>
            )} />
            <FormField name="centerLocation" control={form.control} render={({ field }) => (
              <FormItem>
                <FormLabel>Center location</FormLabel>
                <FormControl><Input placeholder="e.g. Soweto" {...field} /></FormControl>
              </FormItem>
            )} />
          </div>
          <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
            <FormField name="mentorshipOffered" control={form.control} render={({ field }) => (
              <FormItem>
                <FormLabel>Offers mentorship</FormLabel>
                <FormControl><div className="flex items-center gap-3"><Switch checked={!!field.value} onCheckedChange={field.onChange} /></div></FormControl>
              </FormItem>
            )} />
            <FormField name="mentorshipSought" control={form.control} render={({ field }) => (
              <FormItem>
                <FormLabel>Seeks mentorship</FormLabel>
                <FormControl><div className="flex items-center gap-3"><Switch checked={!!field.value} onCheckedChange={field.onChange} /></div></FormControl>
              </FormItem>
            )} />
          </div>

          <div className="flex items-center justify-end">
            <Button type="submit">Apply filters</Button>
          </div>
        </form>
      </Form>
    </div>
  )
}

export default SearchFilters


