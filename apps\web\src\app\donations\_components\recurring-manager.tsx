"use client"

import { useState } from "react"
import { useMutation } from "@tanstack/react-query"
import { client } from "@/utils/orpc"
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { useForm } from "react-hook-form"

type SetupInput = {
  donationId: string
  donationType: "MONTHLY" | "QUARTERLY" | "ANNUAL"
  nextDueDate: string
}

type UpdateInput = {
  recurringId: string
  donationType?: "MONTHLY" | "QUARTERLY" | "ANNUAL"
  nextDueDate?: string
}

export default function RecurringManager() {
  const setupForm = useForm<SetupInput>({ defaultValues: { donationId: "", donationType: "MONTHLY", nextDueDate: new Date().toISOString().slice(0, 10) } })
  const updateForm = useForm<UpdateInput>({ defaultValues: { recurringId: "" } })

  const setup = useMutation({
    mutationFn: async (v: SetupInput) => client.donations.recurring.setup({ donationId: v.donationId, donationType: v.donationType, nextDueDate: new Date(v.nextDueDate) }),
  })
  const update = useMutation({
    mutationFn: async (v: UpdateInput) => client.donations.recurring.update({ recurringId: v.recurringId, donationType: v.donationType, nextDueDate: v.nextDueDate ? new Date(v.nextDueDate) : undefined }),
  })
  const cancel = useMutation({ mutationFn: async (recurringId: string) => client.donations.recurring.cancel({ recurringId }) })

  return (
    <div className="grid gap-4 md:grid-cols-2">
      <div className="rounded-lg border bg-card p-4">
        <div className="text-sm font-medium text-muted-foreground mb-2">Set up recurring</div>
        <Form {...setupForm}>
          <form className="grid gap-3" onSubmit={setupForm.handleSubmit((v) => setup.mutate(v))}>
            <FormField name="donationId" control={setupForm.control} render={({ field }) => (
              <FormItem>
                <FormLabel>Base Donation ID</FormLabel>
                <FormControl><Input {...field} placeholder="don_..." /></FormControl>
              </FormItem>
            )} />
            <FormField name="donationType" control={setupForm.control} render={({ field }) => (
              <FormItem>
                <FormLabel>Cycle</FormLabel>
                <FormControl>
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger><SelectValue /></SelectTrigger>
                    <SelectContent>
                      <SelectItem value="MONTHLY">Monthly</SelectItem>
                      <SelectItem value="QUARTERLY">Quarterly</SelectItem>
                      <SelectItem value="ANNUAL">Annual</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
              </FormItem>
            )} />
            <FormField name="nextDueDate" control={setupForm.control} render={({ field }) => (
              <FormItem>
                <FormLabel>Next due date</FormLabel>
                <FormControl><Input type="date" value={field.value} onChange={field.onChange} /></FormControl>
              </FormItem>
            )} />
            <div className="flex items-center justify-end"><Button type="submit" disabled={setup.isPending}>{setup.isPending ? "Saving..." : "Setup"}</Button></div>
          </form>
        </Form>
      </div>

      <div className="rounded-lg border bg-card p-4">
        <div className="text-sm font-medium text-muted-foreground mb-2">Manage recurring</div>
        <Form {...updateForm}>
          <form className="grid gap-3" onSubmit={updateForm.handleSubmit((v) => update.mutate(v))}>
            <FormField name="recurringId" control={updateForm.control} render={({ field }) => (
              <FormItem>
                <FormLabel>Recurring ID</FormLabel>
                <FormControl><Input {...field} placeholder="don_..." /></FormControl>
              </FormItem>
            )} />
            <FormField name="donationType" control={updateForm.control} render={({ field }) => (
              <FormItem>
                <FormLabel>New cycle (optional)</FormLabel>
                <FormControl>
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger><SelectValue placeholder="Keep current" /></SelectTrigger>
                    <SelectContent>
                      <SelectItem value="MONTHLY">Monthly</SelectItem>
                      <SelectItem value="QUARTERLY">Quarterly</SelectItem>
                      <SelectItem value="ANNUAL">Annual</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
              </FormItem>
            )} />
            <FormField name="nextDueDate" control={updateForm.control} render={({ field }) => (
              <FormItem>
                <FormLabel>New next due (optional)</FormLabel>
                <FormControl><Input type="date" value={field.value} onChange={field.onChange} /></FormControl>
              </FormItem>
            )} />
            <div className="flex items-center justify-end gap-2">
              <Button type="button" variant="destructive" onClick={() => { const id = updateForm.getValues("recurringId"); if (id) cancel.mutate(id) }} disabled={cancel.isPending}>Cancel</Button>
              <Button type="submit" disabled={update.isPending}>{update.isPending ? "Saving..." : "Update"}</Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  )
}


