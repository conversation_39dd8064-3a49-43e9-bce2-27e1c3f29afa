"use client"

import { useState } from "react"
import { useMutation } from "@tanstack/react-query"
import { client } from "@/utils/orpc"
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { useForm } from "react-hook-form"

type InviteInput = {
  email: string
  role: string
}

export default function OrgInvites() {
  const form = useForm<InviteInput>({ defaultValues: { email: "", role: "member" } })
  const invite = useMutation({
    mutationFn: async (v: InviteInput) => client.privateData() as any,
  })

  return (
    <div className="rounded-lg border bg-card p-4">
      <div className="text-sm font-medium text-muted-foreground mb-2">Invite member</div>
      <Form {...form}>
        <form className="grid gap-4 md:grid-cols-3" onSubmit={form.handleSubmit((v) => invite.mutate(v))}>
          <FormField name="email" control={form.control} render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl><Input type="email" {...field} /></FormControl>
            </FormItem>
          )} />
          <FormField name="role" control={form.control} render={({ field }) => (
            <FormItem>
              <FormLabel>Role</FormLabel>
              <FormControl>
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger><SelectValue /></SelectTrigger>
                  <SelectContent>
                    <SelectItem value="member">Member</SelectItem>
                    <SelectItem value="moderator">Moderator</SelectItem>
                    <SelectItem value="admin">Admin</SelectItem>
                  </SelectContent>
                </Select>
              </FormControl>
            </FormItem>
          )} />
          <div className="flex items-end justify-end"><Button type="submit" disabled={invite.isPending}>{invite.isPending ? "Sending..." : "Send invite"}</Button></div>
        </form>
      </Form>
    </div>
  )
}


