# Project Structure

## Monorepo Organization
```
protec/
├── apps/                    # Main applications
│   ├── web/                # Next.js web application
│   ├── native/             # React Native/Expo mobile app
│   └── server/             # Hono API server
├── packages/               # Shared packages (if any)
├── .kiro/                  # Kiro IDE configuration
├── biome.json              # Code formatting/linting config
├── turbo.json              # Turborepo configuration
├── pnpm-workspace.yaml     # pnpm workspace definition
└── package.json            # Root package.json with scripts
```

## Application Structure

### Web App (`apps/web/`)
```
apps/web/
├── src/
│   ├── app/               # Next.js App Router pages
│   ├── components/        # React components
│   ├── lib/              # Utility libraries
│   └── utils/            # Helper functions
├── public/               # Static assets
├── components.json       # shadcn/ui configuration
├── next.config.ts        # Next.js configuration
└── tailwind.config.js    # TailwindCSS configuration
```

### Server App (`apps/server/`)
```
apps/server/
├── src/
│   ├── lib/              # Shared utilities
│   ├── routers/          # oRPC route definitions
│   └── index.ts          # Server entry point
├── prisma/               # Database schema and migrations
│   ├── schema/           # Prisma schema files
│   └── generated/        # Generated Prisma client
└── .env                  # Environment variables
```

### Native App (`apps/native/`)
```
apps/native/
├── app/                  # Expo Router pages
├── components/           # React Native components
├── assets/              # Images, fonts, etc.
└── app.json             # Expo configuration
```

## Key Conventions

### File Naming
- Use kebab-case for directories and files
- React components use PascalCase
- Utility functions use camelCase

### Import Organization
- External dependencies first
- Internal imports grouped by type
- Relative imports last
- Automatic organization via Biome

### Environment Configuration
- `.env` files in each app directory
- `.env.example` templates provided
- Database connection in `apps/server/.env`

### Database Schema
- Prisma schema in `apps/server/prisma/schema/`
- Migrations managed through Prisma CLI
- Generated client in `prisma/generated/`

### Shared Code
- Cross-platform utilities in appropriate app directories
- oRPC types shared automatically between client and server
- Component patterns consistent between web and native where possible