"use client"

import { useState } from "react"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { orpc, client } from "@/utils/orpc"
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { useForm } from "react-hook-form"

type ArticleInput = {
  title: string
  content: string
  excerpt?: string
  imageUrl?: string
  authorName: string
  category: "GENERAL" | "EVENTS" | "SUCCESS_STORIES" | "OPPORTUNITIES" | "ANNOUNCEMENTS"
}

export default function NewsAdmin() {
  const queryClient = useQueryClient()
  const form = useForm<ArticleInput>({ defaultValues: { title: "", content: "", excerpt: "", imageUrl: "", authorName: "", category: "GENERAL" } })

  const create = useMutation({
    mutationFn: async (v: ArticleInput) => client.news.admin.create({ ...v }),
    onSuccess: async () => { await queryClient.invalidateQueries(); form.reset({ title: "", content: "", excerpt: "", imageUrl: "", authorName: "", category: "GENERAL" }) },
  })

  const drafts = useQuery(orpc.news.admin.getOrganizationArticles.queryOptions({ input: { page: 1, limit: 10 } }))
  const togglePublication = useMutation({
    mutationFn: async ({ id, isPublished }: { id: string; isPublished: boolean }) => client.news.admin.togglePublication({ articleId: id, isPublished }),
    onSuccess: async () => queryClient.invalidateQueries(),
  })

  return (
    <div className="space-y-4">
      <div className="rounded-lg border bg-card p-4">
        <div className="text-sm font-medium text-muted-foreground mb-3">Admin: Create Article</div>
        <Form {...form}>
          <form className="grid gap-4" onSubmit={form.handleSubmit((v) => create.mutate(v))}>
            <div className="grid gap-4 md:grid-cols-2">
              <FormField name="title" control={form.control} render={({ field }) => (
                <FormItem>
                  <FormLabel>Title</FormLabel>
                  <FormControl><Input {...field} /></FormControl>
                </FormItem>
              )} />
              <FormField name="authorName" control={form.control} render={({ field }) => (
                <FormItem>
                  <FormLabel>Author</FormLabel>
                  <FormControl><Input {...field} /></FormControl>
                </FormItem>
              )} />
            </div>
            <FormField name="category" control={form.control} render={({ field }) => (
              <FormItem>
                <FormLabel>Category</FormLabel>
                <FormControl>
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger><SelectValue /></SelectTrigger>
                    <SelectContent>
                      <SelectItem value="GENERAL">General</SelectItem>
                      <SelectItem value="EVENTS">Events</SelectItem>
                      <SelectItem value="SUCCESS_STORIES">Success stories</SelectItem>
                      <SelectItem value="OPPORTUNITIES">Opportunities</SelectItem>
                      <SelectItem value="ANNOUNCEMENTS">Announcements</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
              </FormItem>
            )} />
            <FormField name="excerpt" control={form.control} render={({ field }) => (
              <FormItem>
                <FormLabel>Excerpt (optional)</FormLabel>
                <FormControl><Input {...field} /></FormControl>
              </FormItem>
            )} />
            <FormField name="imageUrl" control={form.control} render={({ field }) => (
              <FormItem>
                <FormLabel>Image URL (optional)</FormLabel>
                <FormControl><Input {...field} placeholder="https://..." /></FormControl>
              </FormItem>
            )} />
            <FormField name="content" control={form.control} render={({ field }) => (
              <FormItem>
                <FormLabel>Content</FormLabel>
                <FormControl><Textarea className="min-h-32" {...field} /></FormControl>
              </FormItem>
            )} />
            <div className="flex items-center justify-end">
              <Button type="submit" disabled={create.isPending}>{create.isPending ? "Publishing..." : "Publish"}</Button>
            </div>
          </form>
        </Form>
      </div>

      <div className="rounded-lg border bg-card p-4">
        <div className="text-sm font-medium text-muted-foreground mb-3">Admin: Articles</div>
        <div className="grid gap-3">
          {drafts.isLoading && (<div className="text-sm text-muted-foreground">Loading...</div>)}
          {(drafts.data as any)?.data?.map((a: any) => (
            <div key={a.id} className="rounded-md border p-3 flex items-center justify-between">
              <div className="min-w-0">
                <div className="font-medium truncate">{a.title}</div>
                <div className="text-xs text-muted-foreground">{a.category}</div>
              </div>
              <div className="flex items-center gap-2">
                <Button size="sm" variant="outline" onClick={() => togglePublication.mutate({ id: a.id, isPublished: !a.isPublished })}>{a.isPublished ? "Unpublish" : "Publish"}</Button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}


