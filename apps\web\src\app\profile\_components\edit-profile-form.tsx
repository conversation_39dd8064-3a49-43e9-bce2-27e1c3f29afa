"use client"

import { useEffect, useMemo, useState } from "react"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { orpc, client } from "@/utils/orpc"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { useForm } from "react-hook-form"

type UpdateAlumniProfileInput = Partial<{
  firstName: string
  lastName: string
  displayName?: string
  bio?: string
  profilePicture?: string
  graduationYear: number
  programType: string
  centerLocation: string
  achievements?: string
  currentPosition?: string
  currentCompany?: string
  industry?: string
  location?: string
  linkedInUrl?: string
  phoneNumber?: string
  mentorshipOffered?: boolean
  mentorshipSought?: boolean
  skillsOffered?: string
  skillsWanted?: string
  primaryOrganizationId?: string
}>

export function EditProfileForm() {
  const queryClient = useQueryClient()
  const me = useQuery(orpc.profiles.getMe.queryOptions())
  const [submitting, setSubmitting] = useState(false)

  const form = useForm<UpdateAlumniProfileInput>({ defaultValues: {} })

  useEffect(() => {
    if (me.data) {
      const { id, userId, createdAt, updatedAt, ...rest } = me.data as any
      form.reset({ ...rest })
    }
  }, [me.data])

  const years = useMemo(() => {
    const current = new Date().getFullYear()
    const list: number[] = []
    for (let y = current; y >= 1980; y--) list.push(y)
    return list
  }, [])

  const updateMutation = useMutation({
    mutationFn: async (data: UpdateAlumniProfileInput) => client.profiles.updateMe(data),
    onSuccess: async () => {
      await Promise.all([queryClient.invalidateQueries()])
    },
  })

  async function onSubmit(values: UpdateAlumniProfileInput) {
    setSubmitting(true)
    try {
      await updateMutation.mutateAsync({
        ...values,
        graduationYear: values.graduationYear ? Number(values.graduationYear) : undefined,
      })
    } finally {
      setSubmitting(false)
    }
  }

  if (me.isLoading) return null
  if (!me.data) return null

  return (
    <div className="space-y-4">
      <div className="rounded-lg border bg-card p-4">
        <Form {...form}>
          <form className="space-y-6" onSubmit={form.handleSubmit(onSubmit)}>
            <section className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div className="space-y-4">
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <FormField name="firstName" control={form.control} render={({ field }) => (
                    <FormItem>
                      <FormLabel>First name</FormLabel>
                      <FormControl><Input {...field} /></FormControl>
                      <FormMessage />
                    </FormItem>
                  )} />
                  <FormField name="lastName" control={form.control} render={({ field }) => (
                    <FormItem>
                      <FormLabel>Last name</FormLabel>
                      <FormControl><Input {...field} /></FormControl>
                      <FormMessage />
                    </FormItem>
                  )} />
                </div>
                <FormField name="displayName" control={form.control} render={({ field }) => (
                  <FormItem>
                    <FormLabel>Display name</FormLabel>
                    <FormControl><Input {...field} /></FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
                <FormField name="bio" control={form.control} render={({ field }) => (
                  <FormItem>
                    <FormLabel>Bio</FormLabel>
                    <FormControl><Textarea className="min-h-24" {...field} /></FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
              </div>
              <div className="space-y-4">
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <FormField name="graduationYear" control={form.control} render={({ field }) => (
                    <FormItem>
                      <FormLabel>Graduation year</FormLabel>
                      <FormControl>
                        <Select onValueChange={(val) => field.onChange(Number(val))} value={field.value ? String(field.value) : undefined}>
                          <SelectTrigger><SelectValue placeholder="Select year" /></SelectTrigger>
                          <SelectContent>
                            {years.map((y) => (
                              <SelectItem key={y} value={String(y)}>{y}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )} />
                  <FormField name="programType" control={form.control} render={({ field }) => (
                    <FormItem>
                      <FormLabel>Program type</FormLabel>
                      <FormControl><Input {...field} /></FormControl>
                      <FormMessage />
                    </FormItem>
                  )} />
                </div>
                <FormField name="centerLocation" control={form.control} render={({ field }) => (
                  <FormItem>
                    <FormLabel>Center location</FormLabel>
                    <FormControl><Input {...field} /></FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
                <FormField name="achievements" control={form.control} render={({ field }) => (
                  <FormItem>
                    <FormLabel>Achievements</FormLabel>
                    <FormControl><Textarea className="min-h-20" {...field} /></FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
              </div>
            </section>

            <section className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div className="space-y-4">
                <FormField name="currentPosition" control={form.control} render={({ field }) => (
                  <FormItem>
                    <FormLabel>Current position</FormLabel>
                    <FormControl><Input {...field} /></FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <FormField name="currentCompany" control={form.control} render={({ field }) => (
                    <FormItem>
                      <FormLabel>Company</FormLabel>
                      <FormControl><Input {...field} /></FormControl>
                      <FormMessage />
                    </FormItem>
                  )} />
                  <FormField name="industry" control={form.control} render={({ field }) => (
                    <FormItem>
                      <FormLabel>Industry</FormLabel>
                      <FormControl><Input {...field} /></FormControl>
                      <FormMessage />
                    </FormItem>
                  )} />
                </div>
                <FormField name="location" control={form.control} render={({ field }) => (
                  <FormItem>
                    <FormLabel>Location</FormLabel>
                    <FormControl><Input {...field} /></FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
              </div>
              <div className="space-y-4">
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <FormField name="linkedInUrl" control={form.control} render={({ field }) => (
                    <FormItem>
                      <FormLabel>LinkedIn URL</FormLabel>
                      <FormControl><Input {...field} /></FormControl>
                      <FormMessage />
                    </FormItem>
                  )} />
                  <FormField name="phoneNumber" control={form.control} render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone number</FormLabel>
                      <FormControl><Input {...field} /></FormControl>
                      <FormMessage />
                    </FormItem>
                  )} />
                </div>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <FormField name="mentorshipOffered" control={form.control} render={({ field }) => (
                    <FormItem>
                      <FormLabel>Offering mentorship</FormLabel>
                      <FormControl><div className="flex items-center gap-3"><Switch checked={!!field.value} onCheckedChange={field.onChange} /></div></FormControl>
                      <FormMessage />
                    </FormItem>
                  )} />
                  <FormField name="mentorshipSought" control={form.control} render={({ field }) => (
                    <FormItem>
                      <FormLabel>Seeking mentorship</FormLabel>
                      <FormControl><div className="flex items-center gap-3"><Switch checked={!!field.value} onCheckedChange={field.onChange} /></div></FormControl>
                      <FormMessage />
                    </FormItem>
                  )} />
                </div>
                <FormField name="skillsOffered" control={form.control} render={({ field }) => (
                  <FormItem>
                    <FormLabel>Skills offered</FormLabel>
                    <FormControl><Textarea className="min-h-20" {...field} /></FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
                <FormField name="skillsWanted" control={form.control} render={({ field }) => (
                  <FormItem>
                    <FormLabel>Skills wanted</FormLabel>
                    <FormControl><Textarea className="min-h-20" {...field} /></FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
              </div>
            </section>

            <div className="flex items-center justify-end gap-2">
              <Button type="submit" disabled={submitting}>{submitting ? "Saving..." : "Save changes"}</Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  )
}

export default EditProfileForm


