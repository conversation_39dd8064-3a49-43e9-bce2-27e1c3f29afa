import { os } from "@orpc/server";
import type { Context } from "./context";

export const o = os.$context<Context>();

export const publicProcedure = o;

/**
 * Middleware that requires user authentication
 */
const requireAuth = o.middleware(async ({ context, next }) => {
  if (!context.session?.user) {
    throw new Error("Unauthorized");
  }

  return next({
    context: {
      ...context,
      user: context.session.user,
      organizationId: context.session.session.activeOrganizationId
    },
  });
});

export const protectedProcedure = publicProcedure.use(requireAuth);
