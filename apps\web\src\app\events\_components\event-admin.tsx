"use client"

import { use<PERSON>em<PERSON>, useState } from "react"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { orpc, client } from "@/utils/orpc"
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Button } from "@/components/ui/button"
import { useForm } from "react-hook-form"

type EventInput = {
  title: string
  description: string
  eventType: "WORKSHOP" | "NETWORKING" | "CONFERENCE" | "WEBINAR" | "SOCIAL" | "FUNDRAISING" | "MENTORSHIP"
  location?: string
  isVirtual?: boolean
  virtualLink?: string
  startDateTime: string
  endDateTime: string
  maxAttendees?: number
  imageUrl?: string
}

export default function EventAdmin() {
  const queryClient = useQueryClient()
  const form = useForm<EventInput>({
    defaultValues: {
      title: "",
      description: "",
      eventType: "WORKSHOP",
      location: "",
      isVirtual: false,
      virtualLink: "",
      startDateTime: new Date().toISOString().slice(0, 16),
      endDateTime: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString().slice(0, 16),
      maxAttendees: undefined,
      imageUrl: "",
    },
  })

  const create = useMutation({
    mutationFn: async (v: EventInput) => client.events.admin.create({
      title: v.title,
      description: v.description,
      eventType: v.eventType,
      location: v.location || undefined,
      isVirtual: !!v.isVirtual,
      virtualLink: v.virtualLink || undefined,
      startDateTime: new Date(v.startDateTime),
      endDateTime: new Date(v.endDateTime),
      maxAttendees: v.maxAttendees ? Number(v.maxAttendees) : undefined,
      imageUrl: v.imageUrl || undefined,
    }),
    onSuccess: async () => { await queryClient.invalidateQueries(); form.reset() },
  })

  return (
    <div className="rounded-lg border bg-card p-4 space-y-4">
      <div className="text-sm font-medium text-muted-foreground">Admin: Create Event</div>
      <Form {...form}>
        <form className="grid gap-4" onSubmit={form.handleSubmit((v) => create.mutate(v))}>
          <div className="grid gap-4 md:grid-cols-2">
            <FormField name="title" control={form.control} render={({ field }) => (
              <FormItem>
                <FormLabel>Title</FormLabel>
                <FormControl><Input {...field} placeholder="Event title" /></FormControl>
              </FormItem>
            )} />
            <FormField name="eventType" control={form.control} render={({ field }) => (
              <FormItem>
                <FormLabel>Type</FormLabel>
                <FormControl>
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger><SelectValue /></SelectTrigger>
                    <SelectContent>
                      <SelectItem value="WORKSHOP">Workshop</SelectItem>
                      <SelectItem value="NETWORKING">Networking</SelectItem>
                      <SelectItem value="CONFERENCE">Conference</SelectItem>
                      <SelectItem value="WEBINAR">Webinar</SelectItem>
                      <SelectItem value="SOCIAL">Social</SelectItem>
                      <SelectItem value="FUNDRAISING">Fundraising</SelectItem>
                      <SelectItem value="MENTORSHIP">Mentorship</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
              </FormItem>
            )} />
          </div>
          <FormField name="description" control={form.control} render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl><Textarea className="min-h-24" {...field} /></FormControl>
            </FormItem>
          )} />
          <div className="grid gap-4 md:grid-cols-2">
            <FormField name="location" control={form.control} render={({ field }) => (
              <FormItem>
                <FormLabel>Location</FormLabel>
                <FormControl><Input {...field} placeholder="Venue or city" /></FormControl>
              </FormItem>
            )} />
            <FormField name="isVirtual" control={form.control} render={({ field }) => (
              <FormItem>
                <FormLabel>Virtual</FormLabel>
                <FormControl><div className="flex items-center gap-3"><Switch checked={!!field.value} onCheckedChange={field.onChange} /></div></FormControl>
              </FormItem>
            )} />
          </div>
          <div className="grid gap-4 md:grid-cols-2">
            <FormField name="virtualLink" control={form.control} render={({ field }) => (
              <FormItem>
                <FormLabel>Virtual link</FormLabel>
                <FormControl><Input {...field} placeholder="https://..." /></FormControl>
              </FormItem>
            )} />
            <FormField name="maxAttendees" control={form.control} render={({ field }) => (
              <FormItem>
                <FormLabel>Max attendees</FormLabel>
                <FormControl><Input type="number" {...field} /></FormControl>
              </FormItem>
            )} />
          </div>
          <div className="grid gap-4 md:grid-cols-2">
            <FormField name="startDateTime" control={form.control} render={({ field }) => (
              <FormItem>
                <FormLabel>Start</FormLabel>
                <FormControl><Input type="datetime-local" value={field.value} onChange={field.onChange} /></FormControl>
              </FormItem>
            )} />
            <FormField name="endDateTime" control={form.control} render={({ field }) => (
              <FormItem>
                <FormLabel>End</FormLabel>
                <FormControl><Input type="datetime-local" value={field.value} onChange={field.onChange} /></FormControl>
              </FormItem>
            )} />
          </div>
          <div className="flex items-center justify-end">
            <Button type="submit" disabled={create.isPending}>{create.isPending ? "Creating..." : "Create event"}</Button>
          </div>
        </form>
      </Form>
    </div>
  )
}


