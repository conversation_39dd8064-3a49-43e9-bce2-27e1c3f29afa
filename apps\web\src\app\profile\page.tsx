"use client"

import { useQuery } from "@tanstack/react-query"
import { orpc } from "@/utils/orpc"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { CreateProfileForm, EditProfileForm, PrivacySettingsForm, DeleteProfile, ProfileSummary } from "./_components"

export default function ProfilePage() {
  const exists = useQuery(orpc.profiles.checkExists.queryOptions())

  return (
    <div className="container mx-auto px-4 py-4 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold">My Profile</h1>
        <div className="flex items-center gap-2"></div>
      </div>

      {!exists.isLoading && !exists.data?.exists ? (
        <div className="space-y-4">
          <div className="rounded-lg border bg-card p-4">
            <div className="text-sm text-muted-foreground">You don’t have an alumni profile yet. Create one to connect with the PROTEC community.</div>
          </div>
          <CreateProfileForm />
        </div>
      ) : (
        <Tabs defaultValue="overview" className="w-full">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="edit">Edit</TabsTrigger>
            <TabsTrigger value="privacy">Privacy</TabsTrigger>
            <TabsTrigger value="danger">Danger</TabsTrigger>
          </TabsList>
          <TabsContent value="overview" className="space-y-4">
            <ProfileSummary />
          </TabsContent>
          <TabsContent value="edit" className="space-y-4">
            <EditProfileForm />
          </TabsContent>
          <TabsContent value="privacy" className="space-y-4">
            <PrivacySettingsForm />
          </TabsContent>
          <TabsContent value="danger" className="space-y-4">
            <DeleteProfile />
          </TabsContent>
        </Tabs>
      )}
    </div>
  )
}

