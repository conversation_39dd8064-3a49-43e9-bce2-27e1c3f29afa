
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `EventRegistration` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model EventRegistration
 * 
 */
export type EventRegistrationModel = runtime.Types.Result.DefaultSelection<Prisma.$EventRegistrationPayload>

export type AggregateEventRegistration = {
  _count: EventRegistrationCountAggregateOutputType | null
  _min: EventRegistrationMinAggregateOutputType | null
  _max: EventRegistrationMaxAggregateOutputType | null
}

export type EventRegistrationMinAggregateOutputType = {
  id: string | null
  eventId: string | null
  attendeeId: string | null
  status: $Enums.RegistrationStatus | null
  registeredAt: Date | null
}

export type EventRegistrationMaxAggregateOutputType = {
  id: string | null
  eventId: string | null
  attendeeId: string | null
  status: $Enums.RegistrationStatus | null
  registeredAt: Date | null
}

export type EventRegistrationCountAggregateOutputType = {
  id: number
  eventId: number
  attendeeId: number
  status: number
  registeredAt: number
  _all: number
}


export type EventRegistrationMinAggregateInputType = {
  id?: true
  eventId?: true
  attendeeId?: true
  status?: true
  registeredAt?: true
}

export type EventRegistrationMaxAggregateInputType = {
  id?: true
  eventId?: true
  attendeeId?: true
  status?: true
  registeredAt?: true
}

export type EventRegistrationCountAggregateInputType = {
  id?: true
  eventId?: true
  attendeeId?: true
  status?: true
  registeredAt?: true
  _all?: true
}

export type EventRegistrationAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which EventRegistration to aggregate.
   */
  where?: Prisma.EventRegistrationWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of EventRegistrations to fetch.
   */
  orderBy?: Prisma.EventRegistrationOrderByWithRelationInput | Prisma.EventRegistrationOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.EventRegistrationWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` EventRegistrations from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` EventRegistrations.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned EventRegistrations
  **/
  _count?: true | EventRegistrationCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: EventRegistrationMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: EventRegistrationMaxAggregateInputType
}

export type GetEventRegistrationAggregateType<T extends EventRegistrationAggregateArgs> = {
      [P in keyof T & keyof AggregateEventRegistration]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateEventRegistration[P]>
    : Prisma.GetScalarType<T[P], AggregateEventRegistration[P]>
}




export type EventRegistrationGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.EventRegistrationWhereInput
  orderBy?: Prisma.EventRegistrationOrderByWithAggregationInput | Prisma.EventRegistrationOrderByWithAggregationInput[]
  by: Prisma.EventRegistrationScalarFieldEnum[] | Prisma.EventRegistrationScalarFieldEnum
  having?: Prisma.EventRegistrationScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: EventRegistrationCountAggregateInputType | true
  _min?: EventRegistrationMinAggregateInputType
  _max?: EventRegistrationMaxAggregateInputType
}

export type EventRegistrationGroupByOutputType = {
  id: string
  eventId: string
  attendeeId: string
  status: $Enums.RegistrationStatus
  registeredAt: Date
  _count: EventRegistrationCountAggregateOutputType | null
  _min: EventRegistrationMinAggregateOutputType | null
  _max: EventRegistrationMaxAggregateOutputType | null
}

type GetEventRegistrationGroupByPayload<T extends EventRegistrationGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<EventRegistrationGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof EventRegistrationGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], EventRegistrationGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], EventRegistrationGroupByOutputType[P]>
      }
    >
  >



export type EventRegistrationWhereInput = {
  AND?: Prisma.EventRegistrationWhereInput | Prisma.EventRegistrationWhereInput[]
  OR?: Prisma.EventRegistrationWhereInput[]
  NOT?: Prisma.EventRegistrationWhereInput | Prisma.EventRegistrationWhereInput[]
  id?: Prisma.StringFilter<"EventRegistration"> | string
  eventId?: Prisma.StringFilter<"EventRegistration"> | string
  attendeeId?: Prisma.StringFilter<"EventRegistration"> | string
  status?: Prisma.EnumRegistrationStatusFilter<"EventRegistration"> | $Enums.RegistrationStatus
  registeredAt?: Prisma.DateTimeFilter<"EventRegistration"> | Date | string
  event?: Prisma.XOR<Prisma.EventScalarRelationFilter, Prisma.EventWhereInput>
  attendee?: Prisma.XOR<Prisma.AlumniProfileScalarRelationFilter, Prisma.AlumniProfileWhereInput>
}

export type EventRegistrationOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  eventId?: Prisma.SortOrder
  attendeeId?: Prisma.SortOrder
  status?: Prisma.SortOrder
  registeredAt?: Prisma.SortOrder
  event?: Prisma.EventOrderByWithRelationInput
  attendee?: Prisma.AlumniProfileOrderByWithRelationInput
  _relevance?: Prisma.EventRegistrationOrderByRelevanceInput
}

export type EventRegistrationWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  eventId_attendeeId?: Prisma.EventRegistrationEventIdAttendeeIdCompoundUniqueInput
  AND?: Prisma.EventRegistrationWhereInput | Prisma.EventRegistrationWhereInput[]
  OR?: Prisma.EventRegistrationWhereInput[]
  NOT?: Prisma.EventRegistrationWhereInput | Prisma.EventRegistrationWhereInput[]
  eventId?: Prisma.StringFilter<"EventRegistration"> | string
  attendeeId?: Prisma.StringFilter<"EventRegistration"> | string
  status?: Prisma.EnumRegistrationStatusFilter<"EventRegistration"> | $Enums.RegistrationStatus
  registeredAt?: Prisma.DateTimeFilter<"EventRegistration"> | Date | string
  event?: Prisma.XOR<Prisma.EventScalarRelationFilter, Prisma.EventWhereInput>
  attendee?: Prisma.XOR<Prisma.AlumniProfileScalarRelationFilter, Prisma.AlumniProfileWhereInput>
}, "id" | "eventId_attendeeId">

export type EventRegistrationOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  eventId?: Prisma.SortOrder
  attendeeId?: Prisma.SortOrder
  status?: Prisma.SortOrder
  registeredAt?: Prisma.SortOrder
  _count?: Prisma.EventRegistrationCountOrderByAggregateInput
  _max?: Prisma.EventRegistrationMaxOrderByAggregateInput
  _min?: Prisma.EventRegistrationMinOrderByAggregateInput
}

export type EventRegistrationScalarWhereWithAggregatesInput = {
  AND?: Prisma.EventRegistrationScalarWhereWithAggregatesInput | Prisma.EventRegistrationScalarWhereWithAggregatesInput[]
  OR?: Prisma.EventRegistrationScalarWhereWithAggregatesInput[]
  NOT?: Prisma.EventRegistrationScalarWhereWithAggregatesInput | Prisma.EventRegistrationScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"EventRegistration"> | string
  eventId?: Prisma.StringWithAggregatesFilter<"EventRegistration"> | string
  attendeeId?: Prisma.StringWithAggregatesFilter<"EventRegistration"> | string
  status?: Prisma.EnumRegistrationStatusWithAggregatesFilter<"EventRegistration"> | $Enums.RegistrationStatus
  registeredAt?: Prisma.DateTimeWithAggregatesFilter<"EventRegistration"> | Date | string
}

export type EventRegistrationCreateInput = {
  id?: string
  status?: $Enums.RegistrationStatus
  registeredAt?: Date | string
  event: Prisma.EventCreateNestedOneWithoutRegistrationsInput
  attendee: Prisma.AlumniProfileCreateNestedOneWithoutEventRegistrationsInput
}

export type EventRegistrationUncheckedCreateInput = {
  id?: string
  eventId: string
  attendeeId: string
  status?: $Enums.RegistrationStatus
  registeredAt?: Date | string
}

export type EventRegistrationUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumRegistrationStatusFieldUpdateOperationsInput | $Enums.RegistrationStatus
  registeredAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  event?: Prisma.EventUpdateOneRequiredWithoutRegistrationsNestedInput
  attendee?: Prisma.AlumniProfileUpdateOneRequiredWithoutEventRegistrationsNestedInput
}

export type EventRegistrationUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  eventId?: Prisma.StringFieldUpdateOperationsInput | string
  attendeeId?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumRegistrationStatusFieldUpdateOperationsInput | $Enums.RegistrationStatus
  registeredAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type EventRegistrationCreateManyInput = {
  id?: string
  eventId: string
  attendeeId: string
  status?: $Enums.RegistrationStatus
  registeredAt?: Date | string
}

export type EventRegistrationUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumRegistrationStatusFieldUpdateOperationsInput | $Enums.RegistrationStatus
  registeredAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type EventRegistrationUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  eventId?: Prisma.StringFieldUpdateOperationsInput | string
  attendeeId?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumRegistrationStatusFieldUpdateOperationsInput | $Enums.RegistrationStatus
  registeredAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type EventRegistrationListRelationFilter = {
  every?: Prisma.EventRegistrationWhereInput
  some?: Prisma.EventRegistrationWhereInput
  none?: Prisma.EventRegistrationWhereInput
}

export type EventRegistrationOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type EventRegistrationOrderByRelevanceInput = {
  fields: Prisma.EventRegistrationOrderByRelevanceFieldEnum | Prisma.EventRegistrationOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type EventRegistrationEventIdAttendeeIdCompoundUniqueInput = {
  eventId: string
  attendeeId: string
}

export type EventRegistrationCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  eventId?: Prisma.SortOrder
  attendeeId?: Prisma.SortOrder
  status?: Prisma.SortOrder
  registeredAt?: Prisma.SortOrder
}

export type EventRegistrationMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  eventId?: Prisma.SortOrder
  attendeeId?: Prisma.SortOrder
  status?: Prisma.SortOrder
  registeredAt?: Prisma.SortOrder
}

export type EventRegistrationMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  eventId?: Prisma.SortOrder
  attendeeId?: Prisma.SortOrder
  status?: Prisma.SortOrder
  registeredAt?: Prisma.SortOrder
}

export type EventRegistrationCreateNestedManyWithoutAttendeeInput = {
  create?: Prisma.XOR<Prisma.EventRegistrationCreateWithoutAttendeeInput, Prisma.EventRegistrationUncheckedCreateWithoutAttendeeInput> | Prisma.EventRegistrationCreateWithoutAttendeeInput[] | Prisma.EventRegistrationUncheckedCreateWithoutAttendeeInput[]
  connectOrCreate?: Prisma.EventRegistrationCreateOrConnectWithoutAttendeeInput | Prisma.EventRegistrationCreateOrConnectWithoutAttendeeInput[]
  createMany?: Prisma.EventRegistrationCreateManyAttendeeInputEnvelope
  connect?: Prisma.EventRegistrationWhereUniqueInput | Prisma.EventRegistrationWhereUniqueInput[]
}

export type EventRegistrationUncheckedCreateNestedManyWithoutAttendeeInput = {
  create?: Prisma.XOR<Prisma.EventRegistrationCreateWithoutAttendeeInput, Prisma.EventRegistrationUncheckedCreateWithoutAttendeeInput> | Prisma.EventRegistrationCreateWithoutAttendeeInput[] | Prisma.EventRegistrationUncheckedCreateWithoutAttendeeInput[]
  connectOrCreate?: Prisma.EventRegistrationCreateOrConnectWithoutAttendeeInput | Prisma.EventRegistrationCreateOrConnectWithoutAttendeeInput[]
  createMany?: Prisma.EventRegistrationCreateManyAttendeeInputEnvelope
  connect?: Prisma.EventRegistrationWhereUniqueInput | Prisma.EventRegistrationWhereUniqueInput[]
}

export type EventRegistrationUpdateManyWithoutAttendeeNestedInput = {
  create?: Prisma.XOR<Prisma.EventRegistrationCreateWithoutAttendeeInput, Prisma.EventRegistrationUncheckedCreateWithoutAttendeeInput> | Prisma.EventRegistrationCreateWithoutAttendeeInput[] | Prisma.EventRegistrationUncheckedCreateWithoutAttendeeInput[]
  connectOrCreate?: Prisma.EventRegistrationCreateOrConnectWithoutAttendeeInput | Prisma.EventRegistrationCreateOrConnectWithoutAttendeeInput[]
  upsert?: Prisma.EventRegistrationUpsertWithWhereUniqueWithoutAttendeeInput | Prisma.EventRegistrationUpsertWithWhereUniqueWithoutAttendeeInput[]
  createMany?: Prisma.EventRegistrationCreateManyAttendeeInputEnvelope
  set?: Prisma.EventRegistrationWhereUniqueInput | Prisma.EventRegistrationWhereUniqueInput[]
  disconnect?: Prisma.EventRegistrationWhereUniqueInput | Prisma.EventRegistrationWhereUniqueInput[]
  delete?: Prisma.EventRegistrationWhereUniqueInput | Prisma.EventRegistrationWhereUniqueInput[]
  connect?: Prisma.EventRegistrationWhereUniqueInput | Prisma.EventRegistrationWhereUniqueInput[]
  update?: Prisma.EventRegistrationUpdateWithWhereUniqueWithoutAttendeeInput | Prisma.EventRegistrationUpdateWithWhereUniqueWithoutAttendeeInput[]
  updateMany?: Prisma.EventRegistrationUpdateManyWithWhereWithoutAttendeeInput | Prisma.EventRegistrationUpdateManyWithWhereWithoutAttendeeInput[]
  deleteMany?: Prisma.EventRegistrationScalarWhereInput | Prisma.EventRegistrationScalarWhereInput[]
}

export type EventRegistrationUncheckedUpdateManyWithoutAttendeeNestedInput = {
  create?: Prisma.XOR<Prisma.EventRegistrationCreateWithoutAttendeeInput, Prisma.EventRegistrationUncheckedCreateWithoutAttendeeInput> | Prisma.EventRegistrationCreateWithoutAttendeeInput[] | Prisma.EventRegistrationUncheckedCreateWithoutAttendeeInput[]
  connectOrCreate?: Prisma.EventRegistrationCreateOrConnectWithoutAttendeeInput | Prisma.EventRegistrationCreateOrConnectWithoutAttendeeInput[]
  upsert?: Prisma.EventRegistrationUpsertWithWhereUniqueWithoutAttendeeInput | Prisma.EventRegistrationUpsertWithWhereUniqueWithoutAttendeeInput[]
  createMany?: Prisma.EventRegistrationCreateManyAttendeeInputEnvelope
  set?: Prisma.EventRegistrationWhereUniqueInput | Prisma.EventRegistrationWhereUniqueInput[]
  disconnect?: Prisma.EventRegistrationWhereUniqueInput | Prisma.EventRegistrationWhereUniqueInput[]
  delete?: Prisma.EventRegistrationWhereUniqueInput | Prisma.EventRegistrationWhereUniqueInput[]
  connect?: Prisma.EventRegistrationWhereUniqueInput | Prisma.EventRegistrationWhereUniqueInput[]
  update?: Prisma.EventRegistrationUpdateWithWhereUniqueWithoutAttendeeInput | Prisma.EventRegistrationUpdateWithWhereUniqueWithoutAttendeeInput[]
  updateMany?: Prisma.EventRegistrationUpdateManyWithWhereWithoutAttendeeInput | Prisma.EventRegistrationUpdateManyWithWhereWithoutAttendeeInput[]
  deleteMany?: Prisma.EventRegistrationScalarWhereInput | Prisma.EventRegistrationScalarWhereInput[]
}

export type EventRegistrationCreateNestedManyWithoutEventInput = {
  create?: Prisma.XOR<Prisma.EventRegistrationCreateWithoutEventInput, Prisma.EventRegistrationUncheckedCreateWithoutEventInput> | Prisma.EventRegistrationCreateWithoutEventInput[] | Prisma.EventRegistrationUncheckedCreateWithoutEventInput[]
  connectOrCreate?: Prisma.EventRegistrationCreateOrConnectWithoutEventInput | Prisma.EventRegistrationCreateOrConnectWithoutEventInput[]
  createMany?: Prisma.EventRegistrationCreateManyEventInputEnvelope
  connect?: Prisma.EventRegistrationWhereUniqueInput | Prisma.EventRegistrationWhereUniqueInput[]
}

export type EventRegistrationUncheckedCreateNestedManyWithoutEventInput = {
  create?: Prisma.XOR<Prisma.EventRegistrationCreateWithoutEventInput, Prisma.EventRegistrationUncheckedCreateWithoutEventInput> | Prisma.EventRegistrationCreateWithoutEventInput[] | Prisma.EventRegistrationUncheckedCreateWithoutEventInput[]
  connectOrCreate?: Prisma.EventRegistrationCreateOrConnectWithoutEventInput | Prisma.EventRegistrationCreateOrConnectWithoutEventInput[]
  createMany?: Prisma.EventRegistrationCreateManyEventInputEnvelope
  connect?: Prisma.EventRegistrationWhereUniqueInput | Prisma.EventRegistrationWhereUniqueInput[]
}

export type EventRegistrationUpdateManyWithoutEventNestedInput = {
  create?: Prisma.XOR<Prisma.EventRegistrationCreateWithoutEventInput, Prisma.EventRegistrationUncheckedCreateWithoutEventInput> | Prisma.EventRegistrationCreateWithoutEventInput[] | Prisma.EventRegistrationUncheckedCreateWithoutEventInput[]
  connectOrCreate?: Prisma.EventRegistrationCreateOrConnectWithoutEventInput | Prisma.EventRegistrationCreateOrConnectWithoutEventInput[]
  upsert?: Prisma.EventRegistrationUpsertWithWhereUniqueWithoutEventInput | Prisma.EventRegistrationUpsertWithWhereUniqueWithoutEventInput[]
  createMany?: Prisma.EventRegistrationCreateManyEventInputEnvelope
  set?: Prisma.EventRegistrationWhereUniqueInput | Prisma.EventRegistrationWhereUniqueInput[]
  disconnect?: Prisma.EventRegistrationWhereUniqueInput | Prisma.EventRegistrationWhereUniqueInput[]
  delete?: Prisma.EventRegistrationWhereUniqueInput | Prisma.EventRegistrationWhereUniqueInput[]
  connect?: Prisma.EventRegistrationWhereUniqueInput | Prisma.EventRegistrationWhereUniqueInput[]
  update?: Prisma.EventRegistrationUpdateWithWhereUniqueWithoutEventInput | Prisma.EventRegistrationUpdateWithWhereUniqueWithoutEventInput[]
  updateMany?: Prisma.EventRegistrationUpdateManyWithWhereWithoutEventInput | Prisma.EventRegistrationUpdateManyWithWhereWithoutEventInput[]
  deleteMany?: Prisma.EventRegistrationScalarWhereInput | Prisma.EventRegistrationScalarWhereInput[]
}

export type EventRegistrationUncheckedUpdateManyWithoutEventNestedInput = {
  create?: Prisma.XOR<Prisma.EventRegistrationCreateWithoutEventInput, Prisma.EventRegistrationUncheckedCreateWithoutEventInput> | Prisma.EventRegistrationCreateWithoutEventInput[] | Prisma.EventRegistrationUncheckedCreateWithoutEventInput[]
  connectOrCreate?: Prisma.EventRegistrationCreateOrConnectWithoutEventInput | Prisma.EventRegistrationCreateOrConnectWithoutEventInput[]
  upsert?: Prisma.EventRegistrationUpsertWithWhereUniqueWithoutEventInput | Prisma.EventRegistrationUpsertWithWhereUniqueWithoutEventInput[]
  createMany?: Prisma.EventRegistrationCreateManyEventInputEnvelope
  set?: Prisma.EventRegistrationWhereUniqueInput | Prisma.EventRegistrationWhereUniqueInput[]
  disconnect?: Prisma.EventRegistrationWhereUniqueInput | Prisma.EventRegistrationWhereUniqueInput[]
  delete?: Prisma.EventRegistrationWhereUniqueInput | Prisma.EventRegistrationWhereUniqueInput[]
  connect?: Prisma.EventRegistrationWhereUniqueInput | Prisma.EventRegistrationWhereUniqueInput[]
  update?: Prisma.EventRegistrationUpdateWithWhereUniqueWithoutEventInput | Prisma.EventRegistrationUpdateWithWhereUniqueWithoutEventInput[]
  updateMany?: Prisma.EventRegistrationUpdateManyWithWhereWithoutEventInput | Prisma.EventRegistrationUpdateManyWithWhereWithoutEventInput[]
  deleteMany?: Prisma.EventRegistrationScalarWhereInput | Prisma.EventRegistrationScalarWhereInput[]
}

export type EnumRegistrationStatusFieldUpdateOperationsInput = {
  set?: $Enums.RegistrationStatus
}

export type EventRegistrationCreateWithoutAttendeeInput = {
  id?: string
  status?: $Enums.RegistrationStatus
  registeredAt?: Date | string
  event: Prisma.EventCreateNestedOneWithoutRegistrationsInput
}

export type EventRegistrationUncheckedCreateWithoutAttendeeInput = {
  id?: string
  eventId: string
  status?: $Enums.RegistrationStatus
  registeredAt?: Date | string
}

export type EventRegistrationCreateOrConnectWithoutAttendeeInput = {
  where: Prisma.EventRegistrationWhereUniqueInput
  create: Prisma.XOR<Prisma.EventRegistrationCreateWithoutAttendeeInput, Prisma.EventRegistrationUncheckedCreateWithoutAttendeeInput>
}

export type EventRegistrationCreateManyAttendeeInputEnvelope = {
  data: Prisma.EventRegistrationCreateManyAttendeeInput | Prisma.EventRegistrationCreateManyAttendeeInput[]
  skipDuplicates?: boolean
}

export type EventRegistrationUpsertWithWhereUniqueWithoutAttendeeInput = {
  where: Prisma.EventRegistrationWhereUniqueInput
  update: Prisma.XOR<Prisma.EventRegistrationUpdateWithoutAttendeeInput, Prisma.EventRegistrationUncheckedUpdateWithoutAttendeeInput>
  create: Prisma.XOR<Prisma.EventRegistrationCreateWithoutAttendeeInput, Prisma.EventRegistrationUncheckedCreateWithoutAttendeeInput>
}

export type EventRegistrationUpdateWithWhereUniqueWithoutAttendeeInput = {
  where: Prisma.EventRegistrationWhereUniqueInput
  data: Prisma.XOR<Prisma.EventRegistrationUpdateWithoutAttendeeInput, Prisma.EventRegistrationUncheckedUpdateWithoutAttendeeInput>
}

export type EventRegistrationUpdateManyWithWhereWithoutAttendeeInput = {
  where: Prisma.EventRegistrationScalarWhereInput
  data: Prisma.XOR<Prisma.EventRegistrationUpdateManyMutationInput, Prisma.EventRegistrationUncheckedUpdateManyWithoutAttendeeInput>
}

export type EventRegistrationScalarWhereInput = {
  AND?: Prisma.EventRegistrationScalarWhereInput | Prisma.EventRegistrationScalarWhereInput[]
  OR?: Prisma.EventRegistrationScalarWhereInput[]
  NOT?: Prisma.EventRegistrationScalarWhereInput | Prisma.EventRegistrationScalarWhereInput[]
  id?: Prisma.StringFilter<"EventRegistration"> | string
  eventId?: Prisma.StringFilter<"EventRegistration"> | string
  attendeeId?: Prisma.StringFilter<"EventRegistration"> | string
  status?: Prisma.EnumRegistrationStatusFilter<"EventRegistration"> | $Enums.RegistrationStatus
  registeredAt?: Prisma.DateTimeFilter<"EventRegistration"> | Date | string
}

export type EventRegistrationCreateWithoutEventInput = {
  id?: string
  status?: $Enums.RegistrationStatus
  registeredAt?: Date | string
  attendee: Prisma.AlumniProfileCreateNestedOneWithoutEventRegistrationsInput
}

export type EventRegistrationUncheckedCreateWithoutEventInput = {
  id?: string
  attendeeId: string
  status?: $Enums.RegistrationStatus
  registeredAt?: Date | string
}

export type EventRegistrationCreateOrConnectWithoutEventInput = {
  where: Prisma.EventRegistrationWhereUniqueInput
  create: Prisma.XOR<Prisma.EventRegistrationCreateWithoutEventInput, Prisma.EventRegistrationUncheckedCreateWithoutEventInput>
}

export type EventRegistrationCreateManyEventInputEnvelope = {
  data: Prisma.EventRegistrationCreateManyEventInput | Prisma.EventRegistrationCreateManyEventInput[]
  skipDuplicates?: boolean
}

export type EventRegistrationUpsertWithWhereUniqueWithoutEventInput = {
  where: Prisma.EventRegistrationWhereUniqueInput
  update: Prisma.XOR<Prisma.EventRegistrationUpdateWithoutEventInput, Prisma.EventRegistrationUncheckedUpdateWithoutEventInput>
  create: Prisma.XOR<Prisma.EventRegistrationCreateWithoutEventInput, Prisma.EventRegistrationUncheckedCreateWithoutEventInput>
}

export type EventRegistrationUpdateWithWhereUniqueWithoutEventInput = {
  where: Prisma.EventRegistrationWhereUniqueInput
  data: Prisma.XOR<Prisma.EventRegistrationUpdateWithoutEventInput, Prisma.EventRegistrationUncheckedUpdateWithoutEventInput>
}

export type EventRegistrationUpdateManyWithWhereWithoutEventInput = {
  where: Prisma.EventRegistrationScalarWhereInput
  data: Prisma.XOR<Prisma.EventRegistrationUpdateManyMutationInput, Prisma.EventRegistrationUncheckedUpdateManyWithoutEventInput>
}

export type EventRegistrationCreateManyAttendeeInput = {
  id?: string
  eventId: string
  status?: $Enums.RegistrationStatus
  registeredAt?: Date | string
}

export type EventRegistrationUpdateWithoutAttendeeInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumRegistrationStatusFieldUpdateOperationsInput | $Enums.RegistrationStatus
  registeredAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  event?: Prisma.EventUpdateOneRequiredWithoutRegistrationsNestedInput
}

export type EventRegistrationUncheckedUpdateWithoutAttendeeInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  eventId?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumRegistrationStatusFieldUpdateOperationsInput | $Enums.RegistrationStatus
  registeredAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type EventRegistrationUncheckedUpdateManyWithoutAttendeeInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  eventId?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumRegistrationStatusFieldUpdateOperationsInput | $Enums.RegistrationStatus
  registeredAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type EventRegistrationCreateManyEventInput = {
  id?: string
  attendeeId: string
  status?: $Enums.RegistrationStatus
  registeredAt?: Date | string
}

export type EventRegistrationUpdateWithoutEventInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumRegistrationStatusFieldUpdateOperationsInput | $Enums.RegistrationStatus
  registeredAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  attendee?: Prisma.AlumniProfileUpdateOneRequiredWithoutEventRegistrationsNestedInput
}

export type EventRegistrationUncheckedUpdateWithoutEventInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  attendeeId?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumRegistrationStatusFieldUpdateOperationsInput | $Enums.RegistrationStatus
  registeredAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type EventRegistrationUncheckedUpdateManyWithoutEventInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  attendeeId?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumRegistrationStatusFieldUpdateOperationsInput | $Enums.RegistrationStatus
  registeredAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}



export type EventRegistrationSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  eventId?: boolean
  attendeeId?: boolean
  status?: boolean
  registeredAt?: boolean
  event?: boolean | Prisma.EventDefaultArgs<ExtArgs>
  attendee?: boolean | Prisma.AlumniProfileDefaultArgs<ExtArgs>
}, ExtArgs["result"]["eventRegistration"]>



export type EventRegistrationSelectScalar = {
  id?: boolean
  eventId?: boolean
  attendeeId?: boolean
  status?: boolean
  registeredAt?: boolean
}

export type EventRegistrationOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "eventId" | "attendeeId" | "status" | "registeredAt", ExtArgs["result"]["eventRegistration"]>
export type EventRegistrationInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  event?: boolean | Prisma.EventDefaultArgs<ExtArgs>
  attendee?: boolean | Prisma.AlumniProfileDefaultArgs<ExtArgs>
}

export type $EventRegistrationPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "EventRegistration"
  objects: {
    event: Prisma.$EventPayload<ExtArgs>
    attendee: Prisma.$AlumniProfilePayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    eventId: string
    attendeeId: string
    status: $Enums.RegistrationStatus
    registeredAt: Date
  }, ExtArgs["result"]["eventRegistration"]>
  composites: {}
}

export type EventRegistrationGetPayload<S extends boolean | null | undefined | EventRegistrationDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$EventRegistrationPayload, S>

export type EventRegistrationCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<EventRegistrationFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: EventRegistrationCountAggregateInputType | true
  }

export interface EventRegistrationDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['EventRegistration'], meta: { name: 'EventRegistration' } }
  /**
   * Find zero or one EventRegistration that matches the filter.
   * @param {EventRegistrationFindUniqueArgs} args - Arguments to find a EventRegistration
   * @example
   * // Get one EventRegistration
   * const eventRegistration = await prisma.eventRegistration.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends EventRegistrationFindUniqueArgs>(args: Prisma.SelectSubset<T, EventRegistrationFindUniqueArgs<ExtArgs>>): Prisma.Prisma__EventRegistrationClient<runtime.Types.Result.GetResult<Prisma.$EventRegistrationPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one EventRegistration that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {EventRegistrationFindUniqueOrThrowArgs} args - Arguments to find a EventRegistration
   * @example
   * // Get one EventRegistration
   * const eventRegistration = await prisma.eventRegistration.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends EventRegistrationFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, EventRegistrationFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__EventRegistrationClient<runtime.Types.Result.GetResult<Prisma.$EventRegistrationPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first EventRegistration that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {EventRegistrationFindFirstArgs} args - Arguments to find a EventRegistration
   * @example
   * // Get one EventRegistration
   * const eventRegistration = await prisma.eventRegistration.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends EventRegistrationFindFirstArgs>(args?: Prisma.SelectSubset<T, EventRegistrationFindFirstArgs<ExtArgs>>): Prisma.Prisma__EventRegistrationClient<runtime.Types.Result.GetResult<Prisma.$EventRegistrationPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first EventRegistration that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {EventRegistrationFindFirstOrThrowArgs} args - Arguments to find a EventRegistration
   * @example
   * // Get one EventRegistration
   * const eventRegistration = await prisma.eventRegistration.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends EventRegistrationFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, EventRegistrationFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__EventRegistrationClient<runtime.Types.Result.GetResult<Prisma.$EventRegistrationPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more EventRegistrations that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {EventRegistrationFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all EventRegistrations
   * const eventRegistrations = await prisma.eventRegistration.findMany()
   * 
   * // Get first 10 EventRegistrations
   * const eventRegistrations = await prisma.eventRegistration.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const eventRegistrationWithIdOnly = await prisma.eventRegistration.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends EventRegistrationFindManyArgs>(args?: Prisma.SelectSubset<T, EventRegistrationFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$EventRegistrationPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a EventRegistration.
   * @param {EventRegistrationCreateArgs} args - Arguments to create a EventRegistration.
   * @example
   * // Create one EventRegistration
   * const EventRegistration = await prisma.eventRegistration.create({
   *   data: {
   *     // ... data to create a EventRegistration
   *   }
   * })
   * 
   */
  create<T extends EventRegistrationCreateArgs>(args: Prisma.SelectSubset<T, EventRegistrationCreateArgs<ExtArgs>>): Prisma.Prisma__EventRegistrationClient<runtime.Types.Result.GetResult<Prisma.$EventRegistrationPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many EventRegistrations.
   * @param {EventRegistrationCreateManyArgs} args - Arguments to create many EventRegistrations.
   * @example
   * // Create many EventRegistrations
   * const eventRegistration = await prisma.eventRegistration.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends EventRegistrationCreateManyArgs>(args?: Prisma.SelectSubset<T, EventRegistrationCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a EventRegistration.
   * @param {EventRegistrationDeleteArgs} args - Arguments to delete one EventRegistration.
   * @example
   * // Delete one EventRegistration
   * const EventRegistration = await prisma.eventRegistration.delete({
   *   where: {
   *     // ... filter to delete one EventRegistration
   *   }
   * })
   * 
   */
  delete<T extends EventRegistrationDeleteArgs>(args: Prisma.SelectSubset<T, EventRegistrationDeleteArgs<ExtArgs>>): Prisma.Prisma__EventRegistrationClient<runtime.Types.Result.GetResult<Prisma.$EventRegistrationPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one EventRegistration.
   * @param {EventRegistrationUpdateArgs} args - Arguments to update one EventRegistration.
   * @example
   * // Update one EventRegistration
   * const eventRegistration = await prisma.eventRegistration.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends EventRegistrationUpdateArgs>(args: Prisma.SelectSubset<T, EventRegistrationUpdateArgs<ExtArgs>>): Prisma.Prisma__EventRegistrationClient<runtime.Types.Result.GetResult<Prisma.$EventRegistrationPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more EventRegistrations.
   * @param {EventRegistrationDeleteManyArgs} args - Arguments to filter EventRegistrations to delete.
   * @example
   * // Delete a few EventRegistrations
   * const { count } = await prisma.eventRegistration.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends EventRegistrationDeleteManyArgs>(args?: Prisma.SelectSubset<T, EventRegistrationDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more EventRegistrations.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {EventRegistrationUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many EventRegistrations
   * const eventRegistration = await prisma.eventRegistration.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends EventRegistrationUpdateManyArgs>(args: Prisma.SelectSubset<T, EventRegistrationUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one EventRegistration.
   * @param {EventRegistrationUpsertArgs} args - Arguments to update or create a EventRegistration.
   * @example
   * // Update or create a EventRegistration
   * const eventRegistration = await prisma.eventRegistration.upsert({
   *   create: {
   *     // ... data to create a EventRegistration
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the EventRegistration we want to update
   *   }
   * })
   */
  upsert<T extends EventRegistrationUpsertArgs>(args: Prisma.SelectSubset<T, EventRegistrationUpsertArgs<ExtArgs>>): Prisma.Prisma__EventRegistrationClient<runtime.Types.Result.GetResult<Prisma.$EventRegistrationPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of EventRegistrations.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {EventRegistrationCountArgs} args - Arguments to filter EventRegistrations to count.
   * @example
   * // Count the number of EventRegistrations
   * const count = await prisma.eventRegistration.count({
   *   where: {
   *     // ... the filter for the EventRegistrations we want to count
   *   }
   * })
  **/
  count<T extends EventRegistrationCountArgs>(
    args?: Prisma.Subset<T, EventRegistrationCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], EventRegistrationCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a EventRegistration.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {EventRegistrationAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends EventRegistrationAggregateArgs>(args: Prisma.Subset<T, EventRegistrationAggregateArgs>): Prisma.PrismaPromise<GetEventRegistrationAggregateType<T>>

  /**
   * Group by EventRegistration.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {EventRegistrationGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends EventRegistrationGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: EventRegistrationGroupByArgs['orderBy'] }
      : { orderBy?: EventRegistrationGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, EventRegistrationGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetEventRegistrationGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the EventRegistration model
 */
readonly fields: EventRegistrationFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for EventRegistration.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__EventRegistrationClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  event<T extends Prisma.EventDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.EventDefaultArgs<ExtArgs>>): Prisma.Prisma__EventClient<runtime.Types.Result.GetResult<Prisma.$EventPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  attendee<T extends Prisma.AlumniProfileDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.AlumniProfileDefaultArgs<ExtArgs>>): Prisma.Prisma__AlumniProfileClient<runtime.Types.Result.GetResult<Prisma.$AlumniProfilePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the EventRegistration model
 */
export interface EventRegistrationFieldRefs {
  readonly id: Prisma.FieldRef<"EventRegistration", 'String'>
  readonly eventId: Prisma.FieldRef<"EventRegistration", 'String'>
  readonly attendeeId: Prisma.FieldRef<"EventRegistration", 'String'>
  readonly status: Prisma.FieldRef<"EventRegistration", 'RegistrationStatus'>
  readonly registeredAt: Prisma.FieldRef<"EventRegistration", 'DateTime'>
}
    

// Custom InputTypes
/**
 * EventRegistration findUnique
 */
export type EventRegistrationFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the EventRegistration
   */
  select?: Prisma.EventRegistrationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the EventRegistration
   */
  omit?: Prisma.EventRegistrationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.EventRegistrationInclude<ExtArgs> | null
  /**
   * Filter, which EventRegistration to fetch.
   */
  where: Prisma.EventRegistrationWhereUniqueInput
}

/**
 * EventRegistration findUniqueOrThrow
 */
export type EventRegistrationFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the EventRegistration
   */
  select?: Prisma.EventRegistrationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the EventRegistration
   */
  omit?: Prisma.EventRegistrationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.EventRegistrationInclude<ExtArgs> | null
  /**
   * Filter, which EventRegistration to fetch.
   */
  where: Prisma.EventRegistrationWhereUniqueInput
}

/**
 * EventRegistration findFirst
 */
export type EventRegistrationFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the EventRegistration
   */
  select?: Prisma.EventRegistrationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the EventRegistration
   */
  omit?: Prisma.EventRegistrationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.EventRegistrationInclude<ExtArgs> | null
  /**
   * Filter, which EventRegistration to fetch.
   */
  where?: Prisma.EventRegistrationWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of EventRegistrations to fetch.
   */
  orderBy?: Prisma.EventRegistrationOrderByWithRelationInput | Prisma.EventRegistrationOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for EventRegistrations.
   */
  cursor?: Prisma.EventRegistrationWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` EventRegistrations from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` EventRegistrations.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of EventRegistrations.
   */
  distinct?: Prisma.EventRegistrationScalarFieldEnum | Prisma.EventRegistrationScalarFieldEnum[]
}

/**
 * EventRegistration findFirstOrThrow
 */
export type EventRegistrationFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the EventRegistration
   */
  select?: Prisma.EventRegistrationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the EventRegistration
   */
  omit?: Prisma.EventRegistrationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.EventRegistrationInclude<ExtArgs> | null
  /**
   * Filter, which EventRegistration to fetch.
   */
  where?: Prisma.EventRegistrationWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of EventRegistrations to fetch.
   */
  orderBy?: Prisma.EventRegistrationOrderByWithRelationInput | Prisma.EventRegistrationOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for EventRegistrations.
   */
  cursor?: Prisma.EventRegistrationWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` EventRegistrations from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` EventRegistrations.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of EventRegistrations.
   */
  distinct?: Prisma.EventRegistrationScalarFieldEnum | Prisma.EventRegistrationScalarFieldEnum[]
}

/**
 * EventRegistration findMany
 */
export type EventRegistrationFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the EventRegistration
   */
  select?: Prisma.EventRegistrationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the EventRegistration
   */
  omit?: Prisma.EventRegistrationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.EventRegistrationInclude<ExtArgs> | null
  /**
   * Filter, which EventRegistrations to fetch.
   */
  where?: Prisma.EventRegistrationWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of EventRegistrations to fetch.
   */
  orderBy?: Prisma.EventRegistrationOrderByWithRelationInput | Prisma.EventRegistrationOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing EventRegistrations.
   */
  cursor?: Prisma.EventRegistrationWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` EventRegistrations from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` EventRegistrations.
   */
  skip?: number
  distinct?: Prisma.EventRegistrationScalarFieldEnum | Prisma.EventRegistrationScalarFieldEnum[]
}

/**
 * EventRegistration create
 */
export type EventRegistrationCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the EventRegistration
   */
  select?: Prisma.EventRegistrationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the EventRegistration
   */
  omit?: Prisma.EventRegistrationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.EventRegistrationInclude<ExtArgs> | null
  /**
   * The data needed to create a EventRegistration.
   */
  data: Prisma.XOR<Prisma.EventRegistrationCreateInput, Prisma.EventRegistrationUncheckedCreateInput>
}

/**
 * EventRegistration createMany
 */
export type EventRegistrationCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many EventRegistrations.
   */
  data: Prisma.EventRegistrationCreateManyInput | Prisma.EventRegistrationCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * EventRegistration update
 */
export type EventRegistrationUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the EventRegistration
   */
  select?: Prisma.EventRegistrationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the EventRegistration
   */
  omit?: Prisma.EventRegistrationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.EventRegistrationInclude<ExtArgs> | null
  /**
   * The data needed to update a EventRegistration.
   */
  data: Prisma.XOR<Prisma.EventRegistrationUpdateInput, Prisma.EventRegistrationUncheckedUpdateInput>
  /**
   * Choose, which EventRegistration to update.
   */
  where: Prisma.EventRegistrationWhereUniqueInput
}

/**
 * EventRegistration updateMany
 */
export type EventRegistrationUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update EventRegistrations.
   */
  data: Prisma.XOR<Prisma.EventRegistrationUpdateManyMutationInput, Prisma.EventRegistrationUncheckedUpdateManyInput>
  /**
   * Filter which EventRegistrations to update
   */
  where?: Prisma.EventRegistrationWhereInput
  /**
   * Limit how many EventRegistrations to update.
   */
  limit?: number
}

/**
 * EventRegistration upsert
 */
export type EventRegistrationUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the EventRegistration
   */
  select?: Prisma.EventRegistrationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the EventRegistration
   */
  omit?: Prisma.EventRegistrationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.EventRegistrationInclude<ExtArgs> | null
  /**
   * The filter to search for the EventRegistration to update in case it exists.
   */
  where: Prisma.EventRegistrationWhereUniqueInput
  /**
   * In case the EventRegistration found by the `where` argument doesn't exist, create a new EventRegistration with this data.
   */
  create: Prisma.XOR<Prisma.EventRegistrationCreateInput, Prisma.EventRegistrationUncheckedCreateInput>
  /**
   * In case the EventRegistration was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.EventRegistrationUpdateInput, Prisma.EventRegistrationUncheckedUpdateInput>
}

/**
 * EventRegistration delete
 */
export type EventRegistrationDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the EventRegistration
   */
  select?: Prisma.EventRegistrationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the EventRegistration
   */
  omit?: Prisma.EventRegistrationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.EventRegistrationInclude<ExtArgs> | null
  /**
   * Filter which EventRegistration to delete.
   */
  where: Prisma.EventRegistrationWhereUniqueInput
}

/**
 * EventRegistration deleteMany
 */
export type EventRegistrationDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which EventRegistrations to delete
   */
  where?: Prisma.EventRegistrationWhereInput
  /**
   * Limit how many EventRegistrations to delete.
   */
  limit?: number
}

/**
 * EventRegistration without action
 */
export type EventRegistrationDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the EventRegistration
   */
  select?: Prisma.EventRegistrationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the EventRegistration
   */
  omit?: Prisma.EventRegistrationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.EventRegistrationInclude<ExtArgs> | null
}
