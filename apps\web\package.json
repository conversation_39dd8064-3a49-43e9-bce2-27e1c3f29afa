{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port=3001", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ai-sdk/google": "^2.0.3", "@ai-sdk/openai": "^2.0.5", "@better-fetch/fetch": "^1.1.18", "@hookform/resolvers": "^5.2.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-toolbar": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/query-core": "^5.83.1", "@tanstack/react-query": "^5.84.1", "@tanstack/react-query-devtools": "^5.84.1", "@tanstack/react-table": "^8.21.3", "@uploadthing/react": "7.3.2", "ai": "^5.0.8", "better-auth": "1.3.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "country-data-list": "^1.5.4", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "ioredis": "^5.7.0", "libphonenumber-js": "^1.12.10", "loops": "^5.0.1", "lucide-react": "^0.537.0", "moment": "^2.30.1", "next": "15.4.6", "next-themes": "^0.4.6", "pusher": "^5.2.0", "pusher-js": "^8.4.0", "react": "19.1.1", "react-circle-flags": "^0.0.23", "react-day-picker": "^9.8.1", "react-dom": "19.1.1", "react-hook-form": "^7.62.0", "react-icons": "^5.5.0", "react-resizable-panels": "^3.0.4", "react-textarea-autosize": "^8.5.9", "react-toastify": "^11.0.5", "recharts": "^3.1.2", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "tailwind-scrollbar-hide": "^4.0.0", "tailwind-variants": "^2.1.0", "tailwindcss-animate": "^1.0.7", "ua-parser-js": "^2.0.4", "uploadthing": "7.7.3", "vaul": "^1.1.2", "zod": "^4.0.15", "zod-prisma-types": "^3.2.4"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@tanstack/react-query-devtools": "^5.80.5", "@types/node": "^24.2.0", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "tailwindcss": "^4.1.11", "typescript": "^5.9.2"}}