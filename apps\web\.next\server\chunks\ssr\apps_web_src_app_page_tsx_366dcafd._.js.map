{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Work/protec/apps/web/src/app/page.tsx"], "sourcesContent": ["\"use client\"\nimport { useQuery } from \"@tanstack/react-query\";\nimport { orpc } from \"@/utils/orpc\";\n\nconst TITLE_TEXT = `\n ██████╗ ███████╗████████╗████████╗███████╗██████╗\n ██╔══██╗██╔════╝╚══██╔══╝╚══██╔══╝██╔════╝██╔══██╗\n ██████╔╝█████╗     ██║      ██║   █████╗  ██████╔╝\n ██╔══██╗██╔══╝     ██║      ██║   ██╔══╝  ██╔══██╗\n ██████╔╝███████╗   ██║      ██║   ███████╗██║  ██║\n ╚═════╝ ╚══════╝   ╚═╝      ╚═╝   ╚══════╝╚═╝  ╚═╝\n\n ████████╗    ███████╗████████╗ █████╗  ██████╗██╗  ██╗\n ╚══██╔══╝    ██╔════╝╚══██╔══╝██╔══██╗██╔════╝██║ ██╔╝\n    ██║       ███████╗   ██║   ███████║██║     █████╔╝\n    ██║       ╚════██║   ██║   ██╔══██║██║     ██╔═██╗\n    ██║       ███████║   ██║   ██║  ██║╚██████╗██║  ██╗\n    ╚═╝       ╚══════╝   ╚═╝   ╚═╝  ╚═╝ ╚═════╝╚═╝  ╚═╝\n `;\n\nexport default function Home() {\n  const healthCheck = useQuery(orpc.healthCheck.queryOptions());\n\n  return (\n    <div className=\"container mx-auto max-w-3xl px-4 py-2\">\n      <pre className=\"overflow-x-auto font-mono text-sm\">{TITLE_TEXT}</pre>\n      <div className=\"grid gap-6\">\n        <section className=\"rounded-lg border p-4\">\n          <h2 className=\"mb-2 font-medium\">API Status</h2>\n            <div className=\"flex items-center gap-2\">\n              <div\n                className={`h-2 w-2 rounded-full ${healthCheck.data ? \"bg-green-500\" : \"bg-red-500\"}`}\n              />\n              <span className=\"text-sm text-muted-foreground\">\n                {healthCheck.isLoading\n                  ? \"Checking...\"\n                  : healthCheck.data\n                    ? \"Connected\"\n                    : \"Disconnected\"}\n              </span>\n            </div>\n        </section>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AAFA;;;;AAIA,MAAM,aAAa,CAAC;;;;;;;;;;;;;;CAcnB,CAAC;AAEa,SAAS;IACtB,MAAM,cAAc,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE,mIAAA,CAAA,OAAI,CAAC,WAAW,CAAC,YAAY;IAE1D,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BAAqC;;;;;;0BACpD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAQ,WAAU;;sCACjB,8OAAC;4BAAG,WAAU;sCAAmB;;;;;;sCAC/B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,WAAW,CAAC,qBAAqB,EAAE,YAAY,IAAI,GAAG,iBAAiB,cAAc;;;;;;8CAEvF,8OAAC;oCAAK,WAAU;8CACb,YAAY,SAAS,GAClB,gBACA,YAAY,IAAI,GACd,cACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtB", "debugId": null}}]}