
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `AlumniProfile` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model AlumniProfile
 * 
 */
export type AlumniProfileModel = runtime.Types.Result.DefaultSelection<Prisma.$AlumniProfilePayload>

export type AggregateAlumniProfile = {
  _count: AlumniProfileCountAggregateOutputType | null
  _avg: AlumniProfileAvgAggregateOutputType | null
  _sum: AlumniProfileSumAggregateOutputType | null
  _min: AlumniProfileMinAggregateOutputType | null
  _max: AlumniProfileMaxAggregateOutputType | null
}

export type AlumniProfileAvgAggregateOutputType = {
  graduationYear: number | null
}

export type AlumniProfileSumAggregateOutputType = {
  graduationYear: number | null
}

export type AlumniProfileMinAggregateOutputType = {
  id: string | null
  userId: string | null
  firstName: string | null
  lastName: string | null
  displayName: string | null
  bio: string | null
  profilePicture: string | null
  primaryOrganizationId: string | null
  graduationYear: number | null
  programType: string | null
  centerLocation: string | null
  achievements: string | null
  currentPosition: string | null
  currentCompany: string | null
  industry: string | null
  location: string | null
  linkedInUrl: string | null
  phoneNumber: string | null
  mentorshipOffered: boolean | null
  mentorshipSought: boolean | null
  skillsOffered: string | null
  skillsWanted: string | null
  profileVisibility: $Enums.ProfileVisibility | null
  showEmail: boolean | null
  showPhone: boolean | null
  showLocation: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type AlumniProfileMaxAggregateOutputType = {
  id: string | null
  userId: string | null
  firstName: string | null
  lastName: string | null
  displayName: string | null
  bio: string | null
  profilePicture: string | null
  primaryOrganizationId: string | null
  graduationYear: number | null
  programType: string | null
  centerLocation: string | null
  achievements: string | null
  currentPosition: string | null
  currentCompany: string | null
  industry: string | null
  location: string | null
  linkedInUrl: string | null
  phoneNumber: string | null
  mentorshipOffered: boolean | null
  mentorshipSought: boolean | null
  skillsOffered: string | null
  skillsWanted: string | null
  profileVisibility: $Enums.ProfileVisibility | null
  showEmail: boolean | null
  showPhone: boolean | null
  showLocation: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type AlumniProfileCountAggregateOutputType = {
  id: number
  userId: number
  firstName: number
  lastName: number
  displayName: number
  bio: number
  profilePicture: number
  primaryOrganizationId: number
  graduationYear: number
  programType: number
  centerLocation: number
  achievements: number
  currentPosition: number
  currentCompany: number
  industry: number
  location: number
  linkedInUrl: number
  phoneNumber: number
  mentorshipOffered: number
  mentorshipSought: number
  skillsOffered: number
  skillsWanted: number
  profileVisibility: number
  showEmail: number
  showPhone: number
  showLocation: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type AlumniProfileAvgAggregateInputType = {
  graduationYear?: true
}

export type AlumniProfileSumAggregateInputType = {
  graduationYear?: true
}

export type AlumniProfileMinAggregateInputType = {
  id?: true
  userId?: true
  firstName?: true
  lastName?: true
  displayName?: true
  bio?: true
  profilePicture?: true
  primaryOrganizationId?: true
  graduationYear?: true
  programType?: true
  centerLocation?: true
  achievements?: true
  currentPosition?: true
  currentCompany?: true
  industry?: true
  location?: true
  linkedInUrl?: true
  phoneNumber?: true
  mentorshipOffered?: true
  mentorshipSought?: true
  skillsOffered?: true
  skillsWanted?: true
  profileVisibility?: true
  showEmail?: true
  showPhone?: true
  showLocation?: true
  createdAt?: true
  updatedAt?: true
}

export type AlumniProfileMaxAggregateInputType = {
  id?: true
  userId?: true
  firstName?: true
  lastName?: true
  displayName?: true
  bio?: true
  profilePicture?: true
  primaryOrganizationId?: true
  graduationYear?: true
  programType?: true
  centerLocation?: true
  achievements?: true
  currentPosition?: true
  currentCompany?: true
  industry?: true
  location?: true
  linkedInUrl?: true
  phoneNumber?: true
  mentorshipOffered?: true
  mentorshipSought?: true
  skillsOffered?: true
  skillsWanted?: true
  profileVisibility?: true
  showEmail?: true
  showPhone?: true
  showLocation?: true
  createdAt?: true
  updatedAt?: true
}

export type AlumniProfileCountAggregateInputType = {
  id?: true
  userId?: true
  firstName?: true
  lastName?: true
  displayName?: true
  bio?: true
  profilePicture?: true
  primaryOrganizationId?: true
  graduationYear?: true
  programType?: true
  centerLocation?: true
  achievements?: true
  currentPosition?: true
  currentCompany?: true
  industry?: true
  location?: true
  linkedInUrl?: true
  phoneNumber?: true
  mentorshipOffered?: true
  mentorshipSought?: true
  skillsOffered?: true
  skillsWanted?: true
  profileVisibility?: true
  showEmail?: true
  showPhone?: true
  showLocation?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type AlumniProfileAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which AlumniProfile to aggregate.
   */
  where?: Prisma.AlumniProfileWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of AlumniProfiles to fetch.
   */
  orderBy?: Prisma.AlumniProfileOrderByWithRelationInput | Prisma.AlumniProfileOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.AlumniProfileWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` AlumniProfiles from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` AlumniProfiles.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned AlumniProfiles
  **/
  _count?: true | AlumniProfileCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: AlumniProfileAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: AlumniProfileSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: AlumniProfileMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: AlumniProfileMaxAggregateInputType
}

export type GetAlumniProfileAggregateType<T extends AlumniProfileAggregateArgs> = {
      [P in keyof T & keyof AggregateAlumniProfile]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateAlumniProfile[P]>
    : Prisma.GetScalarType<T[P], AggregateAlumniProfile[P]>
}




export type AlumniProfileGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.AlumniProfileWhereInput
  orderBy?: Prisma.AlumniProfileOrderByWithAggregationInput | Prisma.AlumniProfileOrderByWithAggregationInput[]
  by: Prisma.AlumniProfileScalarFieldEnum[] | Prisma.AlumniProfileScalarFieldEnum
  having?: Prisma.AlumniProfileScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: AlumniProfileCountAggregateInputType | true
  _avg?: AlumniProfileAvgAggregateInputType
  _sum?: AlumniProfileSumAggregateInputType
  _min?: AlumniProfileMinAggregateInputType
  _max?: AlumniProfileMaxAggregateInputType
}

export type AlumniProfileGroupByOutputType = {
  id: string
  userId: string
  firstName: string
  lastName: string
  displayName: string | null
  bio: string | null
  profilePicture: string | null
  primaryOrganizationId: string | null
  graduationYear: number
  programType: string
  centerLocation: string
  achievements: string | null
  currentPosition: string | null
  currentCompany: string | null
  industry: string | null
  location: string | null
  linkedInUrl: string | null
  phoneNumber: string | null
  mentorshipOffered: boolean
  mentorshipSought: boolean
  skillsOffered: string | null
  skillsWanted: string | null
  profileVisibility: $Enums.ProfileVisibility
  showEmail: boolean
  showPhone: boolean
  showLocation: boolean
  createdAt: Date
  updatedAt: Date
  _count: AlumniProfileCountAggregateOutputType | null
  _avg: AlumniProfileAvgAggregateOutputType | null
  _sum: AlumniProfileSumAggregateOutputType | null
  _min: AlumniProfileMinAggregateOutputType | null
  _max: AlumniProfileMaxAggregateOutputType | null
}

type GetAlumniProfileGroupByPayload<T extends AlumniProfileGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<AlumniProfileGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof AlumniProfileGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], AlumniProfileGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], AlumniProfileGroupByOutputType[P]>
      }
    >
  >



export type AlumniProfileWhereInput = {
  AND?: Prisma.AlumniProfileWhereInput | Prisma.AlumniProfileWhereInput[]
  OR?: Prisma.AlumniProfileWhereInput[]
  NOT?: Prisma.AlumniProfileWhereInput | Prisma.AlumniProfileWhereInput[]
  id?: Prisma.StringFilter<"AlumniProfile"> | string
  userId?: Prisma.StringFilter<"AlumniProfile"> | string
  firstName?: Prisma.StringFilter<"AlumniProfile"> | string
  lastName?: Prisma.StringFilter<"AlumniProfile"> | string
  displayName?: Prisma.StringNullableFilter<"AlumniProfile"> | string | null
  bio?: Prisma.StringNullableFilter<"AlumniProfile"> | string | null
  profilePicture?: Prisma.StringNullableFilter<"AlumniProfile"> | string | null
  primaryOrganizationId?: Prisma.StringNullableFilter<"AlumniProfile"> | string | null
  graduationYear?: Prisma.IntFilter<"AlumniProfile"> | number
  programType?: Prisma.StringFilter<"AlumniProfile"> | string
  centerLocation?: Prisma.StringFilter<"AlumniProfile"> | string
  achievements?: Prisma.StringNullableFilter<"AlumniProfile"> | string | null
  currentPosition?: Prisma.StringNullableFilter<"AlumniProfile"> | string | null
  currentCompany?: Prisma.StringNullableFilter<"AlumniProfile"> | string | null
  industry?: Prisma.StringNullableFilter<"AlumniProfile"> | string | null
  location?: Prisma.StringNullableFilter<"AlumniProfile"> | string | null
  linkedInUrl?: Prisma.StringNullableFilter<"AlumniProfile"> | string | null
  phoneNumber?: Prisma.StringNullableFilter<"AlumniProfile"> | string | null
  mentorshipOffered?: Prisma.BoolFilter<"AlumniProfile"> | boolean
  mentorshipSought?: Prisma.BoolFilter<"AlumniProfile"> | boolean
  skillsOffered?: Prisma.StringNullableFilter<"AlumniProfile"> | string | null
  skillsWanted?: Prisma.StringNullableFilter<"AlumniProfile"> | string | null
  profileVisibility?: Prisma.EnumProfileVisibilityFilter<"AlumniProfile"> | $Enums.ProfileVisibility
  showEmail?: Prisma.BoolFilter<"AlumniProfile"> | boolean
  showPhone?: Prisma.BoolFilter<"AlumniProfile"> | boolean
  showLocation?: Prisma.BoolFilter<"AlumniProfile"> | boolean
  createdAt?: Prisma.DateTimeFilter<"AlumniProfile"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"AlumniProfile"> | Date | string
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
  primaryOrganization?: Prisma.XOR<Prisma.OrganizationNullableScalarRelationFilter, Prisma.OrganizationWhereInput> | null
  posts?: Prisma.PostListRelationFilter
  connections?: Prisma.ConnectionListRelationFilter
  connectionRequests?: Prisma.ConnectionListRelationFilter
  eventRegistrations?: Prisma.EventRegistrationListRelationFilter
  donations?: Prisma.DonationListRelationFilter
  messages?: Prisma.MessageListRelationFilter
  receivedMessages?: Prisma.MessageListRelationFilter
  postLikes?: Prisma.PostLikeListRelationFilter
  comments?: Prisma.CommentListRelationFilter
  notifications?: Prisma.NotificationListRelationFilter
}

export type AlumniProfileOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  firstName?: Prisma.SortOrder
  lastName?: Prisma.SortOrder
  displayName?: Prisma.SortOrderInput | Prisma.SortOrder
  bio?: Prisma.SortOrderInput | Prisma.SortOrder
  profilePicture?: Prisma.SortOrderInput | Prisma.SortOrder
  primaryOrganizationId?: Prisma.SortOrderInput | Prisma.SortOrder
  graduationYear?: Prisma.SortOrder
  programType?: Prisma.SortOrder
  centerLocation?: Prisma.SortOrder
  achievements?: Prisma.SortOrderInput | Prisma.SortOrder
  currentPosition?: Prisma.SortOrderInput | Prisma.SortOrder
  currentCompany?: Prisma.SortOrderInput | Prisma.SortOrder
  industry?: Prisma.SortOrderInput | Prisma.SortOrder
  location?: Prisma.SortOrderInput | Prisma.SortOrder
  linkedInUrl?: Prisma.SortOrderInput | Prisma.SortOrder
  phoneNumber?: Prisma.SortOrderInput | Prisma.SortOrder
  mentorshipOffered?: Prisma.SortOrder
  mentorshipSought?: Prisma.SortOrder
  skillsOffered?: Prisma.SortOrderInput | Prisma.SortOrder
  skillsWanted?: Prisma.SortOrderInput | Prisma.SortOrder
  profileVisibility?: Prisma.SortOrder
  showEmail?: Prisma.SortOrder
  showPhone?: Prisma.SortOrder
  showLocation?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  user?: Prisma.UserOrderByWithRelationInput
  primaryOrganization?: Prisma.OrganizationOrderByWithRelationInput
  posts?: Prisma.PostOrderByRelationAggregateInput
  connections?: Prisma.ConnectionOrderByRelationAggregateInput
  connectionRequests?: Prisma.ConnectionOrderByRelationAggregateInput
  eventRegistrations?: Prisma.EventRegistrationOrderByRelationAggregateInput
  donations?: Prisma.DonationOrderByRelationAggregateInput
  messages?: Prisma.MessageOrderByRelationAggregateInput
  receivedMessages?: Prisma.MessageOrderByRelationAggregateInput
  postLikes?: Prisma.PostLikeOrderByRelationAggregateInput
  comments?: Prisma.CommentOrderByRelationAggregateInput
  notifications?: Prisma.NotificationOrderByRelationAggregateInput
  _relevance?: Prisma.AlumniProfileOrderByRelevanceInput
}

export type AlumniProfileWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  userId?: string
  AND?: Prisma.AlumniProfileWhereInput | Prisma.AlumniProfileWhereInput[]
  OR?: Prisma.AlumniProfileWhereInput[]
  NOT?: Prisma.AlumniProfileWhereInput | Prisma.AlumniProfileWhereInput[]
  firstName?: Prisma.StringFilter<"AlumniProfile"> | string
  lastName?: Prisma.StringFilter<"AlumniProfile"> | string
  displayName?: Prisma.StringNullableFilter<"AlumniProfile"> | string | null
  bio?: Prisma.StringNullableFilter<"AlumniProfile"> | string | null
  profilePicture?: Prisma.StringNullableFilter<"AlumniProfile"> | string | null
  primaryOrganizationId?: Prisma.StringNullableFilter<"AlumniProfile"> | string | null
  graduationYear?: Prisma.IntFilter<"AlumniProfile"> | number
  programType?: Prisma.StringFilter<"AlumniProfile"> | string
  centerLocation?: Prisma.StringFilter<"AlumniProfile"> | string
  achievements?: Prisma.StringNullableFilter<"AlumniProfile"> | string | null
  currentPosition?: Prisma.StringNullableFilter<"AlumniProfile"> | string | null
  currentCompany?: Prisma.StringNullableFilter<"AlumniProfile"> | string | null
  industry?: Prisma.StringNullableFilter<"AlumniProfile"> | string | null
  location?: Prisma.StringNullableFilter<"AlumniProfile"> | string | null
  linkedInUrl?: Prisma.StringNullableFilter<"AlumniProfile"> | string | null
  phoneNumber?: Prisma.StringNullableFilter<"AlumniProfile"> | string | null
  mentorshipOffered?: Prisma.BoolFilter<"AlumniProfile"> | boolean
  mentorshipSought?: Prisma.BoolFilter<"AlumniProfile"> | boolean
  skillsOffered?: Prisma.StringNullableFilter<"AlumniProfile"> | string | null
  skillsWanted?: Prisma.StringNullableFilter<"AlumniProfile"> | string | null
  profileVisibility?: Prisma.EnumProfileVisibilityFilter<"AlumniProfile"> | $Enums.ProfileVisibility
  showEmail?: Prisma.BoolFilter<"AlumniProfile"> | boolean
  showPhone?: Prisma.BoolFilter<"AlumniProfile"> | boolean
  showLocation?: Prisma.BoolFilter<"AlumniProfile"> | boolean
  createdAt?: Prisma.DateTimeFilter<"AlumniProfile"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"AlumniProfile"> | Date | string
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
  primaryOrganization?: Prisma.XOR<Prisma.OrganizationNullableScalarRelationFilter, Prisma.OrganizationWhereInput> | null
  posts?: Prisma.PostListRelationFilter
  connections?: Prisma.ConnectionListRelationFilter
  connectionRequests?: Prisma.ConnectionListRelationFilter
  eventRegistrations?: Prisma.EventRegistrationListRelationFilter
  donations?: Prisma.DonationListRelationFilter
  messages?: Prisma.MessageListRelationFilter
  receivedMessages?: Prisma.MessageListRelationFilter
  postLikes?: Prisma.PostLikeListRelationFilter
  comments?: Prisma.CommentListRelationFilter
  notifications?: Prisma.NotificationListRelationFilter
}, "id" | "userId">

export type AlumniProfileOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  firstName?: Prisma.SortOrder
  lastName?: Prisma.SortOrder
  displayName?: Prisma.SortOrderInput | Prisma.SortOrder
  bio?: Prisma.SortOrderInput | Prisma.SortOrder
  profilePicture?: Prisma.SortOrderInput | Prisma.SortOrder
  primaryOrganizationId?: Prisma.SortOrderInput | Prisma.SortOrder
  graduationYear?: Prisma.SortOrder
  programType?: Prisma.SortOrder
  centerLocation?: Prisma.SortOrder
  achievements?: Prisma.SortOrderInput | Prisma.SortOrder
  currentPosition?: Prisma.SortOrderInput | Prisma.SortOrder
  currentCompany?: Prisma.SortOrderInput | Prisma.SortOrder
  industry?: Prisma.SortOrderInput | Prisma.SortOrder
  location?: Prisma.SortOrderInput | Prisma.SortOrder
  linkedInUrl?: Prisma.SortOrderInput | Prisma.SortOrder
  phoneNumber?: Prisma.SortOrderInput | Prisma.SortOrder
  mentorshipOffered?: Prisma.SortOrder
  mentorshipSought?: Prisma.SortOrder
  skillsOffered?: Prisma.SortOrderInput | Prisma.SortOrder
  skillsWanted?: Prisma.SortOrderInput | Prisma.SortOrder
  profileVisibility?: Prisma.SortOrder
  showEmail?: Prisma.SortOrder
  showPhone?: Prisma.SortOrder
  showLocation?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.AlumniProfileCountOrderByAggregateInput
  _avg?: Prisma.AlumniProfileAvgOrderByAggregateInput
  _max?: Prisma.AlumniProfileMaxOrderByAggregateInput
  _min?: Prisma.AlumniProfileMinOrderByAggregateInput
  _sum?: Prisma.AlumniProfileSumOrderByAggregateInput
}

export type AlumniProfileScalarWhereWithAggregatesInput = {
  AND?: Prisma.AlumniProfileScalarWhereWithAggregatesInput | Prisma.AlumniProfileScalarWhereWithAggregatesInput[]
  OR?: Prisma.AlumniProfileScalarWhereWithAggregatesInput[]
  NOT?: Prisma.AlumniProfileScalarWhereWithAggregatesInput | Prisma.AlumniProfileScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"AlumniProfile"> | string
  userId?: Prisma.StringWithAggregatesFilter<"AlumniProfile"> | string
  firstName?: Prisma.StringWithAggregatesFilter<"AlumniProfile"> | string
  lastName?: Prisma.StringWithAggregatesFilter<"AlumniProfile"> | string
  displayName?: Prisma.StringNullableWithAggregatesFilter<"AlumniProfile"> | string | null
  bio?: Prisma.StringNullableWithAggregatesFilter<"AlumniProfile"> | string | null
  profilePicture?: Prisma.StringNullableWithAggregatesFilter<"AlumniProfile"> | string | null
  primaryOrganizationId?: Prisma.StringNullableWithAggregatesFilter<"AlumniProfile"> | string | null
  graduationYear?: Prisma.IntWithAggregatesFilter<"AlumniProfile"> | number
  programType?: Prisma.StringWithAggregatesFilter<"AlumniProfile"> | string
  centerLocation?: Prisma.StringWithAggregatesFilter<"AlumniProfile"> | string
  achievements?: Prisma.StringNullableWithAggregatesFilter<"AlumniProfile"> | string | null
  currentPosition?: Prisma.StringNullableWithAggregatesFilter<"AlumniProfile"> | string | null
  currentCompany?: Prisma.StringNullableWithAggregatesFilter<"AlumniProfile"> | string | null
  industry?: Prisma.StringNullableWithAggregatesFilter<"AlumniProfile"> | string | null
  location?: Prisma.StringNullableWithAggregatesFilter<"AlumniProfile"> | string | null
  linkedInUrl?: Prisma.StringNullableWithAggregatesFilter<"AlumniProfile"> | string | null
  phoneNumber?: Prisma.StringNullableWithAggregatesFilter<"AlumniProfile"> | string | null
  mentorshipOffered?: Prisma.BoolWithAggregatesFilter<"AlumniProfile"> | boolean
  mentorshipSought?: Prisma.BoolWithAggregatesFilter<"AlumniProfile"> | boolean
  skillsOffered?: Prisma.StringNullableWithAggregatesFilter<"AlumniProfile"> | string | null
  skillsWanted?: Prisma.StringNullableWithAggregatesFilter<"AlumniProfile"> | string | null
  profileVisibility?: Prisma.EnumProfileVisibilityWithAggregatesFilter<"AlumniProfile"> | $Enums.ProfileVisibility
  showEmail?: Prisma.BoolWithAggregatesFilter<"AlumniProfile"> | boolean
  showPhone?: Prisma.BoolWithAggregatesFilter<"AlumniProfile"> | boolean
  showLocation?: Prisma.BoolWithAggregatesFilter<"AlumniProfile"> | boolean
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"AlumniProfile"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"AlumniProfile"> | Date | string
}

export type AlumniProfileCreateInput = {
  id?: string
  firstName: string
  lastName: string
  displayName?: string | null
  bio?: string | null
  profilePicture?: string | null
  graduationYear: number
  programType: string
  centerLocation: string
  achievements?: string | null
  currentPosition?: string | null
  currentCompany?: string | null
  industry?: string | null
  location?: string | null
  linkedInUrl?: string | null
  phoneNumber?: string | null
  mentorshipOffered?: boolean
  mentorshipSought?: boolean
  skillsOffered?: string | null
  skillsWanted?: string | null
  profileVisibility?: $Enums.ProfileVisibility
  showEmail?: boolean
  showPhone?: boolean
  showLocation?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  user: Prisma.UserCreateNestedOneWithoutAlumniProfileInput
  primaryOrganization?: Prisma.OrganizationCreateNestedOneWithoutAlumniProfilesInput
  posts?: Prisma.PostCreateNestedManyWithoutAuthorInput
  connections?: Prisma.ConnectionCreateNestedManyWithoutRequesterInput
  connectionRequests?: Prisma.ConnectionCreateNestedManyWithoutRequestedInput
  eventRegistrations?: Prisma.EventRegistrationCreateNestedManyWithoutAttendeeInput
  donations?: Prisma.DonationCreateNestedManyWithoutDonorInput
  messages?: Prisma.MessageCreateNestedManyWithoutSenderInput
  receivedMessages?: Prisma.MessageCreateNestedManyWithoutReceiverInput
  postLikes?: Prisma.PostLikeCreateNestedManyWithoutUserInput
  comments?: Prisma.CommentCreateNestedManyWithoutAuthorInput
  notifications?: Prisma.NotificationCreateNestedManyWithoutRecipientInput
}

export type AlumniProfileUncheckedCreateInput = {
  id?: string
  userId: string
  firstName: string
  lastName: string
  displayName?: string | null
  bio?: string | null
  profilePicture?: string | null
  primaryOrganizationId?: string | null
  graduationYear: number
  programType: string
  centerLocation: string
  achievements?: string | null
  currentPosition?: string | null
  currentCompany?: string | null
  industry?: string | null
  location?: string | null
  linkedInUrl?: string | null
  phoneNumber?: string | null
  mentorshipOffered?: boolean
  mentorshipSought?: boolean
  skillsOffered?: string | null
  skillsWanted?: string | null
  profileVisibility?: $Enums.ProfileVisibility
  showEmail?: boolean
  showPhone?: boolean
  showLocation?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  posts?: Prisma.PostUncheckedCreateNestedManyWithoutAuthorInput
  connections?: Prisma.ConnectionUncheckedCreateNestedManyWithoutRequesterInput
  connectionRequests?: Prisma.ConnectionUncheckedCreateNestedManyWithoutRequestedInput
  eventRegistrations?: Prisma.EventRegistrationUncheckedCreateNestedManyWithoutAttendeeInput
  donations?: Prisma.DonationUncheckedCreateNestedManyWithoutDonorInput
  messages?: Prisma.MessageUncheckedCreateNestedManyWithoutSenderInput
  receivedMessages?: Prisma.MessageUncheckedCreateNestedManyWithoutReceiverInput
  postLikes?: Prisma.PostLikeUncheckedCreateNestedManyWithoutUserInput
  comments?: Prisma.CommentUncheckedCreateNestedManyWithoutAuthorInput
  notifications?: Prisma.NotificationUncheckedCreateNestedManyWithoutRecipientInput
}

export type AlumniProfileUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bio?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profilePicture?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  graduationYear?: Prisma.IntFieldUpdateOperationsInput | number
  programType?: Prisma.StringFieldUpdateOperationsInput | string
  centerLocation?: Prisma.StringFieldUpdateOperationsInput | string
  achievements?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentPosition?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentCompany?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  industry?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  linkedInUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phoneNumber?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  mentorshipOffered?: Prisma.BoolFieldUpdateOperationsInput | boolean
  mentorshipSought?: Prisma.BoolFieldUpdateOperationsInput | boolean
  skillsOffered?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillsWanted?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profileVisibility?: Prisma.EnumProfileVisibilityFieldUpdateOperationsInput | $Enums.ProfileVisibility
  showEmail?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showPhone?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showLocation?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.UserUpdateOneRequiredWithoutAlumniProfileNestedInput
  primaryOrganization?: Prisma.OrganizationUpdateOneWithoutAlumniProfilesNestedInput
  posts?: Prisma.PostUpdateManyWithoutAuthorNestedInput
  connections?: Prisma.ConnectionUpdateManyWithoutRequesterNestedInput
  connectionRequests?: Prisma.ConnectionUpdateManyWithoutRequestedNestedInput
  eventRegistrations?: Prisma.EventRegistrationUpdateManyWithoutAttendeeNestedInput
  donations?: Prisma.DonationUpdateManyWithoutDonorNestedInput
  messages?: Prisma.MessageUpdateManyWithoutSenderNestedInput
  receivedMessages?: Prisma.MessageUpdateManyWithoutReceiverNestedInput
  postLikes?: Prisma.PostLikeUpdateManyWithoutUserNestedInput
  comments?: Prisma.CommentUpdateManyWithoutAuthorNestedInput
  notifications?: Prisma.NotificationUpdateManyWithoutRecipientNestedInput
}

export type AlumniProfileUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bio?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profilePicture?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  primaryOrganizationId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  graduationYear?: Prisma.IntFieldUpdateOperationsInput | number
  programType?: Prisma.StringFieldUpdateOperationsInput | string
  centerLocation?: Prisma.StringFieldUpdateOperationsInput | string
  achievements?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentPosition?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentCompany?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  industry?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  linkedInUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phoneNumber?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  mentorshipOffered?: Prisma.BoolFieldUpdateOperationsInput | boolean
  mentorshipSought?: Prisma.BoolFieldUpdateOperationsInput | boolean
  skillsOffered?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillsWanted?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profileVisibility?: Prisma.EnumProfileVisibilityFieldUpdateOperationsInput | $Enums.ProfileVisibility
  showEmail?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showPhone?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showLocation?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  posts?: Prisma.PostUncheckedUpdateManyWithoutAuthorNestedInput
  connections?: Prisma.ConnectionUncheckedUpdateManyWithoutRequesterNestedInput
  connectionRequests?: Prisma.ConnectionUncheckedUpdateManyWithoutRequestedNestedInput
  eventRegistrations?: Prisma.EventRegistrationUncheckedUpdateManyWithoutAttendeeNestedInput
  donations?: Prisma.DonationUncheckedUpdateManyWithoutDonorNestedInput
  messages?: Prisma.MessageUncheckedUpdateManyWithoutSenderNestedInput
  receivedMessages?: Prisma.MessageUncheckedUpdateManyWithoutReceiverNestedInput
  postLikes?: Prisma.PostLikeUncheckedUpdateManyWithoutUserNestedInput
  comments?: Prisma.CommentUncheckedUpdateManyWithoutAuthorNestedInput
  notifications?: Prisma.NotificationUncheckedUpdateManyWithoutRecipientNestedInput
}

export type AlumniProfileCreateManyInput = {
  id?: string
  userId: string
  firstName: string
  lastName: string
  displayName?: string | null
  bio?: string | null
  profilePicture?: string | null
  primaryOrganizationId?: string | null
  graduationYear: number
  programType: string
  centerLocation: string
  achievements?: string | null
  currentPosition?: string | null
  currentCompany?: string | null
  industry?: string | null
  location?: string | null
  linkedInUrl?: string | null
  phoneNumber?: string | null
  mentorshipOffered?: boolean
  mentorshipSought?: boolean
  skillsOffered?: string | null
  skillsWanted?: string | null
  profileVisibility?: $Enums.ProfileVisibility
  showEmail?: boolean
  showPhone?: boolean
  showLocation?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type AlumniProfileUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bio?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profilePicture?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  graduationYear?: Prisma.IntFieldUpdateOperationsInput | number
  programType?: Prisma.StringFieldUpdateOperationsInput | string
  centerLocation?: Prisma.StringFieldUpdateOperationsInput | string
  achievements?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentPosition?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentCompany?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  industry?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  linkedInUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phoneNumber?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  mentorshipOffered?: Prisma.BoolFieldUpdateOperationsInput | boolean
  mentorshipSought?: Prisma.BoolFieldUpdateOperationsInput | boolean
  skillsOffered?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillsWanted?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profileVisibility?: Prisma.EnumProfileVisibilityFieldUpdateOperationsInput | $Enums.ProfileVisibility
  showEmail?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showPhone?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showLocation?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type AlumniProfileUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bio?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profilePicture?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  primaryOrganizationId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  graduationYear?: Prisma.IntFieldUpdateOperationsInput | number
  programType?: Prisma.StringFieldUpdateOperationsInput | string
  centerLocation?: Prisma.StringFieldUpdateOperationsInput | string
  achievements?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentPosition?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentCompany?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  industry?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  linkedInUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phoneNumber?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  mentorshipOffered?: Prisma.BoolFieldUpdateOperationsInput | boolean
  mentorshipSought?: Prisma.BoolFieldUpdateOperationsInput | boolean
  skillsOffered?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillsWanted?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profileVisibility?: Prisma.EnumProfileVisibilityFieldUpdateOperationsInput | $Enums.ProfileVisibility
  showEmail?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showPhone?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showLocation?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type AlumniProfileNullableScalarRelationFilter = {
  is?: Prisma.AlumniProfileWhereInput | null
  isNot?: Prisma.AlumniProfileWhereInput | null
}

export type AlumniProfileListRelationFilter = {
  every?: Prisma.AlumniProfileWhereInput
  some?: Prisma.AlumniProfileWhereInput
  none?: Prisma.AlumniProfileWhereInput
}

export type AlumniProfileOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type AlumniProfileOrderByRelevanceInput = {
  fields: Prisma.AlumniProfileOrderByRelevanceFieldEnum | Prisma.AlumniProfileOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type AlumniProfileCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  firstName?: Prisma.SortOrder
  lastName?: Prisma.SortOrder
  displayName?: Prisma.SortOrder
  bio?: Prisma.SortOrder
  profilePicture?: Prisma.SortOrder
  primaryOrganizationId?: Prisma.SortOrder
  graduationYear?: Prisma.SortOrder
  programType?: Prisma.SortOrder
  centerLocation?: Prisma.SortOrder
  achievements?: Prisma.SortOrder
  currentPosition?: Prisma.SortOrder
  currentCompany?: Prisma.SortOrder
  industry?: Prisma.SortOrder
  location?: Prisma.SortOrder
  linkedInUrl?: Prisma.SortOrder
  phoneNumber?: Prisma.SortOrder
  mentorshipOffered?: Prisma.SortOrder
  mentorshipSought?: Prisma.SortOrder
  skillsOffered?: Prisma.SortOrder
  skillsWanted?: Prisma.SortOrder
  profileVisibility?: Prisma.SortOrder
  showEmail?: Prisma.SortOrder
  showPhone?: Prisma.SortOrder
  showLocation?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type AlumniProfileAvgOrderByAggregateInput = {
  graduationYear?: Prisma.SortOrder
}

export type AlumniProfileMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  firstName?: Prisma.SortOrder
  lastName?: Prisma.SortOrder
  displayName?: Prisma.SortOrder
  bio?: Prisma.SortOrder
  profilePicture?: Prisma.SortOrder
  primaryOrganizationId?: Prisma.SortOrder
  graduationYear?: Prisma.SortOrder
  programType?: Prisma.SortOrder
  centerLocation?: Prisma.SortOrder
  achievements?: Prisma.SortOrder
  currentPosition?: Prisma.SortOrder
  currentCompany?: Prisma.SortOrder
  industry?: Prisma.SortOrder
  location?: Prisma.SortOrder
  linkedInUrl?: Prisma.SortOrder
  phoneNumber?: Prisma.SortOrder
  mentorshipOffered?: Prisma.SortOrder
  mentorshipSought?: Prisma.SortOrder
  skillsOffered?: Prisma.SortOrder
  skillsWanted?: Prisma.SortOrder
  profileVisibility?: Prisma.SortOrder
  showEmail?: Prisma.SortOrder
  showPhone?: Prisma.SortOrder
  showLocation?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type AlumniProfileMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  firstName?: Prisma.SortOrder
  lastName?: Prisma.SortOrder
  displayName?: Prisma.SortOrder
  bio?: Prisma.SortOrder
  profilePicture?: Prisma.SortOrder
  primaryOrganizationId?: Prisma.SortOrder
  graduationYear?: Prisma.SortOrder
  programType?: Prisma.SortOrder
  centerLocation?: Prisma.SortOrder
  achievements?: Prisma.SortOrder
  currentPosition?: Prisma.SortOrder
  currentCompany?: Prisma.SortOrder
  industry?: Prisma.SortOrder
  location?: Prisma.SortOrder
  linkedInUrl?: Prisma.SortOrder
  phoneNumber?: Prisma.SortOrder
  mentorshipOffered?: Prisma.SortOrder
  mentorshipSought?: Prisma.SortOrder
  skillsOffered?: Prisma.SortOrder
  skillsWanted?: Prisma.SortOrder
  profileVisibility?: Prisma.SortOrder
  showEmail?: Prisma.SortOrder
  showPhone?: Prisma.SortOrder
  showLocation?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type AlumniProfileSumOrderByAggregateInput = {
  graduationYear?: Prisma.SortOrder
}

export type AlumniProfileScalarRelationFilter = {
  is?: Prisma.AlumniProfileWhereInput
  isNot?: Prisma.AlumniProfileWhereInput
}

export type AlumniProfileCreateNestedOneWithoutUserInput = {
  create?: Prisma.XOR<Prisma.AlumniProfileCreateWithoutUserInput, Prisma.AlumniProfileUncheckedCreateWithoutUserInput>
  connectOrCreate?: Prisma.AlumniProfileCreateOrConnectWithoutUserInput
  connect?: Prisma.AlumniProfileWhereUniqueInput
}

export type AlumniProfileUncheckedCreateNestedOneWithoutUserInput = {
  create?: Prisma.XOR<Prisma.AlumniProfileCreateWithoutUserInput, Prisma.AlumniProfileUncheckedCreateWithoutUserInput>
  connectOrCreate?: Prisma.AlumniProfileCreateOrConnectWithoutUserInput
  connect?: Prisma.AlumniProfileWhereUniqueInput
}

export type AlumniProfileUpdateOneWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.AlumniProfileCreateWithoutUserInput, Prisma.AlumniProfileUncheckedCreateWithoutUserInput>
  connectOrCreate?: Prisma.AlumniProfileCreateOrConnectWithoutUserInput
  upsert?: Prisma.AlumniProfileUpsertWithoutUserInput
  disconnect?: Prisma.AlumniProfileWhereInput | boolean
  delete?: Prisma.AlumniProfileWhereInput | boolean
  connect?: Prisma.AlumniProfileWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.AlumniProfileUpdateToOneWithWhereWithoutUserInput, Prisma.AlumniProfileUpdateWithoutUserInput>, Prisma.AlumniProfileUncheckedUpdateWithoutUserInput>
}

export type AlumniProfileUncheckedUpdateOneWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.AlumniProfileCreateWithoutUserInput, Prisma.AlumniProfileUncheckedCreateWithoutUserInput>
  connectOrCreate?: Prisma.AlumniProfileCreateOrConnectWithoutUserInput
  upsert?: Prisma.AlumniProfileUpsertWithoutUserInput
  disconnect?: Prisma.AlumniProfileWhereInput | boolean
  delete?: Prisma.AlumniProfileWhereInput | boolean
  connect?: Prisma.AlumniProfileWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.AlumniProfileUpdateToOneWithWhereWithoutUserInput, Prisma.AlumniProfileUpdateWithoutUserInput>, Prisma.AlumniProfileUncheckedUpdateWithoutUserInput>
}

export type AlumniProfileCreateNestedManyWithoutPrimaryOrganizationInput = {
  create?: Prisma.XOR<Prisma.AlumniProfileCreateWithoutPrimaryOrganizationInput, Prisma.AlumniProfileUncheckedCreateWithoutPrimaryOrganizationInput> | Prisma.AlumniProfileCreateWithoutPrimaryOrganizationInput[] | Prisma.AlumniProfileUncheckedCreateWithoutPrimaryOrganizationInput[]
  connectOrCreate?: Prisma.AlumniProfileCreateOrConnectWithoutPrimaryOrganizationInput | Prisma.AlumniProfileCreateOrConnectWithoutPrimaryOrganizationInput[]
  createMany?: Prisma.AlumniProfileCreateManyPrimaryOrganizationInputEnvelope
  connect?: Prisma.AlumniProfileWhereUniqueInput | Prisma.AlumniProfileWhereUniqueInput[]
}

export type AlumniProfileUncheckedCreateNestedManyWithoutPrimaryOrganizationInput = {
  create?: Prisma.XOR<Prisma.AlumniProfileCreateWithoutPrimaryOrganizationInput, Prisma.AlumniProfileUncheckedCreateWithoutPrimaryOrganizationInput> | Prisma.AlumniProfileCreateWithoutPrimaryOrganizationInput[] | Prisma.AlumniProfileUncheckedCreateWithoutPrimaryOrganizationInput[]
  connectOrCreate?: Prisma.AlumniProfileCreateOrConnectWithoutPrimaryOrganizationInput | Prisma.AlumniProfileCreateOrConnectWithoutPrimaryOrganizationInput[]
  createMany?: Prisma.AlumniProfileCreateManyPrimaryOrganizationInputEnvelope
  connect?: Prisma.AlumniProfileWhereUniqueInput | Prisma.AlumniProfileWhereUniqueInput[]
}

export type AlumniProfileUpdateManyWithoutPrimaryOrganizationNestedInput = {
  create?: Prisma.XOR<Prisma.AlumniProfileCreateWithoutPrimaryOrganizationInput, Prisma.AlumniProfileUncheckedCreateWithoutPrimaryOrganizationInput> | Prisma.AlumniProfileCreateWithoutPrimaryOrganizationInput[] | Prisma.AlumniProfileUncheckedCreateWithoutPrimaryOrganizationInput[]
  connectOrCreate?: Prisma.AlumniProfileCreateOrConnectWithoutPrimaryOrganizationInput | Prisma.AlumniProfileCreateOrConnectWithoutPrimaryOrganizationInput[]
  upsert?: Prisma.AlumniProfileUpsertWithWhereUniqueWithoutPrimaryOrganizationInput | Prisma.AlumniProfileUpsertWithWhereUniqueWithoutPrimaryOrganizationInput[]
  createMany?: Prisma.AlumniProfileCreateManyPrimaryOrganizationInputEnvelope
  set?: Prisma.AlumniProfileWhereUniqueInput | Prisma.AlumniProfileWhereUniqueInput[]
  disconnect?: Prisma.AlumniProfileWhereUniqueInput | Prisma.AlumniProfileWhereUniqueInput[]
  delete?: Prisma.AlumniProfileWhereUniqueInput | Prisma.AlumniProfileWhereUniqueInput[]
  connect?: Prisma.AlumniProfileWhereUniqueInput | Prisma.AlumniProfileWhereUniqueInput[]
  update?: Prisma.AlumniProfileUpdateWithWhereUniqueWithoutPrimaryOrganizationInput | Prisma.AlumniProfileUpdateWithWhereUniqueWithoutPrimaryOrganizationInput[]
  updateMany?: Prisma.AlumniProfileUpdateManyWithWhereWithoutPrimaryOrganizationInput | Prisma.AlumniProfileUpdateManyWithWhereWithoutPrimaryOrganizationInput[]
  deleteMany?: Prisma.AlumniProfileScalarWhereInput | Prisma.AlumniProfileScalarWhereInput[]
}

export type AlumniProfileUncheckedUpdateManyWithoutPrimaryOrganizationNestedInput = {
  create?: Prisma.XOR<Prisma.AlumniProfileCreateWithoutPrimaryOrganizationInput, Prisma.AlumniProfileUncheckedCreateWithoutPrimaryOrganizationInput> | Prisma.AlumniProfileCreateWithoutPrimaryOrganizationInput[] | Prisma.AlumniProfileUncheckedCreateWithoutPrimaryOrganizationInput[]
  connectOrCreate?: Prisma.AlumniProfileCreateOrConnectWithoutPrimaryOrganizationInput | Prisma.AlumniProfileCreateOrConnectWithoutPrimaryOrganizationInput[]
  upsert?: Prisma.AlumniProfileUpsertWithWhereUniqueWithoutPrimaryOrganizationInput | Prisma.AlumniProfileUpsertWithWhereUniqueWithoutPrimaryOrganizationInput[]
  createMany?: Prisma.AlumniProfileCreateManyPrimaryOrganizationInputEnvelope
  set?: Prisma.AlumniProfileWhereUniqueInput | Prisma.AlumniProfileWhereUniqueInput[]
  disconnect?: Prisma.AlumniProfileWhereUniqueInput | Prisma.AlumniProfileWhereUniqueInput[]
  delete?: Prisma.AlumniProfileWhereUniqueInput | Prisma.AlumniProfileWhereUniqueInput[]
  connect?: Prisma.AlumniProfileWhereUniqueInput | Prisma.AlumniProfileWhereUniqueInput[]
  update?: Prisma.AlumniProfileUpdateWithWhereUniqueWithoutPrimaryOrganizationInput | Prisma.AlumniProfileUpdateWithWhereUniqueWithoutPrimaryOrganizationInput[]
  updateMany?: Prisma.AlumniProfileUpdateManyWithWhereWithoutPrimaryOrganizationInput | Prisma.AlumniProfileUpdateManyWithWhereWithoutPrimaryOrganizationInput[]
  deleteMany?: Prisma.AlumniProfileScalarWhereInput | Prisma.AlumniProfileScalarWhereInput[]
}

export type EnumProfileVisibilityFieldUpdateOperationsInput = {
  set?: $Enums.ProfileVisibility
}

export type AlumniProfileCreateNestedOneWithoutConnectionsInput = {
  create?: Prisma.XOR<Prisma.AlumniProfileCreateWithoutConnectionsInput, Prisma.AlumniProfileUncheckedCreateWithoutConnectionsInput>
  connectOrCreate?: Prisma.AlumniProfileCreateOrConnectWithoutConnectionsInput
  connect?: Prisma.AlumniProfileWhereUniqueInput
}

export type AlumniProfileCreateNestedOneWithoutConnectionRequestsInput = {
  create?: Prisma.XOR<Prisma.AlumniProfileCreateWithoutConnectionRequestsInput, Prisma.AlumniProfileUncheckedCreateWithoutConnectionRequestsInput>
  connectOrCreate?: Prisma.AlumniProfileCreateOrConnectWithoutConnectionRequestsInput
  connect?: Prisma.AlumniProfileWhereUniqueInput
}

export type AlumniProfileUpdateOneRequiredWithoutConnectionsNestedInput = {
  create?: Prisma.XOR<Prisma.AlumniProfileCreateWithoutConnectionsInput, Prisma.AlumniProfileUncheckedCreateWithoutConnectionsInput>
  connectOrCreate?: Prisma.AlumniProfileCreateOrConnectWithoutConnectionsInput
  upsert?: Prisma.AlumniProfileUpsertWithoutConnectionsInput
  connect?: Prisma.AlumniProfileWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.AlumniProfileUpdateToOneWithWhereWithoutConnectionsInput, Prisma.AlumniProfileUpdateWithoutConnectionsInput>, Prisma.AlumniProfileUncheckedUpdateWithoutConnectionsInput>
}

export type AlumniProfileUpdateOneRequiredWithoutConnectionRequestsNestedInput = {
  create?: Prisma.XOR<Prisma.AlumniProfileCreateWithoutConnectionRequestsInput, Prisma.AlumniProfileUncheckedCreateWithoutConnectionRequestsInput>
  connectOrCreate?: Prisma.AlumniProfileCreateOrConnectWithoutConnectionRequestsInput
  upsert?: Prisma.AlumniProfileUpsertWithoutConnectionRequestsInput
  connect?: Prisma.AlumniProfileWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.AlumniProfileUpdateToOneWithWhereWithoutConnectionRequestsInput, Prisma.AlumniProfileUpdateWithoutConnectionRequestsInput>, Prisma.AlumniProfileUncheckedUpdateWithoutConnectionRequestsInput>
}

export type AlumniProfileCreateNestedOneWithoutMessagesInput = {
  create?: Prisma.XOR<Prisma.AlumniProfileCreateWithoutMessagesInput, Prisma.AlumniProfileUncheckedCreateWithoutMessagesInput>
  connectOrCreate?: Prisma.AlumniProfileCreateOrConnectWithoutMessagesInput
  connect?: Prisma.AlumniProfileWhereUniqueInput
}

export type AlumniProfileCreateNestedOneWithoutReceivedMessagesInput = {
  create?: Prisma.XOR<Prisma.AlumniProfileCreateWithoutReceivedMessagesInput, Prisma.AlumniProfileUncheckedCreateWithoutReceivedMessagesInput>
  connectOrCreate?: Prisma.AlumniProfileCreateOrConnectWithoutReceivedMessagesInput
  connect?: Prisma.AlumniProfileWhereUniqueInput
}

export type AlumniProfileUpdateOneRequiredWithoutMessagesNestedInput = {
  create?: Prisma.XOR<Prisma.AlumniProfileCreateWithoutMessagesInput, Prisma.AlumniProfileUncheckedCreateWithoutMessagesInput>
  connectOrCreate?: Prisma.AlumniProfileCreateOrConnectWithoutMessagesInput
  upsert?: Prisma.AlumniProfileUpsertWithoutMessagesInput
  connect?: Prisma.AlumniProfileWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.AlumniProfileUpdateToOneWithWhereWithoutMessagesInput, Prisma.AlumniProfileUpdateWithoutMessagesInput>, Prisma.AlumniProfileUncheckedUpdateWithoutMessagesInput>
}

export type AlumniProfileUpdateOneRequiredWithoutReceivedMessagesNestedInput = {
  create?: Prisma.XOR<Prisma.AlumniProfileCreateWithoutReceivedMessagesInput, Prisma.AlumniProfileUncheckedCreateWithoutReceivedMessagesInput>
  connectOrCreate?: Prisma.AlumniProfileCreateOrConnectWithoutReceivedMessagesInput
  upsert?: Prisma.AlumniProfileUpsertWithoutReceivedMessagesInput
  connect?: Prisma.AlumniProfileWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.AlumniProfileUpdateToOneWithWhereWithoutReceivedMessagesInput, Prisma.AlumniProfileUpdateWithoutReceivedMessagesInput>, Prisma.AlumniProfileUncheckedUpdateWithoutReceivedMessagesInput>
}

export type AlumniProfileCreateNestedOneWithoutPostsInput = {
  create?: Prisma.XOR<Prisma.AlumniProfileCreateWithoutPostsInput, Prisma.AlumniProfileUncheckedCreateWithoutPostsInput>
  connectOrCreate?: Prisma.AlumniProfileCreateOrConnectWithoutPostsInput
  connect?: Prisma.AlumniProfileWhereUniqueInput
}

export type AlumniProfileUpdateOneRequiredWithoutPostsNestedInput = {
  create?: Prisma.XOR<Prisma.AlumniProfileCreateWithoutPostsInput, Prisma.AlumniProfileUncheckedCreateWithoutPostsInput>
  connectOrCreate?: Prisma.AlumniProfileCreateOrConnectWithoutPostsInput
  upsert?: Prisma.AlumniProfileUpsertWithoutPostsInput
  connect?: Prisma.AlumniProfileWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.AlumniProfileUpdateToOneWithWhereWithoutPostsInput, Prisma.AlumniProfileUpdateWithoutPostsInput>, Prisma.AlumniProfileUncheckedUpdateWithoutPostsInput>
}

export type AlumniProfileCreateNestedOneWithoutPostLikesInput = {
  create?: Prisma.XOR<Prisma.AlumniProfileCreateWithoutPostLikesInput, Prisma.AlumniProfileUncheckedCreateWithoutPostLikesInput>
  connectOrCreate?: Prisma.AlumniProfileCreateOrConnectWithoutPostLikesInput
  connect?: Prisma.AlumniProfileWhereUniqueInput
}

export type AlumniProfileUpdateOneRequiredWithoutPostLikesNestedInput = {
  create?: Prisma.XOR<Prisma.AlumniProfileCreateWithoutPostLikesInput, Prisma.AlumniProfileUncheckedCreateWithoutPostLikesInput>
  connectOrCreate?: Prisma.AlumniProfileCreateOrConnectWithoutPostLikesInput
  upsert?: Prisma.AlumniProfileUpsertWithoutPostLikesInput
  connect?: Prisma.AlumniProfileWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.AlumniProfileUpdateToOneWithWhereWithoutPostLikesInput, Prisma.AlumniProfileUpdateWithoutPostLikesInput>, Prisma.AlumniProfileUncheckedUpdateWithoutPostLikesInput>
}

export type AlumniProfileCreateNestedOneWithoutCommentsInput = {
  create?: Prisma.XOR<Prisma.AlumniProfileCreateWithoutCommentsInput, Prisma.AlumniProfileUncheckedCreateWithoutCommentsInput>
  connectOrCreate?: Prisma.AlumniProfileCreateOrConnectWithoutCommentsInput
  connect?: Prisma.AlumniProfileWhereUniqueInput
}

export type AlumniProfileUpdateOneRequiredWithoutCommentsNestedInput = {
  create?: Prisma.XOR<Prisma.AlumniProfileCreateWithoutCommentsInput, Prisma.AlumniProfileUncheckedCreateWithoutCommentsInput>
  connectOrCreate?: Prisma.AlumniProfileCreateOrConnectWithoutCommentsInput
  upsert?: Prisma.AlumniProfileUpsertWithoutCommentsInput
  connect?: Prisma.AlumniProfileWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.AlumniProfileUpdateToOneWithWhereWithoutCommentsInput, Prisma.AlumniProfileUpdateWithoutCommentsInput>, Prisma.AlumniProfileUncheckedUpdateWithoutCommentsInput>
}

export type AlumniProfileCreateNestedOneWithoutEventRegistrationsInput = {
  create?: Prisma.XOR<Prisma.AlumniProfileCreateWithoutEventRegistrationsInput, Prisma.AlumniProfileUncheckedCreateWithoutEventRegistrationsInput>
  connectOrCreate?: Prisma.AlumniProfileCreateOrConnectWithoutEventRegistrationsInput
  connect?: Prisma.AlumniProfileWhereUniqueInput
}

export type AlumniProfileUpdateOneRequiredWithoutEventRegistrationsNestedInput = {
  create?: Prisma.XOR<Prisma.AlumniProfileCreateWithoutEventRegistrationsInput, Prisma.AlumniProfileUncheckedCreateWithoutEventRegistrationsInput>
  connectOrCreate?: Prisma.AlumniProfileCreateOrConnectWithoutEventRegistrationsInput
  upsert?: Prisma.AlumniProfileUpsertWithoutEventRegistrationsInput
  connect?: Prisma.AlumniProfileWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.AlumniProfileUpdateToOneWithWhereWithoutEventRegistrationsInput, Prisma.AlumniProfileUpdateWithoutEventRegistrationsInput>, Prisma.AlumniProfileUncheckedUpdateWithoutEventRegistrationsInput>
}

export type AlumniProfileCreateNestedOneWithoutDonationsInput = {
  create?: Prisma.XOR<Prisma.AlumniProfileCreateWithoutDonationsInput, Prisma.AlumniProfileUncheckedCreateWithoutDonationsInput>
  connectOrCreate?: Prisma.AlumniProfileCreateOrConnectWithoutDonationsInput
  connect?: Prisma.AlumniProfileWhereUniqueInput
}

export type AlumniProfileUpdateOneRequiredWithoutDonationsNestedInput = {
  create?: Prisma.XOR<Prisma.AlumniProfileCreateWithoutDonationsInput, Prisma.AlumniProfileUncheckedCreateWithoutDonationsInput>
  connectOrCreate?: Prisma.AlumniProfileCreateOrConnectWithoutDonationsInput
  upsert?: Prisma.AlumniProfileUpsertWithoutDonationsInput
  connect?: Prisma.AlumniProfileWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.AlumniProfileUpdateToOneWithWhereWithoutDonationsInput, Prisma.AlumniProfileUpdateWithoutDonationsInput>, Prisma.AlumniProfileUncheckedUpdateWithoutDonationsInput>
}

export type AlumniProfileCreateNestedOneWithoutNotificationsInput = {
  create?: Prisma.XOR<Prisma.AlumniProfileCreateWithoutNotificationsInput, Prisma.AlumniProfileUncheckedCreateWithoutNotificationsInput>
  connectOrCreate?: Prisma.AlumniProfileCreateOrConnectWithoutNotificationsInput
  connect?: Prisma.AlumniProfileWhereUniqueInput
}

export type AlumniProfileUpdateOneRequiredWithoutNotificationsNestedInput = {
  create?: Prisma.XOR<Prisma.AlumniProfileCreateWithoutNotificationsInput, Prisma.AlumniProfileUncheckedCreateWithoutNotificationsInput>
  connectOrCreate?: Prisma.AlumniProfileCreateOrConnectWithoutNotificationsInput
  upsert?: Prisma.AlumniProfileUpsertWithoutNotificationsInput
  connect?: Prisma.AlumniProfileWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.AlumniProfileUpdateToOneWithWhereWithoutNotificationsInput, Prisma.AlumniProfileUpdateWithoutNotificationsInput>, Prisma.AlumniProfileUncheckedUpdateWithoutNotificationsInput>
}

export type AlumniProfileCreateWithoutUserInput = {
  id?: string
  firstName: string
  lastName: string
  displayName?: string | null
  bio?: string | null
  profilePicture?: string | null
  graduationYear: number
  programType: string
  centerLocation: string
  achievements?: string | null
  currentPosition?: string | null
  currentCompany?: string | null
  industry?: string | null
  location?: string | null
  linkedInUrl?: string | null
  phoneNumber?: string | null
  mentorshipOffered?: boolean
  mentorshipSought?: boolean
  skillsOffered?: string | null
  skillsWanted?: string | null
  profileVisibility?: $Enums.ProfileVisibility
  showEmail?: boolean
  showPhone?: boolean
  showLocation?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  primaryOrganization?: Prisma.OrganizationCreateNestedOneWithoutAlumniProfilesInput
  posts?: Prisma.PostCreateNestedManyWithoutAuthorInput
  connections?: Prisma.ConnectionCreateNestedManyWithoutRequesterInput
  connectionRequests?: Prisma.ConnectionCreateNestedManyWithoutRequestedInput
  eventRegistrations?: Prisma.EventRegistrationCreateNestedManyWithoutAttendeeInput
  donations?: Prisma.DonationCreateNestedManyWithoutDonorInput
  messages?: Prisma.MessageCreateNestedManyWithoutSenderInput
  receivedMessages?: Prisma.MessageCreateNestedManyWithoutReceiverInput
  postLikes?: Prisma.PostLikeCreateNestedManyWithoutUserInput
  comments?: Prisma.CommentCreateNestedManyWithoutAuthorInput
  notifications?: Prisma.NotificationCreateNestedManyWithoutRecipientInput
}

export type AlumniProfileUncheckedCreateWithoutUserInput = {
  id?: string
  firstName: string
  lastName: string
  displayName?: string | null
  bio?: string | null
  profilePicture?: string | null
  primaryOrganizationId?: string | null
  graduationYear: number
  programType: string
  centerLocation: string
  achievements?: string | null
  currentPosition?: string | null
  currentCompany?: string | null
  industry?: string | null
  location?: string | null
  linkedInUrl?: string | null
  phoneNumber?: string | null
  mentorshipOffered?: boolean
  mentorshipSought?: boolean
  skillsOffered?: string | null
  skillsWanted?: string | null
  profileVisibility?: $Enums.ProfileVisibility
  showEmail?: boolean
  showPhone?: boolean
  showLocation?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  posts?: Prisma.PostUncheckedCreateNestedManyWithoutAuthorInput
  connections?: Prisma.ConnectionUncheckedCreateNestedManyWithoutRequesterInput
  connectionRequests?: Prisma.ConnectionUncheckedCreateNestedManyWithoutRequestedInput
  eventRegistrations?: Prisma.EventRegistrationUncheckedCreateNestedManyWithoutAttendeeInput
  donations?: Prisma.DonationUncheckedCreateNestedManyWithoutDonorInput
  messages?: Prisma.MessageUncheckedCreateNestedManyWithoutSenderInput
  receivedMessages?: Prisma.MessageUncheckedCreateNestedManyWithoutReceiverInput
  postLikes?: Prisma.PostLikeUncheckedCreateNestedManyWithoutUserInput
  comments?: Prisma.CommentUncheckedCreateNestedManyWithoutAuthorInput
  notifications?: Prisma.NotificationUncheckedCreateNestedManyWithoutRecipientInput
}

export type AlumniProfileCreateOrConnectWithoutUserInput = {
  where: Prisma.AlumniProfileWhereUniqueInput
  create: Prisma.XOR<Prisma.AlumniProfileCreateWithoutUserInput, Prisma.AlumniProfileUncheckedCreateWithoutUserInput>
}

export type AlumniProfileUpsertWithoutUserInput = {
  update: Prisma.XOR<Prisma.AlumniProfileUpdateWithoutUserInput, Prisma.AlumniProfileUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.AlumniProfileCreateWithoutUserInput, Prisma.AlumniProfileUncheckedCreateWithoutUserInput>
  where?: Prisma.AlumniProfileWhereInput
}

export type AlumniProfileUpdateToOneWithWhereWithoutUserInput = {
  where?: Prisma.AlumniProfileWhereInput
  data: Prisma.XOR<Prisma.AlumniProfileUpdateWithoutUserInput, Prisma.AlumniProfileUncheckedUpdateWithoutUserInput>
}

export type AlumniProfileUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bio?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profilePicture?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  graduationYear?: Prisma.IntFieldUpdateOperationsInput | number
  programType?: Prisma.StringFieldUpdateOperationsInput | string
  centerLocation?: Prisma.StringFieldUpdateOperationsInput | string
  achievements?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentPosition?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentCompany?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  industry?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  linkedInUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phoneNumber?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  mentorshipOffered?: Prisma.BoolFieldUpdateOperationsInput | boolean
  mentorshipSought?: Prisma.BoolFieldUpdateOperationsInput | boolean
  skillsOffered?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillsWanted?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profileVisibility?: Prisma.EnumProfileVisibilityFieldUpdateOperationsInput | $Enums.ProfileVisibility
  showEmail?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showPhone?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showLocation?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  primaryOrganization?: Prisma.OrganizationUpdateOneWithoutAlumniProfilesNestedInput
  posts?: Prisma.PostUpdateManyWithoutAuthorNestedInput
  connections?: Prisma.ConnectionUpdateManyWithoutRequesterNestedInput
  connectionRequests?: Prisma.ConnectionUpdateManyWithoutRequestedNestedInput
  eventRegistrations?: Prisma.EventRegistrationUpdateManyWithoutAttendeeNestedInput
  donations?: Prisma.DonationUpdateManyWithoutDonorNestedInput
  messages?: Prisma.MessageUpdateManyWithoutSenderNestedInput
  receivedMessages?: Prisma.MessageUpdateManyWithoutReceiverNestedInput
  postLikes?: Prisma.PostLikeUpdateManyWithoutUserNestedInput
  comments?: Prisma.CommentUpdateManyWithoutAuthorNestedInput
  notifications?: Prisma.NotificationUpdateManyWithoutRecipientNestedInput
}

export type AlumniProfileUncheckedUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bio?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profilePicture?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  primaryOrganizationId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  graduationYear?: Prisma.IntFieldUpdateOperationsInput | number
  programType?: Prisma.StringFieldUpdateOperationsInput | string
  centerLocation?: Prisma.StringFieldUpdateOperationsInput | string
  achievements?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentPosition?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentCompany?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  industry?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  linkedInUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phoneNumber?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  mentorshipOffered?: Prisma.BoolFieldUpdateOperationsInput | boolean
  mentorshipSought?: Prisma.BoolFieldUpdateOperationsInput | boolean
  skillsOffered?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillsWanted?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profileVisibility?: Prisma.EnumProfileVisibilityFieldUpdateOperationsInput | $Enums.ProfileVisibility
  showEmail?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showPhone?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showLocation?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  posts?: Prisma.PostUncheckedUpdateManyWithoutAuthorNestedInput
  connections?: Prisma.ConnectionUncheckedUpdateManyWithoutRequesterNestedInput
  connectionRequests?: Prisma.ConnectionUncheckedUpdateManyWithoutRequestedNestedInput
  eventRegistrations?: Prisma.EventRegistrationUncheckedUpdateManyWithoutAttendeeNestedInput
  donations?: Prisma.DonationUncheckedUpdateManyWithoutDonorNestedInput
  messages?: Prisma.MessageUncheckedUpdateManyWithoutSenderNestedInput
  receivedMessages?: Prisma.MessageUncheckedUpdateManyWithoutReceiverNestedInput
  postLikes?: Prisma.PostLikeUncheckedUpdateManyWithoutUserNestedInput
  comments?: Prisma.CommentUncheckedUpdateManyWithoutAuthorNestedInput
  notifications?: Prisma.NotificationUncheckedUpdateManyWithoutRecipientNestedInput
}

export type AlumniProfileCreateWithoutPrimaryOrganizationInput = {
  id?: string
  firstName: string
  lastName: string
  displayName?: string | null
  bio?: string | null
  profilePicture?: string | null
  graduationYear: number
  programType: string
  centerLocation: string
  achievements?: string | null
  currentPosition?: string | null
  currentCompany?: string | null
  industry?: string | null
  location?: string | null
  linkedInUrl?: string | null
  phoneNumber?: string | null
  mentorshipOffered?: boolean
  mentorshipSought?: boolean
  skillsOffered?: string | null
  skillsWanted?: string | null
  profileVisibility?: $Enums.ProfileVisibility
  showEmail?: boolean
  showPhone?: boolean
  showLocation?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  user: Prisma.UserCreateNestedOneWithoutAlumniProfileInput
  posts?: Prisma.PostCreateNestedManyWithoutAuthorInput
  connections?: Prisma.ConnectionCreateNestedManyWithoutRequesterInput
  connectionRequests?: Prisma.ConnectionCreateNestedManyWithoutRequestedInput
  eventRegistrations?: Prisma.EventRegistrationCreateNestedManyWithoutAttendeeInput
  donations?: Prisma.DonationCreateNestedManyWithoutDonorInput
  messages?: Prisma.MessageCreateNestedManyWithoutSenderInput
  receivedMessages?: Prisma.MessageCreateNestedManyWithoutReceiverInput
  postLikes?: Prisma.PostLikeCreateNestedManyWithoutUserInput
  comments?: Prisma.CommentCreateNestedManyWithoutAuthorInput
  notifications?: Prisma.NotificationCreateNestedManyWithoutRecipientInput
}

export type AlumniProfileUncheckedCreateWithoutPrimaryOrganizationInput = {
  id?: string
  userId: string
  firstName: string
  lastName: string
  displayName?: string | null
  bio?: string | null
  profilePicture?: string | null
  graduationYear: number
  programType: string
  centerLocation: string
  achievements?: string | null
  currentPosition?: string | null
  currentCompany?: string | null
  industry?: string | null
  location?: string | null
  linkedInUrl?: string | null
  phoneNumber?: string | null
  mentorshipOffered?: boolean
  mentorshipSought?: boolean
  skillsOffered?: string | null
  skillsWanted?: string | null
  profileVisibility?: $Enums.ProfileVisibility
  showEmail?: boolean
  showPhone?: boolean
  showLocation?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  posts?: Prisma.PostUncheckedCreateNestedManyWithoutAuthorInput
  connections?: Prisma.ConnectionUncheckedCreateNestedManyWithoutRequesterInput
  connectionRequests?: Prisma.ConnectionUncheckedCreateNestedManyWithoutRequestedInput
  eventRegistrations?: Prisma.EventRegistrationUncheckedCreateNestedManyWithoutAttendeeInput
  donations?: Prisma.DonationUncheckedCreateNestedManyWithoutDonorInput
  messages?: Prisma.MessageUncheckedCreateNestedManyWithoutSenderInput
  receivedMessages?: Prisma.MessageUncheckedCreateNestedManyWithoutReceiverInput
  postLikes?: Prisma.PostLikeUncheckedCreateNestedManyWithoutUserInput
  comments?: Prisma.CommentUncheckedCreateNestedManyWithoutAuthorInput
  notifications?: Prisma.NotificationUncheckedCreateNestedManyWithoutRecipientInput
}

export type AlumniProfileCreateOrConnectWithoutPrimaryOrganizationInput = {
  where: Prisma.AlumniProfileWhereUniqueInput
  create: Prisma.XOR<Prisma.AlumniProfileCreateWithoutPrimaryOrganizationInput, Prisma.AlumniProfileUncheckedCreateWithoutPrimaryOrganizationInput>
}

export type AlumniProfileCreateManyPrimaryOrganizationInputEnvelope = {
  data: Prisma.AlumniProfileCreateManyPrimaryOrganizationInput | Prisma.AlumniProfileCreateManyPrimaryOrganizationInput[]
  skipDuplicates?: boolean
}

export type AlumniProfileUpsertWithWhereUniqueWithoutPrimaryOrganizationInput = {
  where: Prisma.AlumniProfileWhereUniqueInput
  update: Prisma.XOR<Prisma.AlumniProfileUpdateWithoutPrimaryOrganizationInput, Prisma.AlumniProfileUncheckedUpdateWithoutPrimaryOrganizationInput>
  create: Prisma.XOR<Prisma.AlumniProfileCreateWithoutPrimaryOrganizationInput, Prisma.AlumniProfileUncheckedCreateWithoutPrimaryOrganizationInput>
}

export type AlumniProfileUpdateWithWhereUniqueWithoutPrimaryOrganizationInput = {
  where: Prisma.AlumniProfileWhereUniqueInput
  data: Prisma.XOR<Prisma.AlumniProfileUpdateWithoutPrimaryOrganizationInput, Prisma.AlumniProfileUncheckedUpdateWithoutPrimaryOrganizationInput>
}

export type AlumniProfileUpdateManyWithWhereWithoutPrimaryOrganizationInput = {
  where: Prisma.AlumniProfileScalarWhereInput
  data: Prisma.XOR<Prisma.AlumniProfileUpdateManyMutationInput, Prisma.AlumniProfileUncheckedUpdateManyWithoutPrimaryOrganizationInput>
}

export type AlumniProfileScalarWhereInput = {
  AND?: Prisma.AlumniProfileScalarWhereInput | Prisma.AlumniProfileScalarWhereInput[]
  OR?: Prisma.AlumniProfileScalarWhereInput[]
  NOT?: Prisma.AlumniProfileScalarWhereInput | Prisma.AlumniProfileScalarWhereInput[]
  id?: Prisma.StringFilter<"AlumniProfile"> | string
  userId?: Prisma.StringFilter<"AlumniProfile"> | string
  firstName?: Prisma.StringFilter<"AlumniProfile"> | string
  lastName?: Prisma.StringFilter<"AlumniProfile"> | string
  displayName?: Prisma.StringNullableFilter<"AlumniProfile"> | string | null
  bio?: Prisma.StringNullableFilter<"AlumniProfile"> | string | null
  profilePicture?: Prisma.StringNullableFilter<"AlumniProfile"> | string | null
  primaryOrganizationId?: Prisma.StringNullableFilter<"AlumniProfile"> | string | null
  graduationYear?: Prisma.IntFilter<"AlumniProfile"> | number
  programType?: Prisma.StringFilter<"AlumniProfile"> | string
  centerLocation?: Prisma.StringFilter<"AlumniProfile"> | string
  achievements?: Prisma.StringNullableFilter<"AlumniProfile"> | string | null
  currentPosition?: Prisma.StringNullableFilter<"AlumniProfile"> | string | null
  currentCompany?: Prisma.StringNullableFilter<"AlumniProfile"> | string | null
  industry?: Prisma.StringNullableFilter<"AlumniProfile"> | string | null
  location?: Prisma.StringNullableFilter<"AlumniProfile"> | string | null
  linkedInUrl?: Prisma.StringNullableFilter<"AlumniProfile"> | string | null
  phoneNumber?: Prisma.StringNullableFilter<"AlumniProfile"> | string | null
  mentorshipOffered?: Prisma.BoolFilter<"AlumniProfile"> | boolean
  mentorshipSought?: Prisma.BoolFilter<"AlumniProfile"> | boolean
  skillsOffered?: Prisma.StringNullableFilter<"AlumniProfile"> | string | null
  skillsWanted?: Prisma.StringNullableFilter<"AlumniProfile"> | string | null
  profileVisibility?: Prisma.EnumProfileVisibilityFilter<"AlumniProfile"> | $Enums.ProfileVisibility
  showEmail?: Prisma.BoolFilter<"AlumniProfile"> | boolean
  showPhone?: Prisma.BoolFilter<"AlumniProfile"> | boolean
  showLocation?: Prisma.BoolFilter<"AlumniProfile"> | boolean
  createdAt?: Prisma.DateTimeFilter<"AlumniProfile"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"AlumniProfile"> | Date | string
}

export type AlumniProfileCreateWithoutConnectionsInput = {
  id?: string
  firstName: string
  lastName: string
  displayName?: string | null
  bio?: string | null
  profilePicture?: string | null
  graduationYear: number
  programType: string
  centerLocation: string
  achievements?: string | null
  currentPosition?: string | null
  currentCompany?: string | null
  industry?: string | null
  location?: string | null
  linkedInUrl?: string | null
  phoneNumber?: string | null
  mentorshipOffered?: boolean
  mentorshipSought?: boolean
  skillsOffered?: string | null
  skillsWanted?: string | null
  profileVisibility?: $Enums.ProfileVisibility
  showEmail?: boolean
  showPhone?: boolean
  showLocation?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  user: Prisma.UserCreateNestedOneWithoutAlumniProfileInput
  primaryOrganization?: Prisma.OrganizationCreateNestedOneWithoutAlumniProfilesInput
  posts?: Prisma.PostCreateNestedManyWithoutAuthorInput
  connectionRequests?: Prisma.ConnectionCreateNestedManyWithoutRequestedInput
  eventRegistrations?: Prisma.EventRegistrationCreateNestedManyWithoutAttendeeInput
  donations?: Prisma.DonationCreateNestedManyWithoutDonorInput
  messages?: Prisma.MessageCreateNestedManyWithoutSenderInput
  receivedMessages?: Prisma.MessageCreateNestedManyWithoutReceiverInput
  postLikes?: Prisma.PostLikeCreateNestedManyWithoutUserInput
  comments?: Prisma.CommentCreateNestedManyWithoutAuthorInput
  notifications?: Prisma.NotificationCreateNestedManyWithoutRecipientInput
}

export type AlumniProfileUncheckedCreateWithoutConnectionsInput = {
  id?: string
  userId: string
  firstName: string
  lastName: string
  displayName?: string | null
  bio?: string | null
  profilePicture?: string | null
  primaryOrganizationId?: string | null
  graduationYear: number
  programType: string
  centerLocation: string
  achievements?: string | null
  currentPosition?: string | null
  currentCompany?: string | null
  industry?: string | null
  location?: string | null
  linkedInUrl?: string | null
  phoneNumber?: string | null
  mentorshipOffered?: boolean
  mentorshipSought?: boolean
  skillsOffered?: string | null
  skillsWanted?: string | null
  profileVisibility?: $Enums.ProfileVisibility
  showEmail?: boolean
  showPhone?: boolean
  showLocation?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  posts?: Prisma.PostUncheckedCreateNestedManyWithoutAuthorInput
  connectionRequests?: Prisma.ConnectionUncheckedCreateNestedManyWithoutRequestedInput
  eventRegistrations?: Prisma.EventRegistrationUncheckedCreateNestedManyWithoutAttendeeInput
  donations?: Prisma.DonationUncheckedCreateNestedManyWithoutDonorInput
  messages?: Prisma.MessageUncheckedCreateNestedManyWithoutSenderInput
  receivedMessages?: Prisma.MessageUncheckedCreateNestedManyWithoutReceiverInput
  postLikes?: Prisma.PostLikeUncheckedCreateNestedManyWithoutUserInput
  comments?: Prisma.CommentUncheckedCreateNestedManyWithoutAuthorInput
  notifications?: Prisma.NotificationUncheckedCreateNestedManyWithoutRecipientInput
}

export type AlumniProfileCreateOrConnectWithoutConnectionsInput = {
  where: Prisma.AlumniProfileWhereUniqueInput
  create: Prisma.XOR<Prisma.AlumniProfileCreateWithoutConnectionsInput, Prisma.AlumniProfileUncheckedCreateWithoutConnectionsInput>
}

export type AlumniProfileCreateWithoutConnectionRequestsInput = {
  id?: string
  firstName: string
  lastName: string
  displayName?: string | null
  bio?: string | null
  profilePicture?: string | null
  graduationYear: number
  programType: string
  centerLocation: string
  achievements?: string | null
  currentPosition?: string | null
  currentCompany?: string | null
  industry?: string | null
  location?: string | null
  linkedInUrl?: string | null
  phoneNumber?: string | null
  mentorshipOffered?: boolean
  mentorshipSought?: boolean
  skillsOffered?: string | null
  skillsWanted?: string | null
  profileVisibility?: $Enums.ProfileVisibility
  showEmail?: boolean
  showPhone?: boolean
  showLocation?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  user: Prisma.UserCreateNestedOneWithoutAlumniProfileInput
  primaryOrganization?: Prisma.OrganizationCreateNestedOneWithoutAlumniProfilesInput
  posts?: Prisma.PostCreateNestedManyWithoutAuthorInput
  connections?: Prisma.ConnectionCreateNestedManyWithoutRequesterInput
  eventRegistrations?: Prisma.EventRegistrationCreateNestedManyWithoutAttendeeInput
  donations?: Prisma.DonationCreateNestedManyWithoutDonorInput
  messages?: Prisma.MessageCreateNestedManyWithoutSenderInput
  receivedMessages?: Prisma.MessageCreateNestedManyWithoutReceiverInput
  postLikes?: Prisma.PostLikeCreateNestedManyWithoutUserInput
  comments?: Prisma.CommentCreateNestedManyWithoutAuthorInput
  notifications?: Prisma.NotificationCreateNestedManyWithoutRecipientInput
}

export type AlumniProfileUncheckedCreateWithoutConnectionRequestsInput = {
  id?: string
  userId: string
  firstName: string
  lastName: string
  displayName?: string | null
  bio?: string | null
  profilePicture?: string | null
  primaryOrganizationId?: string | null
  graduationYear: number
  programType: string
  centerLocation: string
  achievements?: string | null
  currentPosition?: string | null
  currentCompany?: string | null
  industry?: string | null
  location?: string | null
  linkedInUrl?: string | null
  phoneNumber?: string | null
  mentorshipOffered?: boolean
  mentorshipSought?: boolean
  skillsOffered?: string | null
  skillsWanted?: string | null
  profileVisibility?: $Enums.ProfileVisibility
  showEmail?: boolean
  showPhone?: boolean
  showLocation?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  posts?: Prisma.PostUncheckedCreateNestedManyWithoutAuthorInput
  connections?: Prisma.ConnectionUncheckedCreateNestedManyWithoutRequesterInput
  eventRegistrations?: Prisma.EventRegistrationUncheckedCreateNestedManyWithoutAttendeeInput
  donations?: Prisma.DonationUncheckedCreateNestedManyWithoutDonorInput
  messages?: Prisma.MessageUncheckedCreateNestedManyWithoutSenderInput
  receivedMessages?: Prisma.MessageUncheckedCreateNestedManyWithoutReceiverInput
  postLikes?: Prisma.PostLikeUncheckedCreateNestedManyWithoutUserInput
  comments?: Prisma.CommentUncheckedCreateNestedManyWithoutAuthorInput
  notifications?: Prisma.NotificationUncheckedCreateNestedManyWithoutRecipientInput
}

export type AlumniProfileCreateOrConnectWithoutConnectionRequestsInput = {
  where: Prisma.AlumniProfileWhereUniqueInput
  create: Prisma.XOR<Prisma.AlumniProfileCreateWithoutConnectionRequestsInput, Prisma.AlumniProfileUncheckedCreateWithoutConnectionRequestsInput>
}

export type AlumniProfileUpsertWithoutConnectionsInput = {
  update: Prisma.XOR<Prisma.AlumniProfileUpdateWithoutConnectionsInput, Prisma.AlumniProfileUncheckedUpdateWithoutConnectionsInput>
  create: Prisma.XOR<Prisma.AlumniProfileCreateWithoutConnectionsInput, Prisma.AlumniProfileUncheckedCreateWithoutConnectionsInput>
  where?: Prisma.AlumniProfileWhereInput
}

export type AlumniProfileUpdateToOneWithWhereWithoutConnectionsInput = {
  where?: Prisma.AlumniProfileWhereInput
  data: Prisma.XOR<Prisma.AlumniProfileUpdateWithoutConnectionsInput, Prisma.AlumniProfileUncheckedUpdateWithoutConnectionsInput>
}

export type AlumniProfileUpdateWithoutConnectionsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bio?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profilePicture?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  graduationYear?: Prisma.IntFieldUpdateOperationsInput | number
  programType?: Prisma.StringFieldUpdateOperationsInput | string
  centerLocation?: Prisma.StringFieldUpdateOperationsInput | string
  achievements?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentPosition?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentCompany?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  industry?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  linkedInUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phoneNumber?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  mentorshipOffered?: Prisma.BoolFieldUpdateOperationsInput | boolean
  mentorshipSought?: Prisma.BoolFieldUpdateOperationsInput | boolean
  skillsOffered?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillsWanted?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profileVisibility?: Prisma.EnumProfileVisibilityFieldUpdateOperationsInput | $Enums.ProfileVisibility
  showEmail?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showPhone?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showLocation?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.UserUpdateOneRequiredWithoutAlumniProfileNestedInput
  primaryOrganization?: Prisma.OrganizationUpdateOneWithoutAlumniProfilesNestedInput
  posts?: Prisma.PostUpdateManyWithoutAuthorNestedInput
  connectionRequests?: Prisma.ConnectionUpdateManyWithoutRequestedNestedInput
  eventRegistrations?: Prisma.EventRegistrationUpdateManyWithoutAttendeeNestedInput
  donations?: Prisma.DonationUpdateManyWithoutDonorNestedInput
  messages?: Prisma.MessageUpdateManyWithoutSenderNestedInput
  receivedMessages?: Prisma.MessageUpdateManyWithoutReceiverNestedInput
  postLikes?: Prisma.PostLikeUpdateManyWithoutUserNestedInput
  comments?: Prisma.CommentUpdateManyWithoutAuthorNestedInput
  notifications?: Prisma.NotificationUpdateManyWithoutRecipientNestedInput
}

export type AlumniProfileUncheckedUpdateWithoutConnectionsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bio?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profilePicture?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  primaryOrganizationId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  graduationYear?: Prisma.IntFieldUpdateOperationsInput | number
  programType?: Prisma.StringFieldUpdateOperationsInput | string
  centerLocation?: Prisma.StringFieldUpdateOperationsInput | string
  achievements?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentPosition?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentCompany?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  industry?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  linkedInUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phoneNumber?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  mentorshipOffered?: Prisma.BoolFieldUpdateOperationsInput | boolean
  mentorshipSought?: Prisma.BoolFieldUpdateOperationsInput | boolean
  skillsOffered?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillsWanted?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profileVisibility?: Prisma.EnumProfileVisibilityFieldUpdateOperationsInput | $Enums.ProfileVisibility
  showEmail?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showPhone?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showLocation?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  posts?: Prisma.PostUncheckedUpdateManyWithoutAuthorNestedInput
  connectionRequests?: Prisma.ConnectionUncheckedUpdateManyWithoutRequestedNestedInput
  eventRegistrations?: Prisma.EventRegistrationUncheckedUpdateManyWithoutAttendeeNestedInput
  donations?: Prisma.DonationUncheckedUpdateManyWithoutDonorNestedInput
  messages?: Prisma.MessageUncheckedUpdateManyWithoutSenderNestedInput
  receivedMessages?: Prisma.MessageUncheckedUpdateManyWithoutReceiverNestedInput
  postLikes?: Prisma.PostLikeUncheckedUpdateManyWithoutUserNestedInput
  comments?: Prisma.CommentUncheckedUpdateManyWithoutAuthorNestedInput
  notifications?: Prisma.NotificationUncheckedUpdateManyWithoutRecipientNestedInput
}

export type AlumniProfileUpsertWithoutConnectionRequestsInput = {
  update: Prisma.XOR<Prisma.AlumniProfileUpdateWithoutConnectionRequestsInput, Prisma.AlumniProfileUncheckedUpdateWithoutConnectionRequestsInput>
  create: Prisma.XOR<Prisma.AlumniProfileCreateWithoutConnectionRequestsInput, Prisma.AlumniProfileUncheckedCreateWithoutConnectionRequestsInput>
  where?: Prisma.AlumniProfileWhereInput
}

export type AlumniProfileUpdateToOneWithWhereWithoutConnectionRequestsInput = {
  where?: Prisma.AlumniProfileWhereInput
  data: Prisma.XOR<Prisma.AlumniProfileUpdateWithoutConnectionRequestsInput, Prisma.AlumniProfileUncheckedUpdateWithoutConnectionRequestsInput>
}

export type AlumniProfileUpdateWithoutConnectionRequestsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bio?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profilePicture?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  graduationYear?: Prisma.IntFieldUpdateOperationsInput | number
  programType?: Prisma.StringFieldUpdateOperationsInput | string
  centerLocation?: Prisma.StringFieldUpdateOperationsInput | string
  achievements?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentPosition?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentCompany?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  industry?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  linkedInUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phoneNumber?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  mentorshipOffered?: Prisma.BoolFieldUpdateOperationsInput | boolean
  mentorshipSought?: Prisma.BoolFieldUpdateOperationsInput | boolean
  skillsOffered?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillsWanted?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profileVisibility?: Prisma.EnumProfileVisibilityFieldUpdateOperationsInput | $Enums.ProfileVisibility
  showEmail?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showPhone?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showLocation?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.UserUpdateOneRequiredWithoutAlumniProfileNestedInput
  primaryOrganization?: Prisma.OrganizationUpdateOneWithoutAlumniProfilesNestedInput
  posts?: Prisma.PostUpdateManyWithoutAuthorNestedInput
  connections?: Prisma.ConnectionUpdateManyWithoutRequesterNestedInput
  eventRegistrations?: Prisma.EventRegistrationUpdateManyWithoutAttendeeNestedInput
  donations?: Prisma.DonationUpdateManyWithoutDonorNestedInput
  messages?: Prisma.MessageUpdateManyWithoutSenderNestedInput
  receivedMessages?: Prisma.MessageUpdateManyWithoutReceiverNestedInput
  postLikes?: Prisma.PostLikeUpdateManyWithoutUserNestedInput
  comments?: Prisma.CommentUpdateManyWithoutAuthorNestedInput
  notifications?: Prisma.NotificationUpdateManyWithoutRecipientNestedInput
}

export type AlumniProfileUncheckedUpdateWithoutConnectionRequestsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bio?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profilePicture?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  primaryOrganizationId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  graduationYear?: Prisma.IntFieldUpdateOperationsInput | number
  programType?: Prisma.StringFieldUpdateOperationsInput | string
  centerLocation?: Prisma.StringFieldUpdateOperationsInput | string
  achievements?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentPosition?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentCompany?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  industry?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  linkedInUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phoneNumber?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  mentorshipOffered?: Prisma.BoolFieldUpdateOperationsInput | boolean
  mentorshipSought?: Prisma.BoolFieldUpdateOperationsInput | boolean
  skillsOffered?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillsWanted?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profileVisibility?: Prisma.EnumProfileVisibilityFieldUpdateOperationsInput | $Enums.ProfileVisibility
  showEmail?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showPhone?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showLocation?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  posts?: Prisma.PostUncheckedUpdateManyWithoutAuthorNestedInput
  connections?: Prisma.ConnectionUncheckedUpdateManyWithoutRequesterNestedInput
  eventRegistrations?: Prisma.EventRegistrationUncheckedUpdateManyWithoutAttendeeNestedInput
  donations?: Prisma.DonationUncheckedUpdateManyWithoutDonorNestedInput
  messages?: Prisma.MessageUncheckedUpdateManyWithoutSenderNestedInput
  receivedMessages?: Prisma.MessageUncheckedUpdateManyWithoutReceiverNestedInput
  postLikes?: Prisma.PostLikeUncheckedUpdateManyWithoutUserNestedInput
  comments?: Prisma.CommentUncheckedUpdateManyWithoutAuthorNestedInput
  notifications?: Prisma.NotificationUncheckedUpdateManyWithoutRecipientNestedInput
}

export type AlumniProfileCreateWithoutMessagesInput = {
  id?: string
  firstName: string
  lastName: string
  displayName?: string | null
  bio?: string | null
  profilePicture?: string | null
  graduationYear: number
  programType: string
  centerLocation: string
  achievements?: string | null
  currentPosition?: string | null
  currentCompany?: string | null
  industry?: string | null
  location?: string | null
  linkedInUrl?: string | null
  phoneNumber?: string | null
  mentorshipOffered?: boolean
  mentorshipSought?: boolean
  skillsOffered?: string | null
  skillsWanted?: string | null
  profileVisibility?: $Enums.ProfileVisibility
  showEmail?: boolean
  showPhone?: boolean
  showLocation?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  user: Prisma.UserCreateNestedOneWithoutAlumniProfileInput
  primaryOrganization?: Prisma.OrganizationCreateNestedOneWithoutAlumniProfilesInput
  posts?: Prisma.PostCreateNestedManyWithoutAuthorInput
  connections?: Prisma.ConnectionCreateNestedManyWithoutRequesterInput
  connectionRequests?: Prisma.ConnectionCreateNestedManyWithoutRequestedInput
  eventRegistrations?: Prisma.EventRegistrationCreateNestedManyWithoutAttendeeInput
  donations?: Prisma.DonationCreateNestedManyWithoutDonorInput
  receivedMessages?: Prisma.MessageCreateNestedManyWithoutReceiverInput
  postLikes?: Prisma.PostLikeCreateNestedManyWithoutUserInput
  comments?: Prisma.CommentCreateNestedManyWithoutAuthorInput
  notifications?: Prisma.NotificationCreateNestedManyWithoutRecipientInput
}

export type AlumniProfileUncheckedCreateWithoutMessagesInput = {
  id?: string
  userId: string
  firstName: string
  lastName: string
  displayName?: string | null
  bio?: string | null
  profilePicture?: string | null
  primaryOrganizationId?: string | null
  graduationYear: number
  programType: string
  centerLocation: string
  achievements?: string | null
  currentPosition?: string | null
  currentCompany?: string | null
  industry?: string | null
  location?: string | null
  linkedInUrl?: string | null
  phoneNumber?: string | null
  mentorshipOffered?: boolean
  mentorshipSought?: boolean
  skillsOffered?: string | null
  skillsWanted?: string | null
  profileVisibility?: $Enums.ProfileVisibility
  showEmail?: boolean
  showPhone?: boolean
  showLocation?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  posts?: Prisma.PostUncheckedCreateNestedManyWithoutAuthorInput
  connections?: Prisma.ConnectionUncheckedCreateNestedManyWithoutRequesterInput
  connectionRequests?: Prisma.ConnectionUncheckedCreateNestedManyWithoutRequestedInput
  eventRegistrations?: Prisma.EventRegistrationUncheckedCreateNestedManyWithoutAttendeeInput
  donations?: Prisma.DonationUncheckedCreateNestedManyWithoutDonorInput
  receivedMessages?: Prisma.MessageUncheckedCreateNestedManyWithoutReceiverInput
  postLikes?: Prisma.PostLikeUncheckedCreateNestedManyWithoutUserInput
  comments?: Prisma.CommentUncheckedCreateNestedManyWithoutAuthorInput
  notifications?: Prisma.NotificationUncheckedCreateNestedManyWithoutRecipientInput
}

export type AlumniProfileCreateOrConnectWithoutMessagesInput = {
  where: Prisma.AlumniProfileWhereUniqueInput
  create: Prisma.XOR<Prisma.AlumniProfileCreateWithoutMessagesInput, Prisma.AlumniProfileUncheckedCreateWithoutMessagesInput>
}

export type AlumniProfileCreateWithoutReceivedMessagesInput = {
  id?: string
  firstName: string
  lastName: string
  displayName?: string | null
  bio?: string | null
  profilePicture?: string | null
  graduationYear: number
  programType: string
  centerLocation: string
  achievements?: string | null
  currentPosition?: string | null
  currentCompany?: string | null
  industry?: string | null
  location?: string | null
  linkedInUrl?: string | null
  phoneNumber?: string | null
  mentorshipOffered?: boolean
  mentorshipSought?: boolean
  skillsOffered?: string | null
  skillsWanted?: string | null
  profileVisibility?: $Enums.ProfileVisibility
  showEmail?: boolean
  showPhone?: boolean
  showLocation?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  user: Prisma.UserCreateNestedOneWithoutAlumniProfileInput
  primaryOrganization?: Prisma.OrganizationCreateNestedOneWithoutAlumniProfilesInput
  posts?: Prisma.PostCreateNestedManyWithoutAuthorInput
  connections?: Prisma.ConnectionCreateNestedManyWithoutRequesterInput
  connectionRequests?: Prisma.ConnectionCreateNestedManyWithoutRequestedInput
  eventRegistrations?: Prisma.EventRegistrationCreateNestedManyWithoutAttendeeInput
  donations?: Prisma.DonationCreateNestedManyWithoutDonorInput
  messages?: Prisma.MessageCreateNestedManyWithoutSenderInput
  postLikes?: Prisma.PostLikeCreateNestedManyWithoutUserInput
  comments?: Prisma.CommentCreateNestedManyWithoutAuthorInput
  notifications?: Prisma.NotificationCreateNestedManyWithoutRecipientInput
}

export type AlumniProfileUncheckedCreateWithoutReceivedMessagesInput = {
  id?: string
  userId: string
  firstName: string
  lastName: string
  displayName?: string | null
  bio?: string | null
  profilePicture?: string | null
  primaryOrganizationId?: string | null
  graduationYear: number
  programType: string
  centerLocation: string
  achievements?: string | null
  currentPosition?: string | null
  currentCompany?: string | null
  industry?: string | null
  location?: string | null
  linkedInUrl?: string | null
  phoneNumber?: string | null
  mentorshipOffered?: boolean
  mentorshipSought?: boolean
  skillsOffered?: string | null
  skillsWanted?: string | null
  profileVisibility?: $Enums.ProfileVisibility
  showEmail?: boolean
  showPhone?: boolean
  showLocation?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  posts?: Prisma.PostUncheckedCreateNestedManyWithoutAuthorInput
  connections?: Prisma.ConnectionUncheckedCreateNestedManyWithoutRequesterInput
  connectionRequests?: Prisma.ConnectionUncheckedCreateNestedManyWithoutRequestedInput
  eventRegistrations?: Prisma.EventRegistrationUncheckedCreateNestedManyWithoutAttendeeInput
  donations?: Prisma.DonationUncheckedCreateNestedManyWithoutDonorInput
  messages?: Prisma.MessageUncheckedCreateNestedManyWithoutSenderInput
  postLikes?: Prisma.PostLikeUncheckedCreateNestedManyWithoutUserInput
  comments?: Prisma.CommentUncheckedCreateNestedManyWithoutAuthorInput
  notifications?: Prisma.NotificationUncheckedCreateNestedManyWithoutRecipientInput
}

export type AlumniProfileCreateOrConnectWithoutReceivedMessagesInput = {
  where: Prisma.AlumniProfileWhereUniqueInput
  create: Prisma.XOR<Prisma.AlumniProfileCreateWithoutReceivedMessagesInput, Prisma.AlumniProfileUncheckedCreateWithoutReceivedMessagesInput>
}

export type AlumniProfileUpsertWithoutMessagesInput = {
  update: Prisma.XOR<Prisma.AlumniProfileUpdateWithoutMessagesInput, Prisma.AlumniProfileUncheckedUpdateWithoutMessagesInput>
  create: Prisma.XOR<Prisma.AlumniProfileCreateWithoutMessagesInput, Prisma.AlumniProfileUncheckedCreateWithoutMessagesInput>
  where?: Prisma.AlumniProfileWhereInput
}

export type AlumniProfileUpdateToOneWithWhereWithoutMessagesInput = {
  where?: Prisma.AlumniProfileWhereInput
  data: Prisma.XOR<Prisma.AlumniProfileUpdateWithoutMessagesInput, Prisma.AlumniProfileUncheckedUpdateWithoutMessagesInput>
}

export type AlumniProfileUpdateWithoutMessagesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bio?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profilePicture?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  graduationYear?: Prisma.IntFieldUpdateOperationsInput | number
  programType?: Prisma.StringFieldUpdateOperationsInput | string
  centerLocation?: Prisma.StringFieldUpdateOperationsInput | string
  achievements?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentPosition?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentCompany?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  industry?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  linkedInUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phoneNumber?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  mentorshipOffered?: Prisma.BoolFieldUpdateOperationsInput | boolean
  mentorshipSought?: Prisma.BoolFieldUpdateOperationsInput | boolean
  skillsOffered?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillsWanted?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profileVisibility?: Prisma.EnumProfileVisibilityFieldUpdateOperationsInput | $Enums.ProfileVisibility
  showEmail?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showPhone?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showLocation?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.UserUpdateOneRequiredWithoutAlumniProfileNestedInput
  primaryOrganization?: Prisma.OrganizationUpdateOneWithoutAlumniProfilesNestedInput
  posts?: Prisma.PostUpdateManyWithoutAuthorNestedInput
  connections?: Prisma.ConnectionUpdateManyWithoutRequesterNestedInput
  connectionRequests?: Prisma.ConnectionUpdateManyWithoutRequestedNestedInput
  eventRegistrations?: Prisma.EventRegistrationUpdateManyWithoutAttendeeNestedInput
  donations?: Prisma.DonationUpdateManyWithoutDonorNestedInput
  receivedMessages?: Prisma.MessageUpdateManyWithoutReceiverNestedInput
  postLikes?: Prisma.PostLikeUpdateManyWithoutUserNestedInput
  comments?: Prisma.CommentUpdateManyWithoutAuthorNestedInput
  notifications?: Prisma.NotificationUpdateManyWithoutRecipientNestedInput
}

export type AlumniProfileUncheckedUpdateWithoutMessagesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bio?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profilePicture?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  primaryOrganizationId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  graduationYear?: Prisma.IntFieldUpdateOperationsInput | number
  programType?: Prisma.StringFieldUpdateOperationsInput | string
  centerLocation?: Prisma.StringFieldUpdateOperationsInput | string
  achievements?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentPosition?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentCompany?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  industry?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  linkedInUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phoneNumber?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  mentorshipOffered?: Prisma.BoolFieldUpdateOperationsInput | boolean
  mentorshipSought?: Prisma.BoolFieldUpdateOperationsInput | boolean
  skillsOffered?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillsWanted?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profileVisibility?: Prisma.EnumProfileVisibilityFieldUpdateOperationsInput | $Enums.ProfileVisibility
  showEmail?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showPhone?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showLocation?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  posts?: Prisma.PostUncheckedUpdateManyWithoutAuthorNestedInput
  connections?: Prisma.ConnectionUncheckedUpdateManyWithoutRequesterNestedInput
  connectionRequests?: Prisma.ConnectionUncheckedUpdateManyWithoutRequestedNestedInput
  eventRegistrations?: Prisma.EventRegistrationUncheckedUpdateManyWithoutAttendeeNestedInput
  donations?: Prisma.DonationUncheckedUpdateManyWithoutDonorNestedInput
  receivedMessages?: Prisma.MessageUncheckedUpdateManyWithoutReceiverNestedInput
  postLikes?: Prisma.PostLikeUncheckedUpdateManyWithoutUserNestedInput
  comments?: Prisma.CommentUncheckedUpdateManyWithoutAuthorNestedInput
  notifications?: Prisma.NotificationUncheckedUpdateManyWithoutRecipientNestedInput
}

export type AlumniProfileUpsertWithoutReceivedMessagesInput = {
  update: Prisma.XOR<Prisma.AlumniProfileUpdateWithoutReceivedMessagesInput, Prisma.AlumniProfileUncheckedUpdateWithoutReceivedMessagesInput>
  create: Prisma.XOR<Prisma.AlumniProfileCreateWithoutReceivedMessagesInput, Prisma.AlumniProfileUncheckedCreateWithoutReceivedMessagesInput>
  where?: Prisma.AlumniProfileWhereInput
}

export type AlumniProfileUpdateToOneWithWhereWithoutReceivedMessagesInput = {
  where?: Prisma.AlumniProfileWhereInput
  data: Prisma.XOR<Prisma.AlumniProfileUpdateWithoutReceivedMessagesInput, Prisma.AlumniProfileUncheckedUpdateWithoutReceivedMessagesInput>
}

export type AlumniProfileUpdateWithoutReceivedMessagesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bio?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profilePicture?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  graduationYear?: Prisma.IntFieldUpdateOperationsInput | number
  programType?: Prisma.StringFieldUpdateOperationsInput | string
  centerLocation?: Prisma.StringFieldUpdateOperationsInput | string
  achievements?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentPosition?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentCompany?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  industry?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  linkedInUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phoneNumber?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  mentorshipOffered?: Prisma.BoolFieldUpdateOperationsInput | boolean
  mentorshipSought?: Prisma.BoolFieldUpdateOperationsInput | boolean
  skillsOffered?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillsWanted?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profileVisibility?: Prisma.EnumProfileVisibilityFieldUpdateOperationsInput | $Enums.ProfileVisibility
  showEmail?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showPhone?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showLocation?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.UserUpdateOneRequiredWithoutAlumniProfileNestedInput
  primaryOrganization?: Prisma.OrganizationUpdateOneWithoutAlumniProfilesNestedInput
  posts?: Prisma.PostUpdateManyWithoutAuthorNestedInput
  connections?: Prisma.ConnectionUpdateManyWithoutRequesterNestedInput
  connectionRequests?: Prisma.ConnectionUpdateManyWithoutRequestedNestedInput
  eventRegistrations?: Prisma.EventRegistrationUpdateManyWithoutAttendeeNestedInput
  donations?: Prisma.DonationUpdateManyWithoutDonorNestedInput
  messages?: Prisma.MessageUpdateManyWithoutSenderNestedInput
  postLikes?: Prisma.PostLikeUpdateManyWithoutUserNestedInput
  comments?: Prisma.CommentUpdateManyWithoutAuthorNestedInput
  notifications?: Prisma.NotificationUpdateManyWithoutRecipientNestedInput
}

export type AlumniProfileUncheckedUpdateWithoutReceivedMessagesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bio?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profilePicture?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  primaryOrganizationId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  graduationYear?: Prisma.IntFieldUpdateOperationsInput | number
  programType?: Prisma.StringFieldUpdateOperationsInput | string
  centerLocation?: Prisma.StringFieldUpdateOperationsInput | string
  achievements?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentPosition?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentCompany?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  industry?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  linkedInUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phoneNumber?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  mentorshipOffered?: Prisma.BoolFieldUpdateOperationsInput | boolean
  mentorshipSought?: Prisma.BoolFieldUpdateOperationsInput | boolean
  skillsOffered?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillsWanted?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profileVisibility?: Prisma.EnumProfileVisibilityFieldUpdateOperationsInput | $Enums.ProfileVisibility
  showEmail?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showPhone?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showLocation?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  posts?: Prisma.PostUncheckedUpdateManyWithoutAuthorNestedInput
  connections?: Prisma.ConnectionUncheckedUpdateManyWithoutRequesterNestedInput
  connectionRequests?: Prisma.ConnectionUncheckedUpdateManyWithoutRequestedNestedInput
  eventRegistrations?: Prisma.EventRegistrationUncheckedUpdateManyWithoutAttendeeNestedInput
  donations?: Prisma.DonationUncheckedUpdateManyWithoutDonorNestedInput
  messages?: Prisma.MessageUncheckedUpdateManyWithoutSenderNestedInput
  postLikes?: Prisma.PostLikeUncheckedUpdateManyWithoutUserNestedInput
  comments?: Prisma.CommentUncheckedUpdateManyWithoutAuthorNestedInput
  notifications?: Prisma.NotificationUncheckedUpdateManyWithoutRecipientNestedInput
}

export type AlumniProfileCreateWithoutPostsInput = {
  id?: string
  firstName: string
  lastName: string
  displayName?: string | null
  bio?: string | null
  profilePicture?: string | null
  graduationYear: number
  programType: string
  centerLocation: string
  achievements?: string | null
  currentPosition?: string | null
  currentCompany?: string | null
  industry?: string | null
  location?: string | null
  linkedInUrl?: string | null
  phoneNumber?: string | null
  mentorshipOffered?: boolean
  mentorshipSought?: boolean
  skillsOffered?: string | null
  skillsWanted?: string | null
  profileVisibility?: $Enums.ProfileVisibility
  showEmail?: boolean
  showPhone?: boolean
  showLocation?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  user: Prisma.UserCreateNestedOneWithoutAlumniProfileInput
  primaryOrganization?: Prisma.OrganizationCreateNestedOneWithoutAlumniProfilesInput
  connections?: Prisma.ConnectionCreateNestedManyWithoutRequesterInput
  connectionRequests?: Prisma.ConnectionCreateNestedManyWithoutRequestedInput
  eventRegistrations?: Prisma.EventRegistrationCreateNestedManyWithoutAttendeeInput
  donations?: Prisma.DonationCreateNestedManyWithoutDonorInput
  messages?: Prisma.MessageCreateNestedManyWithoutSenderInput
  receivedMessages?: Prisma.MessageCreateNestedManyWithoutReceiverInput
  postLikes?: Prisma.PostLikeCreateNestedManyWithoutUserInput
  comments?: Prisma.CommentCreateNestedManyWithoutAuthorInput
  notifications?: Prisma.NotificationCreateNestedManyWithoutRecipientInput
}

export type AlumniProfileUncheckedCreateWithoutPostsInput = {
  id?: string
  userId: string
  firstName: string
  lastName: string
  displayName?: string | null
  bio?: string | null
  profilePicture?: string | null
  primaryOrganizationId?: string | null
  graduationYear: number
  programType: string
  centerLocation: string
  achievements?: string | null
  currentPosition?: string | null
  currentCompany?: string | null
  industry?: string | null
  location?: string | null
  linkedInUrl?: string | null
  phoneNumber?: string | null
  mentorshipOffered?: boolean
  mentorshipSought?: boolean
  skillsOffered?: string | null
  skillsWanted?: string | null
  profileVisibility?: $Enums.ProfileVisibility
  showEmail?: boolean
  showPhone?: boolean
  showLocation?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  connections?: Prisma.ConnectionUncheckedCreateNestedManyWithoutRequesterInput
  connectionRequests?: Prisma.ConnectionUncheckedCreateNestedManyWithoutRequestedInput
  eventRegistrations?: Prisma.EventRegistrationUncheckedCreateNestedManyWithoutAttendeeInput
  donations?: Prisma.DonationUncheckedCreateNestedManyWithoutDonorInput
  messages?: Prisma.MessageUncheckedCreateNestedManyWithoutSenderInput
  receivedMessages?: Prisma.MessageUncheckedCreateNestedManyWithoutReceiverInput
  postLikes?: Prisma.PostLikeUncheckedCreateNestedManyWithoutUserInput
  comments?: Prisma.CommentUncheckedCreateNestedManyWithoutAuthorInput
  notifications?: Prisma.NotificationUncheckedCreateNestedManyWithoutRecipientInput
}

export type AlumniProfileCreateOrConnectWithoutPostsInput = {
  where: Prisma.AlumniProfileWhereUniqueInput
  create: Prisma.XOR<Prisma.AlumniProfileCreateWithoutPostsInput, Prisma.AlumniProfileUncheckedCreateWithoutPostsInput>
}

export type AlumniProfileUpsertWithoutPostsInput = {
  update: Prisma.XOR<Prisma.AlumniProfileUpdateWithoutPostsInput, Prisma.AlumniProfileUncheckedUpdateWithoutPostsInput>
  create: Prisma.XOR<Prisma.AlumniProfileCreateWithoutPostsInput, Prisma.AlumniProfileUncheckedCreateWithoutPostsInput>
  where?: Prisma.AlumniProfileWhereInput
}

export type AlumniProfileUpdateToOneWithWhereWithoutPostsInput = {
  where?: Prisma.AlumniProfileWhereInput
  data: Prisma.XOR<Prisma.AlumniProfileUpdateWithoutPostsInput, Prisma.AlumniProfileUncheckedUpdateWithoutPostsInput>
}

export type AlumniProfileUpdateWithoutPostsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bio?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profilePicture?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  graduationYear?: Prisma.IntFieldUpdateOperationsInput | number
  programType?: Prisma.StringFieldUpdateOperationsInput | string
  centerLocation?: Prisma.StringFieldUpdateOperationsInput | string
  achievements?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentPosition?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentCompany?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  industry?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  linkedInUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phoneNumber?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  mentorshipOffered?: Prisma.BoolFieldUpdateOperationsInput | boolean
  mentorshipSought?: Prisma.BoolFieldUpdateOperationsInput | boolean
  skillsOffered?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillsWanted?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profileVisibility?: Prisma.EnumProfileVisibilityFieldUpdateOperationsInput | $Enums.ProfileVisibility
  showEmail?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showPhone?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showLocation?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.UserUpdateOneRequiredWithoutAlumniProfileNestedInput
  primaryOrganization?: Prisma.OrganizationUpdateOneWithoutAlumniProfilesNestedInput
  connections?: Prisma.ConnectionUpdateManyWithoutRequesterNestedInput
  connectionRequests?: Prisma.ConnectionUpdateManyWithoutRequestedNestedInput
  eventRegistrations?: Prisma.EventRegistrationUpdateManyWithoutAttendeeNestedInput
  donations?: Prisma.DonationUpdateManyWithoutDonorNestedInput
  messages?: Prisma.MessageUpdateManyWithoutSenderNestedInput
  receivedMessages?: Prisma.MessageUpdateManyWithoutReceiverNestedInput
  postLikes?: Prisma.PostLikeUpdateManyWithoutUserNestedInput
  comments?: Prisma.CommentUpdateManyWithoutAuthorNestedInput
  notifications?: Prisma.NotificationUpdateManyWithoutRecipientNestedInput
}

export type AlumniProfileUncheckedUpdateWithoutPostsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bio?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profilePicture?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  primaryOrganizationId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  graduationYear?: Prisma.IntFieldUpdateOperationsInput | number
  programType?: Prisma.StringFieldUpdateOperationsInput | string
  centerLocation?: Prisma.StringFieldUpdateOperationsInput | string
  achievements?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentPosition?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentCompany?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  industry?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  linkedInUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phoneNumber?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  mentorshipOffered?: Prisma.BoolFieldUpdateOperationsInput | boolean
  mentorshipSought?: Prisma.BoolFieldUpdateOperationsInput | boolean
  skillsOffered?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillsWanted?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profileVisibility?: Prisma.EnumProfileVisibilityFieldUpdateOperationsInput | $Enums.ProfileVisibility
  showEmail?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showPhone?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showLocation?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  connections?: Prisma.ConnectionUncheckedUpdateManyWithoutRequesterNestedInput
  connectionRequests?: Prisma.ConnectionUncheckedUpdateManyWithoutRequestedNestedInput
  eventRegistrations?: Prisma.EventRegistrationUncheckedUpdateManyWithoutAttendeeNestedInput
  donations?: Prisma.DonationUncheckedUpdateManyWithoutDonorNestedInput
  messages?: Prisma.MessageUncheckedUpdateManyWithoutSenderNestedInput
  receivedMessages?: Prisma.MessageUncheckedUpdateManyWithoutReceiverNestedInput
  postLikes?: Prisma.PostLikeUncheckedUpdateManyWithoutUserNestedInput
  comments?: Prisma.CommentUncheckedUpdateManyWithoutAuthorNestedInput
  notifications?: Prisma.NotificationUncheckedUpdateManyWithoutRecipientNestedInput
}

export type AlumniProfileCreateWithoutPostLikesInput = {
  id?: string
  firstName: string
  lastName: string
  displayName?: string | null
  bio?: string | null
  profilePicture?: string | null
  graduationYear: number
  programType: string
  centerLocation: string
  achievements?: string | null
  currentPosition?: string | null
  currentCompany?: string | null
  industry?: string | null
  location?: string | null
  linkedInUrl?: string | null
  phoneNumber?: string | null
  mentorshipOffered?: boolean
  mentorshipSought?: boolean
  skillsOffered?: string | null
  skillsWanted?: string | null
  profileVisibility?: $Enums.ProfileVisibility
  showEmail?: boolean
  showPhone?: boolean
  showLocation?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  user: Prisma.UserCreateNestedOneWithoutAlumniProfileInput
  primaryOrganization?: Prisma.OrganizationCreateNestedOneWithoutAlumniProfilesInput
  posts?: Prisma.PostCreateNestedManyWithoutAuthorInput
  connections?: Prisma.ConnectionCreateNestedManyWithoutRequesterInput
  connectionRequests?: Prisma.ConnectionCreateNestedManyWithoutRequestedInput
  eventRegistrations?: Prisma.EventRegistrationCreateNestedManyWithoutAttendeeInput
  donations?: Prisma.DonationCreateNestedManyWithoutDonorInput
  messages?: Prisma.MessageCreateNestedManyWithoutSenderInput
  receivedMessages?: Prisma.MessageCreateNestedManyWithoutReceiverInput
  comments?: Prisma.CommentCreateNestedManyWithoutAuthorInput
  notifications?: Prisma.NotificationCreateNestedManyWithoutRecipientInput
}

export type AlumniProfileUncheckedCreateWithoutPostLikesInput = {
  id?: string
  userId: string
  firstName: string
  lastName: string
  displayName?: string | null
  bio?: string | null
  profilePicture?: string | null
  primaryOrganizationId?: string | null
  graduationYear: number
  programType: string
  centerLocation: string
  achievements?: string | null
  currentPosition?: string | null
  currentCompany?: string | null
  industry?: string | null
  location?: string | null
  linkedInUrl?: string | null
  phoneNumber?: string | null
  mentorshipOffered?: boolean
  mentorshipSought?: boolean
  skillsOffered?: string | null
  skillsWanted?: string | null
  profileVisibility?: $Enums.ProfileVisibility
  showEmail?: boolean
  showPhone?: boolean
  showLocation?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  posts?: Prisma.PostUncheckedCreateNestedManyWithoutAuthorInput
  connections?: Prisma.ConnectionUncheckedCreateNestedManyWithoutRequesterInput
  connectionRequests?: Prisma.ConnectionUncheckedCreateNestedManyWithoutRequestedInput
  eventRegistrations?: Prisma.EventRegistrationUncheckedCreateNestedManyWithoutAttendeeInput
  donations?: Prisma.DonationUncheckedCreateNestedManyWithoutDonorInput
  messages?: Prisma.MessageUncheckedCreateNestedManyWithoutSenderInput
  receivedMessages?: Prisma.MessageUncheckedCreateNestedManyWithoutReceiverInput
  comments?: Prisma.CommentUncheckedCreateNestedManyWithoutAuthorInput
  notifications?: Prisma.NotificationUncheckedCreateNestedManyWithoutRecipientInput
}

export type AlumniProfileCreateOrConnectWithoutPostLikesInput = {
  where: Prisma.AlumniProfileWhereUniqueInput
  create: Prisma.XOR<Prisma.AlumniProfileCreateWithoutPostLikesInput, Prisma.AlumniProfileUncheckedCreateWithoutPostLikesInput>
}

export type AlumniProfileUpsertWithoutPostLikesInput = {
  update: Prisma.XOR<Prisma.AlumniProfileUpdateWithoutPostLikesInput, Prisma.AlumniProfileUncheckedUpdateWithoutPostLikesInput>
  create: Prisma.XOR<Prisma.AlumniProfileCreateWithoutPostLikesInput, Prisma.AlumniProfileUncheckedCreateWithoutPostLikesInput>
  where?: Prisma.AlumniProfileWhereInput
}

export type AlumniProfileUpdateToOneWithWhereWithoutPostLikesInput = {
  where?: Prisma.AlumniProfileWhereInput
  data: Prisma.XOR<Prisma.AlumniProfileUpdateWithoutPostLikesInput, Prisma.AlumniProfileUncheckedUpdateWithoutPostLikesInput>
}

export type AlumniProfileUpdateWithoutPostLikesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bio?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profilePicture?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  graduationYear?: Prisma.IntFieldUpdateOperationsInput | number
  programType?: Prisma.StringFieldUpdateOperationsInput | string
  centerLocation?: Prisma.StringFieldUpdateOperationsInput | string
  achievements?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentPosition?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentCompany?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  industry?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  linkedInUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phoneNumber?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  mentorshipOffered?: Prisma.BoolFieldUpdateOperationsInput | boolean
  mentorshipSought?: Prisma.BoolFieldUpdateOperationsInput | boolean
  skillsOffered?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillsWanted?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profileVisibility?: Prisma.EnumProfileVisibilityFieldUpdateOperationsInput | $Enums.ProfileVisibility
  showEmail?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showPhone?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showLocation?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.UserUpdateOneRequiredWithoutAlumniProfileNestedInput
  primaryOrganization?: Prisma.OrganizationUpdateOneWithoutAlumniProfilesNestedInput
  posts?: Prisma.PostUpdateManyWithoutAuthorNestedInput
  connections?: Prisma.ConnectionUpdateManyWithoutRequesterNestedInput
  connectionRequests?: Prisma.ConnectionUpdateManyWithoutRequestedNestedInput
  eventRegistrations?: Prisma.EventRegistrationUpdateManyWithoutAttendeeNestedInput
  donations?: Prisma.DonationUpdateManyWithoutDonorNestedInput
  messages?: Prisma.MessageUpdateManyWithoutSenderNestedInput
  receivedMessages?: Prisma.MessageUpdateManyWithoutReceiverNestedInput
  comments?: Prisma.CommentUpdateManyWithoutAuthorNestedInput
  notifications?: Prisma.NotificationUpdateManyWithoutRecipientNestedInput
}

export type AlumniProfileUncheckedUpdateWithoutPostLikesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bio?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profilePicture?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  primaryOrganizationId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  graduationYear?: Prisma.IntFieldUpdateOperationsInput | number
  programType?: Prisma.StringFieldUpdateOperationsInput | string
  centerLocation?: Prisma.StringFieldUpdateOperationsInput | string
  achievements?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentPosition?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentCompany?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  industry?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  linkedInUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phoneNumber?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  mentorshipOffered?: Prisma.BoolFieldUpdateOperationsInput | boolean
  mentorshipSought?: Prisma.BoolFieldUpdateOperationsInput | boolean
  skillsOffered?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillsWanted?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profileVisibility?: Prisma.EnumProfileVisibilityFieldUpdateOperationsInput | $Enums.ProfileVisibility
  showEmail?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showPhone?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showLocation?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  posts?: Prisma.PostUncheckedUpdateManyWithoutAuthorNestedInput
  connections?: Prisma.ConnectionUncheckedUpdateManyWithoutRequesterNestedInput
  connectionRequests?: Prisma.ConnectionUncheckedUpdateManyWithoutRequestedNestedInput
  eventRegistrations?: Prisma.EventRegistrationUncheckedUpdateManyWithoutAttendeeNestedInput
  donations?: Prisma.DonationUncheckedUpdateManyWithoutDonorNestedInput
  messages?: Prisma.MessageUncheckedUpdateManyWithoutSenderNestedInput
  receivedMessages?: Prisma.MessageUncheckedUpdateManyWithoutReceiverNestedInput
  comments?: Prisma.CommentUncheckedUpdateManyWithoutAuthorNestedInput
  notifications?: Prisma.NotificationUncheckedUpdateManyWithoutRecipientNestedInput
}

export type AlumniProfileCreateWithoutCommentsInput = {
  id?: string
  firstName: string
  lastName: string
  displayName?: string | null
  bio?: string | null
  profilePicture?: string | null
  graduationYear: number
  programType: string
  centerLocation: string
  achievements?: string | null
  currentPosition?: string | null
  currentCompany?: string | null
  industry?: string | null
  location?: string | null
  linkedInUrl?: string | null
  phoneNumber?: string | null
  mentorshipOffered?: boolean
  mentorshipSought?: boolean
  skillsOffered?: string | null
  skillsWanted?: string | null
  profileVisibility?: $Enums.ProfileVisibility
  showEmail?: boolean
  showPhone?: boolean
  showLocation?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  user: Prisma.UserCreateNestedOneWithoutAlumniProfileInput
  primaryOrganization?: Prisma.OrganizationCreateNestedOneWithoutAlumniProfilesInput
  posts?: Prisma.PostCreateNestedManyWithoutAuthorInput
  connections?: Prisma.ConnectionCreateNestedManyWithoutRequesterInput
  connectionRequests?: Prisma.ConnectionCreateNestedManyWithoutRequestedInput
  eventRegistrations?: Prisma.EventRegistrationCreateNestedManyWithoutAttendeeInput
  donations?: Prisma.DonationCreateNestedManyWithoutDonorInput
  messages?: Prisma.MessageCreateNestedManyWithoutSenderInput
  receivedMessages?: Prisma.MessageCreateNestedManyWithoutReceiverInput
  postLikes?: Prisma.PostLikeCreateNestedManyWithoutUserInput
  notifications?: Prisma.NotificationCreateNestedManyWithoutRecipientInput
}

export type AlumniProfileUncheckedCreateWithoutCommentsInput = {
  id?: string
  userId: string
  firstName: string
  lastName: string
  displayName?: string | null
  bio?: string | null
  profilePicture?: string | null
  primaryOrganizationId?: string | null
  graduationYear: number
  programType: string
  centerLocation: string
  achievements?: string | null
  currentPosition?: string | null
  currentCompany?: string | null
  industry?: string | null
  location?: string | null
  linkedInUrl?: string | null
  phoneNumber?: string | null
  mentorshipOffered?: boolean
  mentorshipSought?: boolean
  skillsOffered?: string | null
  skillsWanted?: string | null
  profileVisibility?: $Enums.ProfileVisibility
  showEmail?: boolean
  showPhone?: boolean
  showLocation?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  posts?: Prisma.PostUncheckedCreateNestedManyWithoutAuthorInput
  connections?: Prisma.ConnectionUncheckedCreateNestedManyWithoutRequesterInput
  connectionRequests?: Prisma.ConnectionUncheckedCreateNestedManyWithoutRequestedInput
  eventRegistrations?: Prisma.EventRegistrationUncheckedCreateNestedManyWithoutAttendeeInput
  donations?: Prisma.DonationUncheckedCreateNestedManyWithoutDonorInput
  messages?: Prisma.MessageUncheckedCreateNestedManyWithoutSenderInput
  receivedMessages?: Prisma.MessageUncheckedCreateNestedManyWithoutReceiverInput
  postLikes?: Prisma.PostLikeUncheckedCreateNestedManyWithoutUserInput
  notifications?: Prisma.NotificationUncheckedCreateNestedManyWithoutRecipientInput
}

export type AlumniProfileCreateOrConnectWithoutCommentsInput = {
  where: Prisma.AlumniProfileWhereUniqueInput
  create: Prisma.XOR<Prisma.AlumniProfileCreateWithoutCommentsInput, Prisma.AlumniProfileUncheckedCreateWithoutCommentsInput>
}

export type AlumniProfileUpsertWithoutCommentsInput = {
  update: Prisma.XOR<Prisma.AlumniProfileUpdateWithoutCommentsInput, Prisma.AlumniProfileUncheckedUpdateWithoutCommentsInput>
  create: Prisma.XOR<Prisma.AlumniProfileCreateWithoutCommentsInput, Prisma.AlumniProfileUncheckedCreateWithoutCommentsInput>
  where?: Prisma.AlumniProfileWhereInput
}

export type AlumniProfileUpdateToOneWithWhereWithoutCommentsInput = {
  where?: Prisma.AlumniProfileWhereInput
  data: Prisma.XOR<Prisma.AlumniProfileUpdateWithoutCommentsInput, Prisma.AlumniProfileUncheckedUpdateWithoutCommentsInput>
}

export type AlumniProfileUpdateWithoutCommentsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bio?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profilePicture?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  graduationYear?: Prisma.IntFieldUpdateOperationsInput | number
  programType?: Prisma.StringFieldUpdateOperationsInput | string
  centerLocation?: Prisma.StringFieldUpdateOperationsInput | string
  achievements?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentPosition?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentCompany?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  industry?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  linkedInUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phoneNumber?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  mentorshipOffered?: Prisma.BoolFieldUpdateOperationsInput | boolean
  mentorshipSought?: Prisma.BoolFieldUpdateOperationsInput | boolean
  skillsOffered?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillsWanted?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profileVisibility?: Prisma.EnumProfileVisibilityFieldUpdateOperationsInput | $Enums.ProfileVisibility
  showEmail?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showPhone?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showLocation?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.UserUpdateOneRequiredWithoutAlumniProfileNestedInput
  primaryOrganization?: Prisma.OrganizationUpdateOneWithoutAlumniProfilesNestedInput
  posts?: Prisma.PostUpdateManyWithoutAuthorNestedInput
  connections?: Prisma.ConnectionUpdateManyWithoutRequesterNestedInput
  connectionRequests?: Prisma.ConnectionUpdateManyWithoutRequestedNestedInput
  eventRegistrations?: Prisma.EventRegistrationUpdateManyWithoutAttendeeNestedInput
  donations?: Prisma.DonationUpdateManyWithoutDonorNestedInput
  messages?: Prisma.MessageUpdateManyWithoutSenderNestedInput
  receivedMessages?: Prisma.MessageUpdateManyWithoutReceiverNestedInput
  postLikes?: Prisma.PostLikeUpdateManyWithoutUserNestedInput
  notifications?: Prisma.NotificationUpdateManyWithoutRecipientNestedInput
}

export type AlumniProfileUncheckedUpdateWithoutCommentsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bio?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profilePicture?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  primaryOrganizationId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  graduationYear?: Prisma.IntFieldUpdateOperationsInput | number
  programType?: Prisma.StringFieldUpdateOperationsInput | string
  centerLocation?: Prisma.StringFieldUpdateOperationsInput | string
  achievements?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentPosition?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentCompany?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  industry?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  linkedInUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phoneNumber?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  mentorshipOffered?: Prisma.BoolFieldUpdateOperationsInput | boolean
  mentorshipSought?: Prisma.BoolFieldUpdateOperationsInput | boolean
  skillsOffered?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillsWanted?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profileVisibility?: Prisma.EnumProfileVisibilityFieldUpdateOperationsInput | $Enums.ProfileVisibility
  showEmail?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showPhone?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showLocation?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  posts?: Prisma.PostUncheckedUpdateManyWithoutAuthorNestedInput
  connections?: Prisma.ConnectionUncheckedUpdateManyWithoutRequesterNestedInput
  connectionRequests?: Prisma.ConnectionUncheckedUpdateManyWithoutRequestedNestedInput
  eventRegistrations?: Prisma.EventRegistrationUncheckedUpdateManyWithoutAttendeeNestedInput
  donations?: Prisma.DonationUncheckedUpdateManyWithoutDonorNestedInput
  messages?: Prisma.MessageUncheckedUpdateManyWithoutSenderNestedInput
  receivedMessages?: Prisma.MessageUncheckedUpdateManyWithoutReceiverNestedInput
  postLikes?: Prisma.PostLikeUncheckedUpdateManyWithoutUserNestedInput
  notifications?: Prisma.NotificationUncheckedUpdateManyWithoutRecipientNestedInput
}

export type AlumniProfileCreateWithoutEventRegistrationsInput = {
  id?: string
  firstName: string
  lastName: string
  displayName?: string | null
  bio?: string | null
  profilePicture?: string | null
  graduationYear: number
  programType: string
  centerLocation: string
  achievements?: string | null
  currentPosition?: string | null
  currentCompany?: string | null
  industry?: string | null
  location?: string | null
  linkedInUrl?: string | null
  phoneNumber?: string | null
  mentorshipOffered?: boolean
  mentorshipSought?: boolean
  skillsOffered?: string | null
  skillsWanted?: string | null
  profileVisibility?: $Enums.ProfileVisibility
  showEmail?: boolean
  showPhone?: boolean
  showLocation?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  user: Prisma.UserCreateNestedOneWithoutAlumniProfileInput
  primaryOrganization?: Prisma.OrganizationCreateNestedOneWithoutAlumniProfilesInput
  posts?: Prisma.PostCreateNestedManyWithoutAuthorInput
  connections?: Prisma.ConnectionCreateNestedManyWithoutRequesterInput
  connectionRequests?: Prisma.ConnectionCreateNestedManyWithoutRequestedInput
  donations?: Prisma.DonationCreateNestedManyWithoutDonorInput
  messages?: Prisma.MessageCreateNestedManyWithoutSenderInput
  receivedMessages?: Prisma.MessageCreateNestedManyWithoutReceiverInput
  postLikes?: Prisma.PostLikeCreateNestedManyWithoutUserInput
  comments?: Prisma.CommentCreateNestedManyWithoutAuthorInput
  notifications?: Prisma.NotificationCreateNestedManyWithoutRecipientInput
}

export type AlumniProfileUncheckedCreateWithoutEventRegistrationsInput = {
  id?: string
  userId: string
  firstName: string
  lastName: string
  displayName?: string | null
  bio?: string | null
  profilePicture?: string | null
  primaryOrganizationId?: string | null
  graduationYear: number
  programType: string
  centerLocation: string
  achievements?: string | null
  currentPosition?: string | null
  currentCompany?: string | null
  industry?: string | null
  location?: string | null
  linkedInUrl?: string | null
  phoneNumber?: string | null
  mentorshipOffered?: boolean
  mentorshipSought?: boolean
  skillsOffered?: string | null
  skillsWanted?: string | null
  profileVisibility?: $Enums.ProfileVisibility
  showEmail?: boolean
  showPhone?: boolean
  showLocation?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  posts?: Prisma.PostUncheckedCreateNestedManyWithoutAuthorInput
  connections?: Prisma.ConnectionUncheckedCreateNestedManyWithoutRequesterInput
  connectionRequests?: Prisma.ConnectionUncheckedCreateNestedManyWithoutRequestedInput
  donations?: Prisma.DonationUncheckedCreateNestedManyWithoutDonorInput
  messages?: Prisma.MessageUncheckedCreateNestedManyWithoutSenderInput
  receivedMessages?: Prisma.MessageUncheckedCreateNestedManyWithoutReceiverInput
  postLikes?: Prisma.PostLikeUncheckedCreateNestedManyWithoutUserInput
  comments?: Prisma.CommentUncheckedCreateNestedManyWithoutAuthorInput
  notifications?: Prisma.NotificationUncheckedCreateNestedManyWithoutRecipientInput
}

export type AlumniProfileCreateOrConnectWithoutEventRegistrationsInput = {
  where: Prisma.AlumniProfileWhereUniqueInput
  create: Prisma.XOR<Prisma.AlumniProfileCreateWithoutEventRegistrationsInput, Prisma.AlumniProfileUncheckedCreateWithoutEventRegistrationsInput>
}

export type AlumniProfileUpsertWithoutEventRegistrationsInput = {
  update: Prisma.XOR<Prisma.AlumniProfileUpdateWithoutEventRegistrationsInput, Prisma.AlumniProfileUncheckedUpdateWithoutEventRegistrationsInput>
  create: Prisma.XOR<Prisma.AlumniProfileCreateWithoutEventRegistrationsInput, Prisma.AlumniProfileUncheckedCreateWithoutEventRegistrationsInput>
  where?: Prisma.AlumniProfileWhereInput
}

export type AlumniProfileUpdateToOneWithWhereWithoutEventRegistrationsInput = {
  where?: Prisma.AlumniProfileWhereInput
  data: Prisma.XOR<Prisma.AlumniProfileUpdateWithoutEventRegistrationsInput, Prisma.AlumniProfileUncheckedUpdateWithoutEventRegistrationsInput>
}

export type AlumniProfileUpdateWithoutEventRegistrationsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bio?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profilePicture?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  graduationYear?: Prisma.IntFieldUpdateOperationsInput | number
  programType?: Prisma.StringFieldUpdateOperationsInput | string
  centerLocation?: Prisma.StringFieldUpdateOperationsInput | string
  achievements?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentPosition?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentCompany?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  industry?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  linkedInUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phoneNumber?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  mentorshipOffered?: Prisma.BoolFieldUpdateOperationsInput | boolean
  mentorshipSought?: Prisma.BoolFieldUpdateOperationsInput | boolean
  skillsOffered?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillsWanted?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profileVisibility?: Prisma.EnumProfileVisibilityFieldUpdateOperationsInput | $Enums.ProfileVisibility
  showEmail?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showPhone?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showLocation?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.UserUpdateOneRequiredWithoutAlumniProfileNestedInput
  primaryOrganization?: Prisma.OrganizationUpdateOneWithoutAlumniProfilesNestedInput
  posts?: Prisma.PostUpdateManyWithoutAuthorNestedInput
  connections?: Prisma.ConnectionUpdateManyWithoutRequesterNestedInput
  connectionRequests?: Prisma.ConnectionUpdateManyWithoutRequestedNestedInput
  donations?: Prisma.DonationUpdateManyWithoutDonorNestedInput
  messages?: Prisma.MessageUpdateManyWithoutSenderNestedInput
  receivedMessages?: Prisma.MessageUpdateManyWithoutReceiverNestedInput
  postLikes?: Prisma.PostLikeUpdateManyWithoutUserNestedInput
  comments?: Prisma.CommentUpdateManyWithoutAuthorNestedInput
  notifications?: Prisma.NotificationUpdateManyWithoutRecipientNestedInput
}

export type AlumniProfileUncheckedUpdateWithoutEventRegistrationsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bio?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profilePicture?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  primaryOrganizationId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  graduationYear?: Prisma.IntFieldUpdateOperationsInput | number
  programType?: Prisma.StringFieldUpdateOperationsInput | string
  centerLocation?: Prisma.StringFieldUpdateOperationsInput | string
  achievements?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentPosition?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentCompany?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  industry?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  linkedInUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phoneNumber?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  mentorshipOffered?: Prisma.BoolFieldUpdateOperationsInput | boolean
  mentorshipSought?: Prisma.BoolFieldUpdateOperationsInput | boolean
  skillsOffered?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillsWanted?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profileVisibility?: Prisma.EnumProfileVisibilityFieldUpdateOperationsInput | $Enums.ProfileVisibility
  showEmail?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showPhone?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showLocation?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  posts?: Prisma.PostUncheckedUpdateManyWithoutAuthorNestedInput
  connections?: Prisma.ConnectionUncheckedUpdateManyWithoutRequesterNestedInput
  connectionRequests?: Prisma.ConnectionUncheckedUpdateManyWithoutRequestedNestedInput
  donations?: Prisma.DonationUncheckedUpdateManyWithoutDonorNestedInput
  messages?: Prisma.MessageUncheckedUpdateManyWithoutSenderNestedInput
  receivedMessages?: Prisma.MessageUncheckedUpdateManyWithoutReceiverNestedInput
  postLikes?: Prisma.PostLikeUncheckedUpdateManyWithoutUserNestedInput
  comments?: Prisma.CommentUncheckedUpdateManyWithoutAuthorNestedInput
  notifications?: Prisma.NotificationUncheckedUpdateManyWithoutRecipientNestedInput
}

export type AlumniProfileCreateWithoutDonationsInput = {
  id?: string
  firstName: string
  lastName: string
  displayName?: string | null
  bio?: string | null
  profilePicture?: string | null
  graduationYear: number
  programType: string
  centerLocation: string
  achievements?: string | null
  currentPosition?: string | null
  currentCompany?: string | null
  industry?: string | null
  location?: string | null
  linkedInUrl?: string | null
  phoneNumber?: string | null
  mentorshipOffered?: boolean
  mentorshipSought?: boolean
  skillsOffered?: string | null
  skillsWanted?: string | null
  profileVisibility?: $Enums.ProfileVisibility
  showEmail?: boolean
  showPhone?: boolean
  showLocation?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  user: Prisma.UserCreateNestedOneWithoutAlumniProfileInput
  primaryOrganization?: Prisma.OrganizationCreateNestedOneWithoutAlumniProfilesInput
  posts?: Prisma.PostCreateNestedManyWithoutAuthorInput
  connections?: Prisma.ConnectionCreateNestedManyWithoutRequesterInput
  connectionRequests?: Prisma.ConnectionCreateNestedManyWithoutRequestedInput
  eventRegistrations?: Prisma.EventRegistrationCreateNestedManyWithoutAttendeeInput
  messages?: Prisma.MessageCreateNestedManyWithoutSenderInput
  receivedMessages?: Prisma.MessageCreateNestedManyWithoutReceiverInput
  postLikes?: Prisma.PostLikeCreateNestedManyWithoutUserInput
  comments?: Prisma.CommentCreateNestedManyWithoutAuthorInput
  notifications?: Prisma.NotificationCreateNestedManyWithoutRecipientInput
}

export type AlumniProfileUncheckedCreateWithoutDonationsInput = {
  id?: string
  userId: string
  firstName: string
  lastName: string
  displayName?: string | null
  bio?: string | null
  profilePicture?: string | null
  primaryOrganizationId?: string | null
  graduationYear: number
  programType: string
  centerLocation: string
  achievements?: string | null
  currentPosition?: string | null
  currentCompany?: string | null
  industry?: string | null
  location?: string | null
  linkedInUrl?: string | null
  phoneNumber?: string | null
  mentorshipOffered?: boolean
  mentorshipSought?: boolean
  skillsOffered?: string | null
  skillsWanted?: string | null
  profileVisibility?: $Enums.ProfileVisibility
  showEmail?: boolean
  showPhone?: boolean
  showLocation?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  posts?: Prisma.PostUncheckedCreateNestedManyWithoutAuthorInput
  connections?: Prisma.ConnectionUncheckedCreateNestedManyWithoutRequesterInput
  connectionRequests?: Prisma.ConnectionUncheckedCreateNestedManyWithoutRequestedInput
  eventRegistrations?: Prisma.EventRegistrationUncheckedCreateNestedManyWithoutAttendeeInput
  messages?: Prisma.MessageUncheckedCreateNestedManyWithoutSenderInput
  receivedMessages?: Prisma.MessageUncheckedCreateNestedManyWithoutReceiverInput
  postLikes?: Prisma.PostLikeUncheckedCreateNestedManyWithoutUserInput
  comments?: Prisma.CommentUncheckedCreateNestedManyWithoutAuthorInput
  notifications?: Prisma.NotificationUncheckedCreateNestedManyWithoutRecipientInput
}

export type AlumniProfileCreateOrConnectWithoutDonationsInput = {
  where: Prisma.AlumniProfileWhereUniqueInput
  create: Prisma.XOR<Prisma.AlumniProfileCreateWithoutDonationsInput, Prisma.AlumniProfileUncheckedCreateWithoutDonationsInput>
}

export type AlumniProfileUpsertWithoutDonationsInput = {
  update: Prisma.XOR<Prisma.AlumniProfileUpdateWithoutDonationsInput, Prisma.AlumniProfileUncheckedUpdateWithoutDonationsInput>
  create: Prisma.XOR<Prisma.AlumniProfileCreateWithoutDonationsInput, Prisma.AlumniProfileUncheckedCreateWithoutDonationsInput>
  where?: Prisma.AlumniProfileWhereInput
}

export type AlumniProfileUpdateToOneWithWhereWithoutDonationsInput = {
  where?: Prisma.AlumniProfileWhereInput
  data: Prisma.XOR<Prisma.AlumniProfileUpdateWithoutDonationsInput, Prisma.AlumniProfileUncheckedUpdateWithoutDonationsInput>
}

export type AlumniProfileUpdateWithoutDonationsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bio?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profilePicture?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  graduationYear?: Prisma.IntFieldUpdateOperationsInput | number
  programType?: Prisma.StringFieldUpdateOperationsInput | string
  centerLocation?: Prisma.StringFieldUpdateOperationsInput | string
  achievements?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentPosition?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentCompany?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  industry?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  linkedInUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phoneNumber?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  mentorshipOffered?: Prisma.BoolFieldUpdateOperationsInput | boolean
  mentorshipSought?: Prisma.BoolFieldUpdateOperationsInput | boolean
  skillsOffered?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillsWanted?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profileVisibility?: Prisma.EnumProfileVisibilityFieldUpdateOperationsInput | $Enums.ProfileVisibility
  showEmail?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showPhone?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showLocation?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.UserUpdateOneRequiredWithoutAlumniProfileNestedInput
  primaryOrganization?: Prisma.OrganizationUpdateOneWithoutAlumniProfilesNestedInput
  posts?: Prisma.PostUpdateManyWithoutAuthorNestedInput
  connections?: Prisma.ConnectionUpdateManyWithoutRequesterNestedInput
  connectionRequests?: Prisma.ConnectionUpdateManyWithoutRequestedNestedInput
  eventRegistrations?: Prisma.EventRegistrationUpdateManyWithoutAttendeeNestedInput
  messages?: Prisma.MessageUpdateManyWithoutSenderNestedInput
  receivedMessages?: Prisma.MessageUpdateManyWithoutReceiverNestedInput
  postLikes?: Prisma.PostLikeUpdateManyWithoutUserNestedInput
  comments?: Prisma.CommentUpdateManyWithoutAuthorNestedInput
  notifications?: Prisma.NotificationUpdateManyWithoutRecipientNestedInput
}

export type AlumniProfileUncheckedUpdateWithoutDonationsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bio?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profilePicture?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  primaryOrganizationId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  graduationYear?: Prisma.IntFieldUpdateOperationsInput | number
  programType?: Prisma.StringFieldUpdateOperationsInput | string
  centerLocation?: Prisma.StringFieldUpdateOperationsInput | string
  achievements?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentPosition?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentCompany?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  industry?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  linkedInUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phoneNumber?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  mentorshipOffered?: Prisma.BoolFieldUpdateOperationsInput | boolean
  mentorshipSought?: Prisma.BoolFieldUpdateOperationsInput | boolean
  skillsOffered?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillsWanted?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profileVisibility?: Prisma.EnumProfileVisibilityFieldUpdateOperationsInput | $Enums.ProfileVisibility
  showEmail?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showPhone?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showLocation?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  posts?: Prisma.PostUncheckedUpdateManyWithoutAuthorNestedInput
  connections?: Prisma.ConnectionUncheckedUpdateManyWithoutRequesterNestedInput
  connectionRequests?: Prisma.ConnectionUncheckedUpdateManyWithoutRequestedNestedInput
  eventRegistrations?: Prisma.EventRegistrationUncheckedUpdateManyWithoutAttendeeNestedInput
  messages?: Prisma.MessageUncheckedUpdateManyWithoutSenderNestedInput
  receivedMessages?: Prisma.MessageUncheckedUpdateManyWithoutReceiverNestedInput
  postLikes?: Prisma.PostLikeUncheckedUpdateManyWithoutUserNestedInput
  comments?: Prisma.CommentUncheckedUpdateManyWithoutAuthorNestedInput
  notifications?: Prisma.NotificationUncheckedUpdateManyWithoutRecipientNestedInput
}

export type AlumniProfileCreateWithoutNotificationsInput = {
  id?: string
  firstName: string
  lastName: string
  displayName?: string | null
  bio?: string | null
  profilePicture?: string | null
  graduationYear: number
  programType: string
  centerLocation: string
  achievements?: string | null
  currentPosition?: string | null
  currentCompany?: string | null
  industry?: string | null
  location?: string | null
  linkedInUrl?: string | null
  phoneNumber?: string | null
  mentorshipOffered?: boolean
  mentorshipSought?: boolean
  skillsOffered?: string | null
  skillsWanted?: string | null
  profileVisibility?: $Enums.ProfileVisibility
  showEmail?: boolean
  showPhone?: boolean
  showLocation?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  user: Prisma.UserCreateNestedOneWithoutAlumniProfileInput
  primaryOrganization?: Prisma.OrganizationCreateNestedOneWithoutAlumniProfilesInput
  posts?: Prisma.PostCreateNestedManyWithoutAuthorInput
  connections?: Prisma.ConnectionCreateNestedManyWithoutRequesterInput
  connectionRequests?: Prisma.ConnectionCreateNestedManyWithoutRequestedInput
  eventRegistrations?: Prisma.EventRegistrationCreateNestedManyWithoutAttendeeInput
  donations?: Prisma.DonationCreateNestedManyWithoutDonorInput
  messages?: Prisma.MessageCreateNestedManyWithoutSenderInput
  receivedMessages?: Prisma.MessageCreateNestedManyWithoutReceiverInput
  postLikes?: Prisma.PostLikeCreateNestedManyWithoutUserInput
  comments?: Prisma.CommentCreateNestedManyWithoutAuthorInput
}

export type AlumniProfileUncheckedCreateWithoutNotificationsInput = {
  id?: string
  userId: string
  firstName: string
  lastName: string
  displayName?: string | null
  bio?: string | null
  profilePicture?: string | null
  primaryOrganizationId?: string | null
  graduationYear: number
  programType: string
  centerLocation: string
  achievements?: string | null
  currentPosition?: string | null
  currentCompany?: string | null
  industry?: string | null
  location?: string | null
  linkedInUrl?: string | null
  phoneNumber?: string | null
  mentorshipOffered?: boolean
  mentorshipSought?: boolean
  skillsOffered?: string | null
  skillsWanted?: string | null
  profileVisibility?: $Enums.ProfileVisibility
  showEmail?: boolean
  showPhone?: boolean
  showLocation?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  posts?: Prisma.PostUncheckedCreateNestedManyWithoutAuthorInput
  connections?: Prisma.ConnectionUncheckedCreateNestedManyWithoutRequesterInput
  connectionRequests?: Prisma.ConnectionUncheckedCreateNestedManyWithoutRequestedInput
  eventRegistrations?: Prisma.EventRegistrationUncheckedCreateNestedManyWithoutAttendeeInput
  donations?: Prisma.DonationUncheckedCreateNestedManyWithoutDonorInput
  messages?: Prisma.MessageUncheckedCreateNestedManyWithoutSenderInput
  receivedMessages?: Prisma.MessageUncheckedCreateNestedManyWithoutReceiverInput
  postLikes?: Prisma.PostLikeUncheckedCreateNestedManyWithoutUserInput
  comments?: Prisma.CommentUncheckedCreateNestedManyWithoutAuthorInput
}

export type AlumniProfileCreateOrConnectWithoutNotificationsInput = {
  where: Prisma.AlumniProfileWhereUniqueInput
  create: Prisma.XOR<Prisma.AlumniProfileCreateWithoutNotificationsInput, Prisma.AlumniProfileUncheckedCreateWithoutNotificationsInput>
}

export type AlumniProfileUpsertWithoutNotificationsInput = {
  update: Prisma.XOR<Prisma.AlumniProfileUpdateWithoutNotificationsInput, Prisma.AlumniProfileUncheckedUpdateWithoutNotificationsInput>
  create: Prisma.XOR<Prisma.AlumniProfileCreateWithoutNotificationsInput, Prisma.AlumniProfileUncheckedCreateWithoutNotificationsInput>
  where?: Prisma.AlumniProfileWhereInput
}

export type AlumniProfileUpdateToOneWithWhereWithoutNotificationsInput = {
  where?: Prisma.AlumniProfileWhereInput
  data: Prisma.XOR<Prisma.AlumniProfileUpdateWithoutNotificationsInput, Prisma.AlumniProfileUncheckedUpdateWithoutNotificationsInput>
}

export type AlumniProfileUpdateWithoutNotificationsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bio?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profilePicture?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  graduationYear?: Prisma.IntFieldUpdateOperationsInput | number
  programType?: Prisma.StringFieldUpdateOperationsInput | string
  centerLocation?: Prisma.StringFieldUpdateOperationsInput | string
  achievements?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentPosition?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentCompany?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  industry?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  linkedInUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phoneNumber?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  mentorshipOffered?: Prisma.BoolFieldUpdateOperationsInput | boolean
  mentorshipSought?: Prisma.BoolFieldUpdateOperationsInput | boolean
  skillsOffered?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillsWanted?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profileVisibility?: Prisma.EnumProfileVisibilityFieldUpdateOperationsInput | $Enums.ProfileVisibility
  showEmail?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showPhone?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showLocation?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.UserUpdateOneRequiredWithoutAlumniProfileNestedInput
  primaryOrganization?: Prisma.OrganizationUpdateOneWithoutAlumniProfilesNestedInput
  posts?: Prisma.PostUpdateManyWithoutAuthorNestedInput
  connections?: Prisma.ConnectionUpdateManyWithoutRequesterNestedInput
  connectionRequests?: Prisma.ConnectionUpdateManyWithoutRequestedNestedInput
  eventRegistrations?: Prisma.EventRegistrationUpdateManyWithoutAttendeeNestedInput
  donations?: Prisma.DonationUpdateManyWithoutDonorNestedInput
  messages?: Prisma.MessageUpdateManyWithoutSenderNestedInput
  receivedMessages?: Prisma.MessageUpdateManyWithoutReceiverNestedInput
  postLikes?: Prisma.PostLikeUpdateManyWithoutUserNestedInput
  comments?: Prisma.CommentUpdateManyWithoutAuthorNestedInput
}

export type AlumniProfileUncheckedUpdateWithoutNotificationsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bio?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profilePicture?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  primaryOrganizationId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  graduationYear?: Prisma.IntFieldUpdateOperationsInput | number
  programType?: Prisma.StringFieldUpdateOperationsInput | string
  centerLocation?: Prisma.StringFieldUpdateOperationsInput | string
  achievements?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentPosition?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentCompany?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  industry?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  linkedInUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phoneNumber?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  mentorshipOffered?: Prisma.BoolFieldUpdateOperationsInput | boolean
  mentorshipSought?: Prisma.BoolFieldUpdateOperationsInput | boolean
  skillsOffered?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillsWanted?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profileVisibility?: Prisma.EnumProfileVisibilityFieldUpdateOperationsInput | $Enums.ProfileVisibility
  showEmail?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showPhone?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showLocation?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  posts?: Prisma.PostUncheckedUpdateManyWithoutAuthorNestedInput
  connections?: Prisma.ConnectionUncheckedUpdateManyWithoutRequesterNestedInput
  connectionRequests?: Prisma.ConnectionUncheckedUpdateManyWithoutRequestedNestedInput
  eventRegistrations?: Prisma.EventRegistrationUncheckedUpdateManyWithoutAttendeeNestedInput
  donations?: Prisma.DonationUncheckedUpdateManyWithoutDonorNestedInput
  messages?: Prisma.MessageUncheckedUpdateManyWithoutSenderNestedInput
  receivedMessages?: Prisma.MessageUncheckedUpdateManyWithoutReceiverNestedInput
  postLikes?: Prisma.PostLikeUncheckedUpdateManyWithoutUserNestedInput
  comments?: Prisma.CommentUncheckedUpdateManyWithoutAuthorNestedInput
}

export type AlumniProfileCreateManyPrimaryOrganizationInput = {
  id?: string
  userId: string
  firstName: string
  lastName: string
  displayName?: string | null
  bio?: string | null
  profilePicture?: string | null
  graduationYear: number
  programType: string
  centerLocation: string
  achievements?: string | null
  currentPosition?: string | null
  currentCompany?: string | null
  industry?: string | null
  location?: string | null
  linkedInUrl?: string | null
  phoneNumber?: string | null
  mentorshipOffered?: boolean
  mentorshipSought?: boolean
  skillsOffered?: string | null
  skillsWanted?: string | null
  profileVisibility?: $Enums.ProfileVisibility
  showEmail?: boolean
  showPhone?: boolean
  showLocation?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type AlumniProfileUpdateWithoutPrimaryOrganizationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bio?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profilePicture?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  graduationYear?: Prisma.IntFieldUpdateOperationsInput | number
  programType?: Prisma.StringFieldUpdateOperationsInput | string
  centerLocation?: Prisma.StringFieldUpdateOperationsInput | string
  achievements?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentPosition?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentCompany?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  industry?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  linkedInUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phoneNumber?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  mentorshipOffered?: Prisma.BoolFieldUpdateOperationsInput | boolean
  mentorshipSought?: Prisma.BoolFieldUpdateOperationsInput | boolean
  skillsOffered?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillsWanted?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profileVisibility?: Prisma.EnumProfileVisibilityFieldUpdateOperationsInput | $Enums.ProfileVisibility
  showEmail?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showPhone?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showLocation?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.UserUpdateOneRequiredWithoutAlumniProfileNestedInput
  posts?: Prisma.PostUpdateManyWithoutAuthorNestedInput
  connections?: Prisma.ConnectionUpdateManyWithoutRequesterNestedInput
  connectionRequests?: Prisma.ConnectionUpdateManyWithoutRequestedNestedInput
  eventRegistrations?: Prisma.EventRegistrationUpdateManyWithoutAttendeeNestedInput
  donations?: Prisma.DonationUpdateManyWithoutDonorNestedInput
  messages?: Prisma.MessageUpdateManyWithoutSenderNestedInput
  receivedMessages?: Prisma.MessageUpdateManyWithoutReceiverNestedInput
  postLikes?: Prisma.PostLikeUpdateManyWithoutUserNestedInput
  comments?: Prisma.CommentUpdateManyWithoutAuthorNestedInput
  notifications?: Prisma.NotificationUpdateManyWithoutRecipientNestedInput
}

export type AlumniProfileUncheckedUpdateWithoutPrimaryOrganizationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bio?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profilePicture?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  graduationYear?: Prisma.IntFieldUpdateOperationsInput | number
  programType?: Prisma.StringFieldUpdateOperationsInput | string
  centerLocation?: Prisma.StringFieldUpdateOperationsInput | string
  achievements?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentPosition?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentCompany?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  industry?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  linkedInUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phoneNumber?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  mentorshipOffered?: Prisma.BoolFieldUpdateOperationsInput | boolean
  mentorshipSought?: Prisma.BoolFieldUpdateOperationsInput | boolean
  skillsOffered?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillsWanted?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profileVisibility?: Prisma.EnumProfileVisibilityFieldUpdateOperationsInput | $Enums.ProfileVisibility
  showEmail?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showPhone?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showLocation?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  posts?: Prisma.PostUncheckedUpdateManyWithoutAuthorNestedInput
  connections?: Prisma.ConnectionUncheckedUpdateManyWithoutRequesterNestedInput
  connectionRequests?: Prisma.ConnectionUncheckedUpdateManyWithoutRequestedNestedInput
  eventRegistrations?: Prisma.EventRegistrationUncheckedUpdateManyWithoutAttendeeNestedInput
  donations?: Prisma.DonationUncheckedUpdateManyWithoutDonorNestedInput
  messages?: Prisma.MessageUncheckedUpdateManyWithoutSenderNestedInput
  receivedMessages?: Prisma.MessageUncheckedUpdateManyWithoutReceiverNestedInput
  postLikes?: Prisma.PostLikeUncheckedUpdateManyWithoutUserNestedInput
  comments?: Prisma.CommentUncheckedUpdateManyWithoutAuthorNestedInput
  notifications?: Prisma.NotificationUncheckedUpdateManyWithoutRecipientNestedInput
}

export type AlumniProfileUncheckedUpdateManyWithoutPrimaryOrganizationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  displayName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  bio?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profilePicture?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  graduationYear?: Prisma.IntFieldUpdateOperationsInput | number
  programType?: Prisma.StringFieldUpdateOperationsInput | string
  centerLocation?: Prisma.StringFieldUpdateOperationsInput | string
  achievements?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentPosition?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currentCompany?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  industry?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  linkedInUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phoneNumber?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  mentorshipOffered?: Prisma.BoolFieldUpdateOperationsInput | boolean
  mentorshipSought?: Prisma.BoolFieldUpdateOperationsInput | boolean
  skillsOffered?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  skillsWanted?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  profileVisibility?: Prisma.EnumProfileVisibilityFieldUpdateOperationsInput | $Enums.ProfileVisibility
  showEmail?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showPhone?: Prisma.BoolFieldUpdateOperationsInput | boolean
  showLocation?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}


/**
 * Count Type AlumniProfileCountOutputType
 */

export type AlumniProfileCountOutputType = {
  posts: number
  connections: number
  connectionRequests: number
  eventRegistrations: number
  donations: number
  messages: number
  receivedMessages: number
  postLikes: number
  comments: number
  notifications: number
}

export type AlumniProfileCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  posts?: boolean | AlumniProfileCountOutputTypeCountPostsArgs
  connections?: boolean | AlumniProfileCountOutputTypeCountConnectionsArgs
  connectionRequests?: boolean | AlumniProfileCountOutputTypeCountConnectionRequestsArgs
  eventRegistrations?: boolean | AlumniProfileCountOutputTypeCountEventRegistrationsArgs
  donations?: boolean | AlumniProfileCountOutputTypeCountDonationsArgs
  messages?: boolean | AlumniProfileCountOutputTypeCountMessagesArgs
  receivedMessages?: boolean | AlumniProfileCountOutputTypeCountReceivedMessagesArgs
  postLikes?: boolean | AlumniProfileCountOutputTypeCountPostLikesArgs
  comments?: boolean | AlumniProfileCountOutputTypeCountCommentsArgs
  notifications?: boolean | AlumniProfileCountOutputTypeCountNotificationsArgs
}

/**
 * AlumniProfileCountOutputType without action
 */
export type AlumniProfileCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AlumniProfileCountOutputType
   */
  select?: Prisma.AlumniProfileCountOutputTypeSelect<ExtArgs> | null
}

/**
 * AlumniProfileCountOutputType without action
 */
export type AlumniProfileCountOutputTypeCountPostsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.PostWhereInput
}

/**
 * AlumniProfileCountOutputType without action
 */
export type AlumniProfileCountOutputTypeCountConnectionsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ConnectionWhereInput
}

/**
 * AlumniProfileCountOutputType without action
 */
export type AlumniProfileCountOutputTypeCountConnectionRequestsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ConnectionWhereInput
}

/**
 * AlumniProfileCountOutputType without action
 */
export type AlumniProfileCountOutputTypeCountEventRegistrationsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.EventRegistrationWhereInput
}

/**
 * AlumniProfileCountOutputType without action
 */
export type AlumniProfileCountOutputTypeCountDonationsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.DonationWhereInput
}

/**
 * AlumniProfileCountOutputType without action
 */
export type AlumniProfileCountOutputTypeCountMessagesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.MessageWhereInput
}

/**
 * AlumniProfileCountOutputType without action
 */
export type AlumniProfileCountOutputTypeCountReceivedMessagesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.MessageWhereInput
}

/**
 * AlumniProfileCountOutputType without action
 */
export type AlumniProfileCountOutputTypeCountPostLikesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.PostLikeWhereInput
}

/**
 * AlumniProfileCountOutputType without action
 */
export type AlumniProfileCountOutputTypeCountCommentsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.CommentWhereInput
}

/**
 * AlumniProfileCountOutputType without action
 */
export type AlumniProfileCountOutputTypeCountNotificationsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.NotificationWhereInput
}


export type AlumniProfileSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  userId?: boolean
  firstName?: boolean
  lastName?: boolean
  displayName?: boolean
  bio?: boolean
  profilePicture?: boolean
  primaryOrganizationId?: boolean
  graduationYear?: boolean
  programType?: boolean
  centerLocation?: boolean
  achievements?: boolean
  currentPosition?: boolean
  currentCompany?: boolean
  industry?: boolean
  location?: boolean
  linkedInUrl?: boolean
  phoneNumber?: boolean
  mentorshipOffered?: boolean
  mentorshipSought?: boolean
  skillsOffered?: boolean
  skillsWanted?: boolean
  profileVisibility?: boolean
  showEmail?: boolean
  showPhone?: boolean
  showLocation?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  primaryOrganization?: boolean | Prisma.AlumniProfile$primaryOrganizationArgs<ExtArgs>
  posts?: boolean | Prisma.AlumniProfile$postsArgs<ExtArgs>
  connections?: boolean | Prisma.AlumniProfile$connectionsArgs<ExtArgs>
  connectionRequests?: boolean | Prisma.AlumniProfile$connectionRequestsArgs<ExtArgs>
  eventRegistrations?: boolean | Prisma.AlumniProfile$eventRegistrationsArgs<ExtArgs>
  donations?: boolean | Prisma.AlumniProfile$donationsArgs<ExtArgs>
  messages?: boolean | Prisma.AlumniProfile$messagesArgs<ExtArgs>
  receivedMessages?: boolean | Prisma.AlumniProfile$receivedMessagesArgs<ExtArgs>
  postLikes?: boolean | Prisma.AlumniProfile$postLikesArgs<ExtArgs>
  comments?: boolean | Prisma.AlumniProfile$commentsArgs<ExtArgs>
  notifications?: boolean | Prisma.AlumniProfile$notificationsArgs<ExtArgs>
  _count?: boolean | Prisma.AlumniProfileCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["alumniProfile"]>



export type AlumniProfileSelectScalar = {
  id?: boolean
  userId?: boolean
  firstName?: boolean
  lastName?: boolean
  displayName?: boolean
  bio?: boolean
  profilePicture?: boolean
  primaryOrganizationId?: boolean
  graduationYear?: boolean
  programType?: boolean
  centerLocation?: boolean
  achievements?: boolean
  currentPosition?: boolean
  currentCompany?: boolean
  industry?: boolean
  location?: boolean
  linkedInUrl?: boolean
  phoneNumber?: boolean
  mentorshipOffered?: boolean
  mentorshipSought?: boolean
  skillsOffered?: boolean
  skillsWanted?: boolean
  profileVisibility?: boolean
  showEmail?: boolean
  showPhone?: boolean
  showLocation?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type AlumniProfileOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "userId" | "firstName" | "lastName" | "displayName" | "bio" | "profilePicture" | "primaryOrganizationId" | "graduationYear" | "programType" | "centerLocation" | "achievements" | "currentPosition" | "currentCompany" | "industry" | "location" | "linkedInUrl" | "phoneNumber" | "mentorshipOffered" | "mentorshipSought" | "skillsOffered" | "skillsWanted" | "profileVisibility" | "showEmail" | "showPhone" | "showLocation" | "createdAt" | "updatedAt", ExtArgs["result"]["alumniProfile"]>
export type AlumniProfileInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  primaryOrganization?: boolean | Prisma.AlumniProfile$primaryOrganizationArgs<ExtArgs>
  posts?: boolean | Prisma.AlumniProfile$postsArgs<ExtArgs>
  connections?: boolean | Prisma.AlumniProfile$connectionsArgs<ExtArgs>
  connectionRequests?: boolean | Prisma.AlumniProfile$connectionRequestsArgs<ExtArgs>
  eventRegistrations?: boolean | Prisma.AlumniProfile$eventRegistrationsArgs<ExtArgs>
  donations?: boolean | Prisma.AlumniProfile$donationsArgs<ExtArgs>
  messages?: boolean | Prisma.AlumniProfile$messagesArgs<ExtArgs>
  receivedMessages?: boolean | Prisma.AlumniProfile$receivedMessagesArgs<ExtArgs>
  postLikes?: boolean | Prisma.AlumniProfile$postLikesArgs<ExtArgs>
  comments?: boolean | Prisma.AlumniProfile$commentsArgs<ExtArgs>
  notifications?: boolean | Prisma.AlumniProfile$notificationsArgs<ExtArgs>
  _count?: boolean | Prisma.AlumniProfileCountOutputTypeDefaultArgs<ExtArgs>
}

export type $AlumniProfilePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "AlumniProfile"
  objects: {
    user: Prisma.$UserPayload<ExtArgs>
    primaryOrganization: Prisma.$OrganizationPayload<ExtArgs> | null
    posts: Prisma.$PostPayload<ExtArgs>[]
    connections: Prisma.$ConnectionPayload<ExtArgs>[]
    connectionRequests: Prisma.$ConnectionPayload<ExtArgs>[]
    eventRegistrations: Prisma.$EventRegistrationPayload<ExtArgs>[]
    donations: Prisma.$DonationPayload<ExtArgs>[]
    messages: Prisma.$MessagePayload<ExtArgs>[]
    receivedMessages: Prisma.$MessagePayload<ExtArgs>[]
    postLikes: Prisma.$PostLikePayload<ExtArgs>[]
    comments: Prisma.$CommentPayload<ExtArgs>[]
    notifications: Prisma.$NotificationPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    userId: string
    firstName: string
    lastName: string
    displayName: string | null
    bio: string | null
    profilePicture: string | null
    primaryOrganizationId: string | null
    graduationYear: number
    programType: string
    centerLocation: string
    achievements: string | null
    currentPosition: string | null
    currentCompany: string | null
    industry: string | null
    location: string | null
    linkedInUrl: string | null
    phoneNumber: string | null
    mentorshipOffered: boolean
    mentorshipSought: boolean
    skillsOffered: string | null
    skillsWanted: string | null
    profileVisibility: $Enums.ProfileVisibility
    showEmail: boolean
    showPhone: boolean
    showLocation: boolean
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["alumniProfile"]>
  composites: {}
}

export type AlumniProfileGetPayload<S extends boolean | null | undefined | AlumniProfileDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$AlumniProfilePayload, S>

export type AlumniProfileCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<AlumniProfileFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: AlumniProfileCountAggregateInputType | true
  }

export interface AlumniProfileDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['AlumniProfile'], meta: { name: 'AlumniProfile' } }
  /**
   * Find zero or one AlumniProfile that matches the filter.
   * @param {AlumniProfileFindUniqueArgs} args - Arguments to find a AlumniProfile
   * @example
   * // Get one AlumniProfile
   * const alumniProfile = await prisma.alumniProfile.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends AlumniProfileFindUniqueArgs>(args: Prisma.SelectSubset<T, AlumniProfileFindUniqueArgs<ExtArgs>>): Prisma.Prisma__AlumniProfileClient<runtime.Types.Result.GetResult<Prisma.$AlumniProfilePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one AlumniProfile that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {AlumniProfileFindUniqueOrThrowArgs} args - Arguments to find a AlumniProfile
   * @example
   * // Get one AlumniProfile
   * const alumniProfile = await prisma.alumniProfile.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends AlumniProfileFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, AlumniProfileFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__AlumniProfileClient<runtime.Types.Result.GetResult<Prisma.$AlumniProfilePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first AlumniProfile that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AlumniProfileFindFirstArgs} args - Arguments to find a AlumniProfile
   * @example
   * // Get one AlumniProfile
   * const alumniProfile = await prisma.alumniProfile.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends AlumniProfileFindFirstArgs>(args?: Prisma.SelectSubset<T, AlumniProfileFindFirstArgs<ExtArgs>>): Prisma.Prisma__AlumniProfileClient<runtime.Types.Result.GetResult<Prisma.$AlumniProfilePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first AlumniProfile that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AlumniProfileFindFirstOrThrowArgs} args - Arguments to find a AlumniProfile
   * @example
   * // Get one AlumniProfile
   * const alumniProfile = await prisma.alumniProfile.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends AlumniProfileFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, AlumniProfileFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__AlumniProfileClient<runtime.Types.Result.GetResult<Prisma.$AlumniProfilePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more AlumniProfiles that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AlumniProfileFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all AlumniProfiles
   * const alumniProfiles = await prisma.alumniProfile.findMany()
   * 
   * // Get first 10 AlumniProfiles
   * const alumniProfiles = await prisma.alumniProfile.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const alumniProfileWithIdOnly = await prisma.alumniProfile.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends AlumniProfileFindManyArgs>(args?: Prisma.SelectSubset<T, AlumniProfileFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$AlumniProfilePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a AlumniProfile.
   * @param {AlumniProfileCreateArgs} args - Arguments to create a AlumniProfile.
   * @example
   * // Create one AlumniProfile
   * const AlumniProfile = await prisma.alumniProfile.create({
   *   data: {
   *     // ... data to create a AlumniProfile
   *   }
   * })
   * 
   */
  create<T extends AlumniProfileCreateArgs>(args: Prisma.SelectSubset<T, AlumniProfileCreateArgs<ExtArgs>>): Prisma.Prisma__AlumniProfileClient<runtime.Types.Result.GetResult<Prisma.$AlumniProfilePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many AlumniProfiles.
   * @param {AlumniProfileCreateManyArgs} args - Arguments to create many AlumniProfiles.
   * @example
   * // Create many AlumniProfiles
   * const alumniProfile = await prisma.alumniProfile.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends AlumniProfileCreateManyArgs>(args?: Prisma.SelectSubset<T, AlumniProfileCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a AlumniProfile.
   * @param {AlumniProfileDeleteArgs} args - Arguments to delete one AlumniProfile.
   * @example
   * // Delete one AlumniProfile
   * const AlumniProfile = await prisma.alumniProfile.delete({
   *   where: {
   *     // ... filter to delete one AlumniProfile
   *   }
   * })
   * 
   */
  delete<T extends AlumniProfileDeleteArgs>(args: Prisma.SelectSubset<T, AlumniProfileDeleteArgs<ExtArgs>>): Prisma.Prisma__AlumniProfileClient<runtime.Types.Result.GetResult<Prisma.$AlumniProfilePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one AlumniProfile.
   * @param {AlumniProfileUpdateArgs} args - Arguments to update one AlumniProfile.
   * @example
   * // Update one AlumniProfile
   * const alumniProfile = await prisma.alumniProfile.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends AlumniProfileUpdateArgs>(args: Prisma.SelectSubset<T, AlumniProfileUpdateArgs<ExtArgs>>): Prisma.Prisma__AlumniProfileClient<runtime.Types.Result.GetResult<Prisma.$AlumniProfilePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more AlumniProfiles.
   * @param {AlumniProfileDeleteManyArgs} args - Arguments to filter AlumniProfiles to delete.
   * @example
   * // Delete a few AlumniProfiles
   * const { count } = await prisma.alumniProfile.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends AlumniProfileDeleteManyArgs>(args?: Prisma.SelectSubset<T, AlumniProfileDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more AlumniProfiles.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AlumniProfileUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many AlumniProfiles
   * const alumniProfile = await prisma.alumniProfile.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends AlumniProfileUpdateManyArgs>(args: Prisma.SelectSubset<T, AlumniProfileUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one AlumniProfile.
   * @param {AlumniProfileUpsertArgs} args - Arguments to update or create a AlumniProfile.
   * @example
   * // Update or create a AlumniProfile
   * const alumniProfile = await prisma.alumniProfile.upsert({
   *   create: {
   *     // ... data to create a AlumniProfile
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the AlumniProfile we want to update
   *   }
   * })
   */
  upsert<T extends AlumniProfileUpsertArgs>(args: Prisma.SelectSubset<T, AlumniProfileUpsertArgs<ExtArgs>>): Prisma.Prisma__AlumniProfileClient<runtime.Types.Result.GetResult<Prisma.$AlumniProfilePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of AlumniProfiles.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AlumniProfileCountArgs} args - Arguments to filter AlumniProfiles to count.
   * @example
   * // Count the number of AlumniProfiles
   * const count = await prisma.alumniProfile.count({
   *   where: {
   *     // ... the filter for the AlumniProfiles we want to count
   *   }
   * })
  **/
  count<T extends AlumniProfileCountArgs>(
    args?: Prisma.Subset<T, AlumniProfileCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], AlumniProfileCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a AlumniProfile.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AlumniProfileAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends AlumniProfileAggregateArgs>(args: Prisma.Subset<T, AlumniProfileAggregateArgs>): Prisma.PrismaPromise<GetAlumniProfileAggregateType<T>>

  /**
   * Group by AlumniProfile.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {AlumniProfileGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends AlumniProfileGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: AlumniProfileGroupByArgs['orderBy'] }
      : { orderBy?: AlumniProfileGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, AlumniProfileGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetAlumniProfileGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the AlumniProfile model
 */
readonly fields: AlumniProfileFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for AlumniProfile.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__AlumniProfileClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  user<T extends Prisma.UserDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.UserDefaultArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  primaryOrganization<T extends Prisma.AlumniProfile$primaryOrganizationArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.AlumniProfile$primaryOrganizationArgs<ExtArgs>>): Prisma.Prisma__OrganizationClient<runtime.Types.Result.GetResult<Prisma.$OrganizationPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  posts<T extends Prisma.AlumniProfile$postsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.AlumniProfile$postsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$PostPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  connections<T extends Prisma.AlumniProfile$connectionsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.AlumniProfile$connectionsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ConnectionPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  connectionRequests<T extends Prisma.AlumniProfile$connectionRequestsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.AlumniProfile$connectionRequestsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ConnectionPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  eventRegistrations<T extends Prisma.AlumniProfile$eventRegistrationsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.AlumniProfile$eventRegistrationsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$EventRegistrationPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  donations<T extends Prisma.AlumniProfile$donationsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.AlumniProfile$donationsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$DonationPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  messages<T extends Prisma.AlumniProfile$messagesArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.AlumniProfile$messagesArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$MessagePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  receivedMessages<T extends Prisma.AlumniProfile$receivedMessagesArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.AlumniProfile$receivedMessagesArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$MessagePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  postLikes<T extends Prisma.AlumniProfile$postLikesArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.AlumniProfile$postLikesArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$PostLikePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  comments<T extends Prisma.AlumniProfile$commentsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.AlumniProfile$commentsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$CommentPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  notifications<T extends Prisma.AlumniProfile$notificationsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.AlumniProfile$notificationsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$NotificationPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the AlumniProfile model
 */
export interface AlumniProfileFieldRefs {
  readonly id: Prisma.FieldRef<"AlumniProfile", 'String'>
  readonly userId: Prisma.FieldRef<"AlumniProfile", 'String'>
  readonly firstName: Prisma.FieldRef<"AlumniProfile", 'String'>
  readonly lastName: Prisma.FieldRef<"AlumniProfile", 'String'>
  readonly displayName: Prisma.FieldRef<"AlumniProfile", 'String'>
  readonly bio: Prisma.FieldRef<"AlumniProfile", 'String'>
  readonly profilePicture: Prisma.FieldRef<"AlumniProfile", 'String'>
  readonly primaryOrganizationId: Prisma.FieldRef<"AlumniProfile", 'String'>
  readonly graduationYear: Prisma.FieldRef<"AlumniProfile", 'Int'>
  readonly programType: Prisma.FieldRef<"AlumniProfile", 'String'>
  readonly centerLocation: Prisma.FieldRef<"AlumniProfile", 'String'>
  readonly achievements: Prisma.FieldRef<"AlumniProfile", 'String'>
  readonly currentPosition: Prisma.FieldRef<"AlumniProfile", 'String'>
  readonly currentCompany: Prisma.FieldRef<"AlumniProfile", 'String'>
  readonly industry: Prisma.FieldRef<"AlumniProfile", 'String'>
  readonly location: Prisma.FieldRef<"AlumniProfile", 'String'>
  readonly linkedInUrl: Prisma.FieldRef<"AlumniProfile", 'String'>
  readonly phoneNumber: Prisma.FieldRef<"AlumniProfile", 'String'>
  readonly mentorshipOffered: Prisma.FieldRef<"AlumniProfile", 'Boolean'>
  readonly mentorshipSought: Prisma.FieldRef<"AlumniProfile", 'Boolean'>
  readonly skillsOffered: Prisma.FieldRef<"AlumniProfile", 'String'>
  readonly skillsWanted: Prisma.FieldRef<"AlumniProfile", 'String'>
  readonly profileVisibility: Prisma.FieldRef<"AlumniProfile", 'ProfileVisibility'>
  readonly showEmail: Prisma.FieldRef<"AlumniProfile", 'Boolean'>
  readonly showPhone: Prisma.FieldRef<"AlumniProfile", 'Boolean'>
  readonly showLocation: Prisma.FieldRef<"AlumniProfile", 'Boolean'>
  readonly createdAt: Prisma.FieldRef<"AlumniProfile", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"AlumniProfile", 'DateTime'>
}
    

// Custom InputTypes
/**
 * AlumniProfile findUnique
 */
export type AlumniProfileFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AlumniProfile
   */
  select?: Prisma.AlumniProfileSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AlumniProfile
   */
  omit?: Prisma.AlumniProfileOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AlumniProfileInclude<ExtArgs> | null
  /**
   * Filter, which AlumniProfile to fetch.
   */
  where: Prisma.AlumniProfileWhereUniqueInput
}

/**
 * AlumniProfile findUniqueOrThrow
 */
export type AlumniProfileFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AlumniProfile
   */
  select?: Prisma.AlumniProfileSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AlumniProfile
   */
  omit?: Prisma.AlumniProfileOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AlumniProfileInclude<ExtArgs> | null
  /**
   * Filter, which AlumniProfile to fetch.
   */
  where: Prisma.AlumniProfileWhereUniqueInput
}

/**
 * AlumniProfile findFirst
 */
export type AlumniProfileFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AlumniProfile
   */
  select?: Prisma.AlumniProfileSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AlumniProfile
   */
  omit?: Prisma.AlumniProfileOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AlumniProfileInclude<ExtArgs> | null
  /**
   * Filter, which AlumniProfile to fetch.
   */
  where?: Prisma.AlumniProfileWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of AlumniProfiles to fetch.
   */
  orderBy?: Prisma.AlumniProfileOrderByWithRelationInput | Prisma.AlumniProfileOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for AlumniProfiles.
   */
  cursor?: Prisma.AlumniProfileWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` AlumniProfiles from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` AlumniProfiles.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of AlumniProfiles.
   */
  distinct?: Prisma.AlumniProfileScalarFieldEnum | Prisma.AlumniProfileScalarFieldEnum[]
}

/**
 * AlumniProfile findFirstOrThrow
 */
export type AlumniProfileFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AlumniProfile
   */
  select?: Prisma.AlumniProfileSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AlumniProfile
   */
  omit?: Prisma.AlumniProfileOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AlumniProfileInclude<ExtArgs> | null
  /**
   * Filter, which AlumniProfile to fetch.
   */
  where?: Prisma.AlumniProfileWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of AlumniProfiles to fetch.
   */
  orderBy?: Prisma.AlumniProfileOrderByWithRelationInput | Prisma.AlumniProfileOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for AlumniProfiles.
   */
  cursor?: Prisma.AlumniProfileWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` AlumniProfiles from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` AlumniProfiles.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of AlumniProfiles.
   */
  distinct?: Prisma.AlumniProfileScalarFieldEnum | Prisma.AlumniProfileScalarFieldEnum[]
}

/**
 * AlumniProfile findMany
 */
export type AlumniProfileFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AlumniProfile
   */
  select?: Prisma.AlumniProfileSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AlumniProfile
   */
  omit?: Prisma.AlumniProfileOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AlumniProfileInclude<ExtArgs> | null
  /**
   * Filter, which AlumniProfiles to fetch.
   */
  where?: Prisma.AlumniProfileWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of AlumniProfiles to fetch.
   */
  orderBy?: Prisma.AlumniProfileOrderByWithRelationInput | Prisma.AlumniProfileOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing AlumniProfiles.
   */
  cursor?: Prisma.AlumniProfileWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` AlumniProfiles from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` AlumniProfiles.
   */
  skip?: number
  distinct?: Prisma.AlumniProfileScalarFieldEnum | Prisma.AlumniProfileScalarFieldEnum[]
}

/**
 * AlumniProfile create
 */
export type AlumniProfileCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AlumniProfile
   */
  select?: Prisma.AlumniProfileSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AlumniProfile
   */
  omit?: Prisma.AlumniProfileOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AlumniProfileInclude<ExtArgs> | null
  /**
   * The data needed to create a AlumniProfile.
   */
  data: Prisma.XOR<Prisma.AlumniProfileCreateInput, Prisma.AlumniProfileUncheckedCreateInput>
}

/**
 * AlumniProfile createMany
 */
export type AlumniProfileCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many AlumniProfiles.
   */
  data: Prisma.AlumniProfileCreateManyInput | Prisma.AlumniProfileCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * AlumniProfile update
 */
export type AlumniProfileUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AlumniProfile
   */
  select?: Prisma.AlumniProfileSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AlumniProfile
   */
  omit?: Prisma.AlumniProfileOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AlumniProfileInclude<ExtArgs> | null
  /**
   * The data needed to update a AlumniProfile.
   */
  data: Prisma.XOR<Prisma.AlumniProfileUpdateInput, Prisma.AlumniProfileUncheckedUpdateInput>
  /**
   * Choose, which AlumniProfile to update.
   */
  where: Prisma.AlumniProfileWhereUniqueInput
}

/**
 * AlumniProfile updateMany
 */
export type AlumniProfileUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update AlumniProfiles.
   */
  data: Prisma.XOR<Prisma.AlumniProfileUpdateManyMutationInput, Prisma.AlumniProfileUncheckedUpdateManyInput>
  /**
   * Filter which AlumniProfiles to update
   */
  where?: Prisma.AlumniProfileWhereInput
  /**
   * Limit how many AlumniProfiles to update.
   */
  limit?: number
}

/**
 * AlumniProfile upsert
 */
export type AlumniProfileUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AlumniProfile
   */
  select?: Prisma.AlumniProfileSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AlumniProfile
   */
  omit?: Prisma.AlumniProfileOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AlumniProfileInclude<ExtArgs> | null
  /**
   * The filter to search for the AlumniProfile to update in case it exists.
   */
  where: Prisma.AlumniProfileWhereUniqueInput
  /**
   * In case the AlumniProfile found by the `where` argument doesn't exist, create a new AlumniProfile with this data.
   */
  create: Prisma.XOR<Prisma.AlumniProfileCreateInput, Prisma.AlumniProfileUncheckedCreateInput>
  /**
   * In case the AlumniProfile was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.AlumniProfileUpdateInput, Prisma.AlumniProfileUncheckedUpdateInput>
}

/**
 * AlumniProfile delete
 */
export type AlumniProfileDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AlumniProfile
   */
  select?: Prisma.AlumniProfileSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AlumniProfile
   */
  omit?: Prisma.AlumniProfileOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AlumniProfileInclude<ExtArgs> | null
  /**
   * Filter which AlumniProfile to delete.
   */
  where: Prisma.AlumniProfileWhereUniqueInput
}

/**
 * AlumniProfile deleteMany
 */
export type AlumniProfileDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which AlumniProfiles to delete
   */
  where?: Prisma.AlumniProfileWhereInput
  /**
   * Limit how many AlumniProfiles to delete.
   */
  limit?: number
}

/**
 * AlumniProfile.primaryOrganization
 */
export type AlumniProfile$primaryOrganizationArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Organization
   */
  select?: Prisma.OrganizationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Organization
   */
  omit?: Prisma.OrganizationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.OrganizationInclude<ExtArgs> | null
  where?: Prisma.OrganizationWhereInput
}

/**
 * AlumniProfile.posts
 */
export type AlumniProfile$postsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Post
   */
  select?: Prisma.PostSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Post
   */
  omit?: Prisma.PostOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PostInclude<ExtArgs> | null
  where?: Prisma.PostWhereInput
  orderBy?: Prisma.PostOrderByWithRelationInput | Prisma.PostOrderByWithRelationInput[]
  cursor?: Prisma.PostWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.PostScalarFieldEnum | Prisma.PostScalarFieldEnum[]
}

/**
 * AlumniProfile.connections
 */
export type AlumniProfile$connectionsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Connection
   */
  select?: Prisma.ConnectionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Connection
   */
  omit?: Prisma.ConnectionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ConnectionInclude<ExtArgs> | null
  where?: Prisma.ConnectionWhereInput
  orderBy?: Prisma.ConnectionOrderByWithRelationInput | Prisma.ConnectionOrderByWithRelationInput[]
  cursor?: Prisma.ConnectionWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.ConnectionScalarFieldEnum | Prisma.ConnectionScalarFieldEnum[]
}

/**
 * AlumniProfile.connectionRequests
 */
export type AlumniProfile$connectionRequestsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Connection
   */
  select?: Prisma.ConnectionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Connection
   */
  omit?: Prisma.ConnectionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ConnectionInclude<ExtArgs> | null
  where?: Prisma.ConnectionWhereInput
  orderBy?: Prisma.ConnectionOrderByWithRelationInput | Prisma.ConnectionOrderByWithRelationInput[]
  cursor?: Prisma.ConnectionWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.ConnectionScalarFieldEnum | Prisma.ConnectionScalarFieldEnum[]
}

/**
 * AlumniProfile.eventRegistrations
 */
export type AlumniProfile$eventRegistrationsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the EventRegistration
   */
  select?: Prisma.EventRegistrationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the EventRegistration
   */
  omit?: Prisma.EventRegistrationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.EventRegistrationInclude<ExtArgs> | null
  where?: Prisma.EventRegistrationWhereInput
  orderBy?: Prisma.EventRegistrationOrderByWithRelationInput | Prisma.EventRegistrationOrderByWithRelationInput[]
  cursor?: Prisma.EventRegistrationWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.EventRegistrationScalarFieldEnum | Prisma.EventRegistrationScalarFieldEnum[]
}

/**
 * AlumniProfile.donations
 */
export type AlumniProfile$donationsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Donation
   */
  select?: Prisma.DonationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Donation
   */
  omit?: Prisma.DonationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DonationInclude<ExtArgs> | null
  where?: Prisma.DonationWhereInput
  orderBy?: Prisma.DonationOrderByWithRelationInput | Prisma.DonationOrderByWithRelationInput[]
  cursor?: Prisma.DonationWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.DonationScalarFieldEnum | Prisma.DonationScalarFieldEnum[]
}

/**
 * AlumniProfile.messages
 */
export type AlumniProfile$messagesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Message
   */
  select?: Prisma.MessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Message
   */
  omit?: Prisma.MessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageInclude<ExtArgs> | null
  where?: Prisma.MessageWhereInput
  orderBy?: Prisma.MessageOrderByWithRelationInput | Prisma.MessageOrderByWithRelationInput[]
  cursor?: Prisma.MessageWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.MessageScalarFieldEnum | Prisma.MessageScalarFieldEnum[]
}

/**
 * AlumniProfile.receivedMessages
 */
export type AlumniProfile$receivedMessagesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Message
   */
  select?: Prisma.MessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Message
   */
  omit?: Prisma.MessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageInclude<ExtArgs> | null
  where?: Prisma.MessageWhereInput
  orderBy?: Prisma.MessageOrderByWithRelationInput | Prisma.MessageOrderByWithRelationInput[]
  cursor?: Prisma.MessageWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.MessageScalarFieldEnum | Prisma.MessageScalarFieldEnum[]
}

/**
 * AlumniProfile.postLikes
 */
export type AlumniProfile$postLikesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the PostLike
   */
  select?: Prisma.PostLikeSelect<ExtArgs> | null
  /**
   * Omit specific fields from the PostLike
   */
  omit?: Prisma.PostLikeOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PostLikeInclude<ExtArgs> | null
  where?: Prisma.PostLikeWhereInput
  orderBy?: Prisma.PostLikeOrderByWithRelationInput | Prisma.PostLikeOrderByWithRelationInput[]
  cursor?: Prisma.PostLikeWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.PostLikeScalarFieldEnum | Prisma.PostLikeScalarFieldEnum[]
}

/**
 * AlumniProfile.comments
 */
export type AlumniProfile$commentsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Comment
   */
  select?: Prisma.CommentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Comment
   */
  omit?: Prisma.CommentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.CommentInclude<ExtArgs> | null
  where?: Prisma.CommentWhereInput
  orderBy?: Prisma.CommentOrderByWithRelationInput | Prisma.CommentOrderByWithRelationInput[]
  cursor?: Prisma.CommentWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.CommentScalarFieldEnum | Prisma.CommentScalarFieldEnum[]
}

/**
 * AlumniProfile.notifications
 */
export type AlumniProfile$notificationsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Notification
   */
  select?: Prisma.NotificationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Notification
   */
  omit?: Prisma.NotificationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.NotificationInclude<ExtArgs> | null
  where?: Prisma.NotificationWhereInput
  orderBy?: Prisma.NotificationOrderByWithRelationInput | Prisma.NotificationOrderByWithRelationInput[]
  cursor?: Prisma.NotificationWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.NotificationScalarFieldEnum | Prisma.NotificationScalarFieldEnum[]
}

/**
 * AlumniProfile without action
 */
export type AlumniProfileDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AlumniProfile
   */
  select?: Prisma.AlumniProfileSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AlumniProfile
   */
  omit?: Prisma.AlumniProfileOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AlumniProfileInclude<ExtArgs> | null
}
