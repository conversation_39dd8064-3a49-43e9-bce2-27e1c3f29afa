import { z } from "zod";
import { publicProcedure, protectedProcedure } from "../lib/orpc";
import { prisma } from "../lib/database";
import {
  createEventSchema,
  updateEventSchema,
  eventFilterSchema,
  eventRegistrationSchema,
  idSchema,
} from "../lib/validation";
import { getPaginationParams, createPaginationResult } from "../lib/utils";

export const eventsRouter = {
  /**
   * Get published events with filtering (public endpoint)
   */
  list: publicProcedure
    .input(eventFilterSchema)
    .handler(async ({ input, context }) => {
      const { page, limit, skip } = getPaginationParams(input);
      const where: any = {
        eventType: input.eventType ?? undefined,
        organizationId: input.organizationId ?? undefined,
        isVirtual: input.isVirtual ?? undefined,
      };
      if (input.startDate || input.endDate) {
        where.startDateTime = {} as any;
        if (input.startDate) where.startDateTime.gte = input.startDate;
        if (input.endDate) where.startDateTime.lte = input.endDate;
      }
      const [data, total] = await Promise.all([
        prisma.event.findMany({ where, orderBy: { startDateTime: "asc" }, skip, take: limit }),
        prisma.event.count({ where }),
      ]);
      return createPaginationResult(data, total, page, limit);
    }),

  /**
   * Get specific event details (public endpoint)
   */
  getById: publicProcedure
    .input(z.object({ id: idSchema }))
    .handler(async ({ input, context }) => {
      const event = await prisma.event.findUnique({ where: { id: input.id } });
      if (!event) throw new Error("Event not found");
      return event;
    }),

  /**
   * Register for event
   */
  register: protectedProcedure
    .input(eventRegistrationSchema)
    .handler(async ({ input, context }) => {
      const userProfile = await prisma.alumniProfile.findUnique({
        where: { userId: context.user!.id },
        select: { id: true },
      });
      if (!userProfile) throw new Error("Create your alumni profile first");
      return prisma.eventRegistration.create({
        data: { eventId: input.eventId, attendeeId: userProfile.id },
      });
    }),

  /**
   * Update registration status
   */
  updateRegistration: protectedProcedure
    .input(
      z.object({
        eventId: idSchema,
        status: z.enum(["REGISTERED", "ATTENDED", "NO_SHOW", "CANCELLED"]),
      })
    )
    .handler(async ({ input, context }) => {
      const userProfile = await prisma.alumniProfile.findUnique({
        where: { userId: context.user!.id },
        select: { id: true },
      });
      if (!userProfile) throw new Error("Alumni profile not found");

      const registration = await prisma.eventRegistration.findUnique({
        where: {
          eventId_attendeeId: {
            eventId: input.eventId,
            attendeeId: userProfile.id,
          },
        },
      });
      if (!registration) throw new Error("Registration not found");

      return prisma.eventRegistration.update({
        where: { id: registration.id },
        data: { status: input.status },
      });
    }),

  /**
   * Cancel registration
   */
  cancelRegistration: protectedProcedure
    .input(z.object({ eventId: idSchema }))
    .handler(async ({ input, context }) => {
      const userProfile = await prisma.alumniProfile.findUnique({
        where: { userId: context.user!.id },
        select: { id: true },
      });
      if (!userProfile) throw new Error("Alumni profile not found");
      await prisma.eventRegistration.delete({
        where: {
          eventId_attendeeId: { eventId: input.eventId, attendeeId: userProfile.id },
        },
      });
      return { success: true };
    }),

  /**
   * Get user's event registrations
   */
  myRegistrations: protectedProcedure
    .input(
      z.object({
        page: z.number().int().min(1).default(1),
        limit: z.number().int().min(1).max(100).default(20),
        status: z
          .enum(["REGISTERED", "ATTENDED", "NO_SHOW", "CANCELLED"])
          .optional(),
      })
    )
    .handler(async ({ input, context }) => {
      const { page, limit, skip } = getPaginationParams(input);
      const profile = await prisma.alumniProfile.findUnique({
        where: { userId: context.user!.id },
        select: { id: true },
      });
      if (!profile) throw new Error("Alumni profile not found");
      const where: any = { attendeeId: profile.id };
      if (input.status) where.status = input.status;
      const [data, total] = await Promise.all([
        prisma.eventRegistration.findMany({ where, skip, take: limit, include: { event: true } }),
        prisma.eventRegistration.count({ where }),
      ]);
      return createPaginationResult(data, total, page, limit);
    }),

  // Admin endpoints for event management
  admin: {
    /**
     * Create new event (admin only)
     */
    create: protectedProcedure
      .input(createEventSchema)
      .handler(async ({ input, context }) => {
        const orgId = context.session?.session.activeOrganizationId;
        if (!orgId) throw new Error("Organization membership required");
        return prisma.event.create({
          data: { ...input, organizationId: orgId },
        });
      }),

    /**
     * Update event (admin only)
     */
    update: protectedProcedure
      .input(z.object({ id: idSchema }).merge(updateEventSchema))
      .handler(async ({ input, context }) => {
        const { id, ...updateData } = input;
        return prisma.event.update({ where: { id }, data: updateData });
      }),

    /**
     * Delete event (admin only)
     */
    delete: protectedProcedure
      .input(z.object({ id: idSchema }))
      .handler(async ({ input, context }) => {
        await prisma.event.delete({ where: { id: input.id } });
        return { success: true };
      }),

    /**
     * Get event registrations (admin only)
     */
    getRegistrations: protectedProcedure
      .input(
        z.object({
          eventId: idSchema,
          page: z.number().int().min(1).default(1),
          limit: z.number().int().min(1).max(100).default(20),
          status: z
            .enum(["REGISTERED", "ATTENDED", "NO_SHOW", "CANCELLED"])
            .optional(),
        })
      )
      .handler(async ({ input }) => {
        const { page, limit, skip } = getPaginationParams(input);
        const where: any = { eventId: input.eventId };
        if (input.status) where.status = input.status;
        const [data, total] = await Promise.all([
          prisma.eventRegistration.findMany({ where, skip, take: limit, include: { attendee: true } }),
          prisma.eventRegistration.count({ where }),
        ]);
        return createPaginationResult(data, total, page, limit);
      }),

    /**
     * Update attendee status (admin only)
     */
    updateAttendeeStatus: protectedProcedure
      .input(
        z.object({
          eventId: idSchema,
          attendeeUserId: idSchema,
          status: z.enum(["REGISTERED", "ATTENDED", "NO_SHOW", "CANCELLED"]),
        })
      )
      .handler(async ({ input }) => {
        const attendee = await prisma.alumniProfile.findUnique({
          where: { userId: input.attendeeUserId },
          select: { id: true },
        });
        if (!attendee) throw new Error("Attendee not found");
        const registration = await prisma.eventRegistration.findUnique({
          where: { eventId_attendeeId: { eventId: input.eventId, attendeeId: attendee.id } },
        });
        if (!registration) throw new Error("Registration not found");
        return prisma.eventRegistration.update({
          where: { id: registration.id },
          data: { status: input.status },
        });
      }),

    /**
     * Publish/unpublish event (admin only)
     */
    togglePublication: protectedProcedure
      .input(
        z.object({
          eventId: idSchema,
          isPublished: z.boolean(),
        })
      )
      .handler(async ({ input }) => {
        return prisma.event.update({
          where: { id: input.eventId },
          data: { isPublished: input.isPublished },
        });
      }),
  },
};