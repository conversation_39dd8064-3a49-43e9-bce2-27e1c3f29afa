# Technology Stack

## Build System & Package Management
- **Turborepo**: Monorepo build system with caching and parallel execution
- **pnpm**: Package manager (v9.11.0)
- **Biome**: Linting, formatting, and code quality

## Core Technologies
- **TypeScript**: Primary language across all applications
- **Node.js**: Runtime environment
- **React 19**: UI library for web and native

## Frontend Stack
### Web (Next.js)
- **Next.js 15.3**: Full-stack React framework with App Router
- **TailwindCSS v4**: Utility-first CSS framework
- **shadcn/ui**: Component library built on Radix UI
- **Tanstack Query**: Data fetching and caching
- **Tanstack Form**: Form management
- **next-themes**: Theme management
- **Sonner**: Toast notifications

### Mobile (React Native/Expo)
- **Expo 53**: React Native development platform
- **React Native 0.79**: Mobile framework
- **NativeWind**: TailwindCSS for React Native
- **Expo Router**: File-based routing

## Backend Stack
- **Hono**: Lightweight web framework
- **oRPC**: Type-safe RPC with OpenAPI integration
- **Prisma**: TypeScript-first ORM
- **MySQL**: Database engine
- **Better Auth**: Authentication system
- **AI SDK**: AI integration capabilities

## Development Tools
- **tsx**: TypeScript execution for development
- **tsdown**: TypeScript bundler for production

## Common Commands

### Development
```bash
pnpm dev              # Start all apps
pnpm dev:web          # Web app only
pnpm dev:server       # Server only  
pnpm dev:native       # Mobile app only
```

### Building & Type Checking
```bash
pnpm build            # Build all apps
pnpm check-types      # TypeScript validation
pnpm check            # Biome linting/formatting
```

### Database Operations
```bash
pnpm db:push          # Push schema to database
pnpm db:generate      # Generate Prisma client
pnpm db:migrate       # Run migrations
pnpm db:studio        # Open database UI
```

## Code Style
- **Indentation**: Tabs (configured in Biome)
- **Quotes**: Double quotes for JavaScript/TypeScript
- **Import organization**: Automatic with Biome
- **Class sorting**: Enabled for Tailwind classes (clsx, cva, cn functions)