
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `Donation` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model Donation
 * 
 */
export type DonationModel = runtime.Types.Result.DefaultSelection<Prisma.$DonationPayload>

export type AggregateDonation = {
  _count: DonationCountAggregateOutputType | null
  _avg: DonationAvgAggregateOutputType | null
  _sum: DonationSumAggregateOutputType | null
  _min: DonationMinAggregateOutputType | null
  _max: DonationMaxAggregateOutputType | null
}

export type DonationAvgAggregateOutputType = {
  amount: runtime.Decimal | null
}

export type DonationSumAggregateOutputType = {
  amount: runtime.Decimal | null
}

export type DonationMinAggregateOutputType = {
  id: string | null
  donorId: string | null
  amount: runtime.Decimal | null
  currency: string | null
  donationType: $Enums.DonationType | null
  organizationId: string | null
  paymentMethod: string | null
  transactionId: string | null
  paymentStatus: $Enums.PaymentStatus | null
  purpose: string | null
  isAnonymous: boolean | null
  recurringId: string | null
  nextDueDate: Date | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type DonationMaxAggregateOutputType = {
  id: string | null
  donorId: string | null
  amount: runtime.Decimal | null
  currency: string | null
  donationType: $Enums.DonationType | null
  organizationId: string | null
  paymentMethod: string | null
  transactionId: string | null
  paymentStatus: $Enums.PaymentStatus | null
  purpose: string | null
  isAnonymous: boolean | null
  recurringId: string | null
  nextDueDate: Date | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type DonationCountAggregateOutputType = {
  id: number
  donorId: number
  amount: number
  currency: number
  donationType: number
  organizationId: number
  paymentMethod: number
  transactionId: number
  paymentStatus: number
  purpose: number
  isAnonymous: number
  recurringId: number
  nextDueDate: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type DonationAvgAggregateInputType = {
  amount?: true
}

export type DonationSumAggregateInputType = {
  amount?: true
}

export type DonationMinAggregateInputType = {
  id?: true
  donorId?: true
  amount?: true
  currency?: true
  donationType?: true
  organizationId?: true
  paymentMethod?: true
  transactionId?: true
  paymentStatus?: true
  purpose?: true
  isAnonymous?: true
  recurringId?: true
  nextDueDate?: true
  createdAt?: true
  updatedAt?: true
}

export type DonationMaxAggregateInputType = {
  id?: true
  donorId?: true
  amount?: true
  currency?: true
  donationType?: true
  organizationId?: true
  paymentMethod?: true
  transactionId?: true
  paymentStatus?: true
  purpose?: true
  isAnonymous?: true
  recurringId?: true
  nextDueDate?: true
  createdAt?: true
  updatedAt?: true
}

export type DonationCountAggregateInputType = {
  id?: true
  donorId?: true
  amount?: true
  currency?: true
  donationType?: true
  organizationId?: true
  paymentMethod?: true
  transactionId?: true
  paymentStatus?: true
  purpose?: true
  isAnonymous?: true
  recurringId?: true
  nextDueDate?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type DonationAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Donation to aggregate.
   */
  where?: Prisma.DonationWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Donations to fetch.
   */
  orderBy?: Prisma.DonationOrderByWithRelationInput | Prisma.DonationOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.DonationWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Donations from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Donations.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Donations
  **/
  _count?: true | DonationCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: DonationAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: DonationSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: DonationMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: DonationMaxAggregateInputType
}

export type GetDonationAggregateType<T extends DonationAggregateArgs> = {
      [P in keyof T & keyof AggregateDonation]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateDonation[P]>
    : Prisma.GetScalarType<T[P], AggregateDonation[P]>
}




export type DonationGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.DonationWhereInput
  orderBy?: Prisma.DonationOrderByWithAggregationInput | Prisma.DonationOrderByWithAggregationInput[]
  by: Prisma.DonationScalarFieldEnum[] | Prisma.DonationScalarFieldEnum
  having?: Prisma.DonationScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: DonationCountAggregateInputType | true
  _avg?: DonationAvgAggregateInputType
  _sum?: DonationSumAggregateInputType
  _min?: DonationMinAggregateInputType
  _max?: DonationMaxAggregateInputType
}

export type DonationGroupByOutputType = {
  id: string
  donorId: string
  amount: runtime.Decimal
  currency: string
  donationType: $Enums.DonationType
  organizationId: string
  paymentMethod: string | null
  transactionId: string | null
  paymentStatus: $Enums.PaymentStatus
  purpose: string | null
  isAnonymous: boolean
  recurringId: string | null
  nextDueDate: Date | null
  createdAt: Date
  updatedAt: Date
  _count: DonationCountAggregateOutputType | null
  _avg: DonationAvgAggregateOutputType | null
  _sum: DonationSumAggregateOutputType | null
  _min: DonationMinAggregateOutputType | null
  _max: DonationMaxAggregateOutputType | null
}

type GetDonationGroupByPayload<T extends DonationGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<DonationGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof DonationGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], DonationGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], DonationGroupByOutputType[P]>
      }
    >
  >



export type DonationWhereInput = {
  AND?: Prisma.DonationWhereInput | Prisma.DonationWhereInput[]
  OR?: Prisma.DonationWhereInput[]
  NOT?: Prisma.DonationWhereInput | Prisma.DonationWhereInput[]
  id?: Prisma.StringFilter<"Donation"> | string
  donorId?: Prisma.StringFilter<"Donation"> | string
  amount?: Prisma.DecimalFilter<"Donation"> | runtime.Decimal | runtime.DecimalJsLike | number | string
  currency?: Prisma.StringFilter<"Donation"> | string
  donationType?: Prisma.EnumDonationTypeFilter<"Donation"> | $Enums.DonationType
  organizationId?: Prisma.StringFilter<"Donation"> | string
  paymentMethod?: Prisma.StringNullableFilter<"Donation"> | string | null
  transactionId?: Prisma.StringNullableFilter<"Donation"> | string | null
  paymentStatus?: Prisma.EnumPaymentStatusFilter<"Donation"> | $Enums.PaymentStatus
  purpose?: Prisma.StringNullableFilter<"Donation"> | string | null
  isAnonymous?: Prisma.BoolFilter<"Donation"> | boolean
  recurringId?: Prisma.StringNullableFilter<"Donation"> | string | null
  nextDueDate?: Prisma.DateTimeNullableFilter<"Donation"> | Date | string | null
  createdAt?: Prisma.DateTimeFilter<"Donation"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Donation"> | Date | string
  donor?: Prisma.XOR<Prisma.AlumniProfileScalarRelationFilter, Prisma.AlumniProfileWhereInput>
  organization?: Prisma.XOR<Prisma.OrganizationScalarRelationFilter, Prisma.OrganizationWhereInput>
}

export type DonationOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  donorId?: Prisma.SortOrder
  amount?: Prisma.SortOrder
  currency?: Prisma.SortOrder
  donationType?: Prisma.SortOrder
  organizationId?: Prisma.SortOrder
  paymentMethod?: Prisma.SortOrderInput | Prisma.SortOrder
  transactionId?: Prisma.SortOrderInput | Prisma.SortOrder
  paymentStatus?: Prisma.SortOrder
  purpose?: Prisma.SortOrderInput | Prisma.SortOrder
  isAnonymous?: Prisma.SortOrder
  recurringId?: Prisma.SortOrderInput | Prisma.SortOrder
  nextDueDate?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  donor?: Prisma.AlumniProfileOrderByWithRelationInput
  organization?: Prisma.OrganizationOrderByWithRelationInput
  _relevance?: Prisma.DonationOrderByRelevanceInput
}

export type DonationWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  transactionId?: string
  AND?: Prisma.DonationWhereInput | Prisma.DonationWhereInput[]
  OR?: Prisma.DonationWhereInput[]
  NOT?: Prisma.DonationWhereInput | Prisma.DonationWhereInput[]
  donorId?: Prisma.StringFilter<"Donation"> | string
  amount?: Prisma.DecimalFilter<"Donation"> | runtime.Decimal | runtime.DecimalJsLike | number | string
  currency?: Prisma.StringFilter<"Donation"> | string
  donationType?: Prisma.EnumDonationTypeFilter<"Donation"> | $Enums.DonationType
  organizationId?: Prisma.StringFilter<"Donation"> | string
  paymentMethod?: Prisma.StringNullableFilter<"Donation"> | string | null
  paymentStatus?: Prisma.EnumPaymentStatusFilter<"Donation"> | $Enums.PaymentStatus
  purpose?: Prisma.StringNullableFilter<"Donation"> | string | null
  isAnonymous?: Prisma.BoolFilter<"Donation"> | boolean
  recurringId?: Prisma.StringNullableFilter<"Donation"> | string | null
  nextDueDate?: Prisma.DateTimeNullableFilter<"Donation"> | Date | string | null
  createdAt?: Prisma.DateTimeFilter<"Donation"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Donation"> | Date | string
  donor?: Prisma.XOR<Prisma.AlumniProfileScalarRelationFilter, Prisma.AlumniProfileWhereInput>
  organization?: Prisma.XOR<Prisma.OrganizationScalarRelationFilter, Prisma.OrganizationWhereInput>
}, "id" | "transactionId">

export type DonationOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  donorId?: Prisma.SortOrder
  amount?: Prisma.SortOrder
  currency?: Prisma.SortOrder
  donationType?: Prisma.SortOrder
  organizationId?: Prisma.SortOrder
  paymentMethod?: Prisma.SortOrderInput | Prisma.SortOrder
  transactionId?: Prisma.SortOrderInput | Prisma.SortOrder
  paymentStatus?: Prisma.SortOrder
  purpose?: Prisma.SortOrderInput | Prisma.SortOrder
  isAnonymous?: Prisma.SortOrder
  recurringId?: Prisma.SortOrderInput | Prisma.SortOrder
  nextDueDate?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.DonationCountOrderByAggregateInput
  _avg?: Prisma.DonationAvgOrderByAggregateInput
  _max?: Prisma.DonationMaxOrderByAggregateInput
  _min?: Prisma.DonationMinOrderByAggregateInput
  _sum?: Prisma.DonationSumOrderByAggregateInput
}

export type DonationScalarWhereWithAggregatesInput = {
  AND?: Prisma.DonationScalarWhereWithAggregatesInput | Prisma.DonationScalarWhereWithAggregatesInput[]
  OR?: Prisma.DonationScalarWhereWithAggregatesInput[]
  NOT?: Prisma.DonationScalarWhereWithAggregatesInput | Prisma.DonationScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"Donation"> | string
  donorId?: Prisma.StringWithAggregatesFilter<"Donation"> | string
  amount?: Prisma.DecimalWithAggregatesFilter<"Donation"> | runtime.Decimal | runtime.DecimalJsLike | number | string
  currency?: Prisma.StringWithAggregatesFilter<"Donation"> | string
  donationType?: Prisma.EnumDonationTypeWithAggregatesFilter<"Donation"> | $Enums.DonationType
  organizationId?: Prisma.StringWithAggregatesFilter<"Donation"> | string
  paymentMethod?: Prisma.StringNullableWithAggregatesFilter<"Donation"> | string | null
  transactionId?: Prisma.StringNullableWithAggregatesFilter<"Donation"> | string | null
  paymentStatus?: Prisma.EnumPaymentStatusWithAggregatesFilter<"Donation"> | $Enums.PaymentStatus
  purpose?: Prisma.StringNullableWithAggregatesFilter<"Donation"> | string | null
  isAnonymous?: Prisma.BoolWithAggregatesFilter<"Donation"> | boolean
  recurringId?: Prisma.StringNullableWithAggregatesFilter<"Donation"> | string | null
  nextDueDate?: Prisma.DateTimeNullableWithAggregatesFilter<"Donation"> | Date | string | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"Donation"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"Donation"> | Date | string
}

export type DonationCreateInput = {
  id?: string
  amount: runtime.Decimal | runtime.DecimalJsLike | number | string
  currency?: string
  donationType?: $Enums.DonationType
  paymentMethod?: string | null
  transactionId?: string | null
  paymentStatus?: $Enums.PaymentStatus
  purpose?: string | null
  isAnonymous?: boolean
  recurringId?: string | null
  nextDueDate?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  donor: Prisma.AlumniProfileCreateNestedOneWithoutDonationsInput
  organization: Prisma.OrganizationCreateNestedOneWithoutDonationsInput
}

export type DonationUncheckedCreateInput = {
  id?: string
  donorId: string
  amount: runtime.Decimal | runtime.DecimalJsLike | number | string
  currency?: string
  donationType?: $Enums.DonationType
  organizationId: string
  paymentMethod?: string | null
  transactionId?: string | null
  paymentStatus?: $Enums.PaymentStatus
  purpose?: string | null
  isAnonymous?: boolean
  recurringId?: string | null
  nextDueDate?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type DonationUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  amount?: Prisma.DecimalFieldUpdateOperationsInput | runtime.Decimal | runtime.DecimalJsLike | number | string
  currency?: Prisma.StringFieldUpdateOperationsInput | string
  donationType?: Prisma.EnumDonationTypeFieldUpdateOperationsInput | $Enums.DonationType
  paymentMethod?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  transactionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  paymentStatus?: Prisma.EnumPaymentStatusFieldUpdateOperationsInput | $Enums.PaymentStatus
  purpose?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isAnonymous?: Prisma.BoolFieldUpdateOperationsInput | boolean
  recurringId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nextDueDate?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  donor?: Prisma.AlumniProfileUpdateOneRequiredWithoutDonationsNestedInput
  organization?: Prisma.OrganizationUpdateOneRequiredWithoutDonationsNestedInput
}

export type DonationUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  donorId?: Prisma.StringFieldUpdateOperationsInput | string
  amount?: Prisma.DecimalFieldUpdateOperationsInput | runtime.Decimal | runtime.DecimalJsLike | number | string
  currency?: Prisma.StringFieldUpdateOperationsInput | string
  donationType?: Prisma.EnumDonationTypeFieldUpdateOperationsInput | $Enums.DonationType
  organizationId?: Prisma.StringFieldUpdateOperationsInput | string
  paymentMethod?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  transactionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  paymentStatus?: Prisma.EnumPaymentStatusFieldUpdateOperationsInput | $Enums.PaymentStatus
  purpose?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isAnonymous?: Prisma.BoolFieldUpdateOperationsInput | boolean
  recurringId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nextDueDate?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type DonationCreateManyInput = {
  id?: string
  donorId: string
  amount: runtime.Decimal | runtime.DecimalJsLike | number | string
  currency?: string
  donationType?: $Enums.DonationType
  organizationId: string
  paymentMethod?: string | null
  transactionId?: string | null
  paymentStatus?: $Enums.PaymentStatus
  purpose?: string | null
  isAnonymous?: boolean
  recurringId?: string | null
  nextDueDate?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type DonationUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  amount?: Prisma.DecimalFieldUpdateOperationsInput | runtime.Decimal | runtime.DecimalJsLike | number | string
  currency?: Prisma.StringFieldUpdateOperationsInput | string
  donationType?: Prisma.EnumDonationTypeFieldUpdateOperationsInput | $Enums.DonationType
  paymentMethod?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  transactionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  paymentStatus?: Prisma.EnumPaymentStatusFieldUpdateOperationsInput | $Enums.PaymentStatus
  purpose?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isAnonymous?: Prisma.BoolFieldUpdateOperationsInput | boolean
  recurringId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nextDueDate?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type DonationUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  donorId?: Prisma.StringFieldUpdateOperationsInput | string
  amount?: Prisma.DecimalFieldUpdateOperationsInput | runtime.Decimal | runtime.DecimalJsLike | number | string
  currency?: Prisma.StringFieldUpdateOperationsInput | string
  donationType?: Prisma.EnumDonationTypeFieldUpdateOperationsInput | $Enums.DonationType
  organizationId?: Prisma.StringFieldUpdateOperationsInput | string
  paymentMethod?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  transactionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  paymentStatus?: Prisma.EnumPaymentStatusFieldUpdateOperationsInput | $Enums.PaymentStatus
  purpose?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isAnonymous?: Prisma.BoolFieldUpdateOperationsInput | boolean
  recurringId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nextDueDate?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type DonationListRelationFilter = {
  every?: Prisma.DonationWhereInput
  some?: Prisma.DonationWhereInput
  none?: Prisma.DonationWhereInput
}

export type DonationOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type DonationOrderByRelevanceInput = {
  fields: Prisma.DonationOrderByRelevanceFieldEnum | Prisma.DonationOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type DonationCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  donorId?: Prisma.SortOrder
  amount?: Prisma.SortOrder
  currency?: Prisma.SortOrder
  donationType?: Prisma.SortOrder
  organizationId?: Prisma.SortOrder
  paymentMethod?: Prisma.SortOrder
  transactionId?: Prisma.SortOrder
  paymentStatus?: Prisma.SortOrder
  purpose?: Prisma.SortOrder
  isAnonymous?: Prisma.SortOrder
  recurringId?: Prisma.SortOrder
  nextDueDate?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type DonationAvgOrderByAggregateInput = {
  amount?: Prisma.SortOrder
}

export type DonationMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  donorId?: Prisma.SortOrder
  amount?: Prisma.SortOrder
  currency?: Prisma.SortOrder
  donationType?: Prisma.SortOrder
  organizationId?: Prisma.SortOrder
  paymentMethod?: Prisma.SortOrder
  transactionId?: Prisma.SortOrder
  paymentStatus?: Prisma.SortOrder
  purpose?: Prisma.SortOrder
  isAnonymous?: Prisma.SortOrder
  recurringId?: Prisma.SortOrder
  nextDueDate?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type DonationMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  donorId?: Prisma.SortOrder
  amount?: Prisma.SortOrder
  currency?: Prisma.SortOrder
  donationType?: Prisma.SortOrder
  organizationId?: Prisma.SortOrder
  paymentMethod?: Prisma.SortOrder
  transactionId?: Prisma.SortOrder
  paymentStatus?: Prisma.SortOrder
  purpose?: Prisma.SortOrder
  isAnonymous?: Prisma.SortOrder
  recurringId?: Prisma.SortOrder
  nextDueDate?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type DonationSumOrderByAggregateInput = {
  amount?: Prisma.SortOrder
}

export type DonationCreateNestedManyWithoutOrganizationInput = {
  create?: Prisma.XOR<Prisma.DonationCreateWithoutOrganizationInput, Prisma.DonationUncheckedCreateWithoutOrganizationInput> | Prisma.DonationCreateWithoutOrganizationInput[] | Prisma.DonationUncheckedCreateWithoutOrganizationInput[]
  connectOrCreate?: Prisma.DonationCreateOrConnectWithoutOrganizationInput | Prisma.DonationCreateOrConnectWithoutOrganizationInput[]
  createMany?: Prisma.DonationCreateManyOrganizationInputEnvelope
  connect?: Prisma.DonationWhereUniqueInput | Prisma.DonationWhereUniqueInput[]
}

export type DonationUncheckedCreateNestedManyWithoutOrganizationInput = {
  create?: Prisma.XOR<Prisma.DonationCreateWithoutOrganizationInput, Prisma.DonationUncheckedCreateWithoutOrganizationInput> | Prisma.DonationCreateWithoutOrganizationInput[] | Prisma.DonationUncheckedCreateWithoutOrganizationInput[]
  connectOrCreate?: Prisma.DonationCreateOrConnectWithoutOrganizationInput | Prisma.DonationCreateOrConnectWithoutOrganizationInput[]
  createMany?: Prisma.DonationCreateManyOrganizationInputEnvelope
  connect?: Prisma.DonationWhereUniqueInput | Prisma.DonationWhereUniqueInput[]
}

export type DonationUpdateManyWithoutOrganizationNestedInput = {
  create?: Prisma.XOR<Prisma.DonationCreateWithoutOrganizationInput, Prisma.DonationUncheckedCreateWithoutOrganizationInput> | Prisma.DonationCreateWithoutOrganizationInput[] | Prisma.DonationUncheckedCreateWithoutOrganizationInput[]
  connectOrCreate?: Prisma.DonationCreateOrConnectWithoutOrganizationInput | Prisma.DonationCreateOrConnectWithoutOrganizationInput[]
  upsert?: Prisma.DonationUpsertWithWhereUniqueWithoutOrganizationInput | Prisma.DonationUpsertWithWhereUniqueWithoutOrganizationInput[]
  createMany?: Prisma.DonationCreateManyOrganizationInputEnvelope
  set?: Prisma.DonationWhereUniqueInput | Prisma.DonationWhereUniqueInput[]
  disconnect?: Prisma.DonationWhereUniqueInput | Prisma.DonationWhereUniqueInput[]
  delete?: Prisma.DonationWhereUniqueInput | Prisma.DonationWhereUniqueInput[]
  connect?: Prisma.DonationWhereUniqueInput | Prisma.DonationWhereUniqueInput[]
  update?: Prisma.DonationUpdateWithWhereUniqueWithoutOrganizationInput | Prisma.DonationUpdateWithWhereUniqueWithoutOrganizationInput[]
  updateMany?: Prisma.DonationUpdateManyWithWhereWithoutOrganizationInput | Prisma.DonationUpdateManyWithWhereWithoutOrganizationInput[]
  deleteMany?: Prisma.DonationScalarWhereInput | Prisma.DonationScalarWhereInput[]
}

export type DonationUncheckedUpdateManyWithoutOrganizationNestedInput = {
  create?: Prisma.XOR<Prisma.DonationCreateWithoutOrganizationInput, Prisma.DonationUncheckedCreateWithoutOrganizationInput> | Prisma.DonationCreateWithoutOrganizationInput[] | Prisma.DonationUncheckedCreateWithoutOrganizationInput[]
  connectOrCreate?: Prisma.DonationCreateOrConnectWithoutOrganizationInput | Prisma.DonationCreateOrConnectWithoutOrganizationInput[]
  upsert?: Prisma.DonationUpsertWithWhereUniqueWithoutOrganizationInput | Prisma.DonationUpsertWithWhereUniqueWithoutOrganizationInput[]
  createMany?: Prisma.DonationCreateManyOrganizationInputEnvelope
  set?: Prisma.DonationWhereUniqueInput | Prisma.DonationWhereUniqueInput[]
  disconnect?: Prisma.DonationWhereUniqueInput | Prisma.DonationWhereUniqueInput[]
  delete?: Prisma.DonationWhereUniqueInput | Prisma.DonationWhereUniqueInput[]
  connect?: Prisma.DonationWhereUniqueInput | Prisma.DonationWhereUniqueInput[]
  update?: Prisma.DonationUpdateWithWhereUniqueWithoutOrganizationInput | Prisma.DonationUpdateWithWhereUniqueWithoutOrganizationInput[]
  updateMany?: Prisma.DonationUpdateManyWithWhereWithoutOrganizationInput | Prisma.DonationUpdateManyWithWhereWithoutOrganizationInput[]
  deleteMany?: Prisma.DonationScalarWhereInput | Prisma.DonationScalarWhereInput[]
}

export type DonationCreateNestedManyWithoutDonorInput = {
  create?: Prisma.XOR<Prisma.DonationCreateWithoutDonorInput, Prisma.DonationUncheckedCreateWithoutDonorInput> | Prisma.DonationCreateWithoutDonorInput[] | Prisma.DonationUncheckedCreateWithoutDonorInput[]
  connectOrCreate?: Prisma.DonationCreateOrConnectWithoutDonorInput | Prisma.DonationCreateOrConnectWithoutDonorInput[]
  createMany?: Prisma.DonationCreateManyDonorInputEnvelope
  connect?: Prisma.DonationWhereUniqueInput | Prisma.DonationWhereUniqueInput[]
}

export type DonationUncheckedCreateNestedManyWithoutDonorInput = {
  create?: Prisma.XOR<Prisma.DonationCreateWithoutDonorInput, Prisma.DonationUncheckedCreateWithoutDonorInput> | Prisma.DonationCreateWithoutDonorInput[] | Prisma.DonationUncheckedCreateWithoutDonorInput[]
  connectOrCreate?: Prisma.DonationCreateOrConnectWithoutDonorInput | Prisma.DonationCreateOrConnectWithoutDonorInput[]
  createMany?: Prisma.DonationCreateManyDonorInputEnvelope
  connect?: Prisma.DonationWhereUniqueInput | Prisma.DonationWhereUniqueInput[]
}

export type DonationUpdateManyWithoutDonorNestedInput = {
  create?: Prisma.XOR<Prisma.DonationCreateWithoutDonorInput, Prisma.DonationUncheckedCreateWithoutDonorInput> | Prisma.DonationCreateWithoutDonorInput[] | Prisma.DonationUncheckedCreateWithoutDonorInput[]
  connectOrCreate?: Prisma.DonationCreateOrConnectWithoutDonorInput | Prisma.DonationCreateOrConnectWithoutDonorInput[]
  upsert?: Prisma.DonationUpsertWithWhereUniqueWithoutDonorInput | Prisma.DonationUpsertWithWhereUniqueWithoutDonorInput[]
  createMany?: Prisma.DonationCreateManyDonorInputEnvelope
  set?: Prisma.DonationWhereUniqueInput | Prisma.DonationWhereUniqueInput[]
  disconnect?: Prisma.DonationWhereUniqueInput | Prisma.DonationWhereUniqueInput[]
  delete?: Prisma.DonationWhereUniqueInput | Prisma.DonationWhereUniqueInput[]
  connect?: Prisma.DonationWhereUniqueInput | Prisma.DonationWhereUniqueInput[]
  update?: Prisma.DonationUpdateWithWhereUniqueWithoutDonorInput | Prisma.DonationUpdateWithWhereUniqueWithoutDonorInput[]
  updateMany?: Prisma.DonationUpdateManyWithWhereWithoutDonorInput | Prisma.DonationUpdateManyWithWhereWithoutDonorInput[]
  deleteMany?: Prisma.DonationScalarWhereInput | Prisma.DonationScalarWhereInput[]
}

export type DonationUncheckedUpdateManyWithoutDonorNestedInput = {
  create?: Prisma.XOR<Prisma.DonationCreateWithoutDonorInput, Prisma.DonationUncheckedCreateWithoutDonorInput> | Prisma.DonationCreateWithoutDonorInput[] | Prisma.DonationUncheckedCreateWithoutDonorInput[]
  connectOrCreate?: Prisma.DonationCreateOrConnectWithoutDonorInput | Prisma.DonationCreateOrConnectWithoutDonorInput[]
  upsert?: Prisma.DonationUpsertWithWhereUniqueWithoutDonorInput | Prisma.DonationUpsertWithWhereUniqueWithoutDonorInput[]
  createMany?: Prisma.DonationCreateManyDonorInputEnvelope
  set?: Prisma.DonationWhereUniqueInput | Prisma.DonationWhereUniqueInput[]
  disconnect?: Prisma.DonationWhereUniqueInput | Prisma.DonationWhereUniqueInput[]
  delete?: Prisma.DonationWhereUniqueInput | Prisma.DonationWhereUniqueInput[]
  connect?: Prisma.DonationWhereUniqueInput | Prisma.DonationWhereUniqueInput[]
  update?: Prisma.DonationUpdateWithWhereUniqueWithoutDonorInput | Prisma.DonationUpdateWithWhereUniqueWithoutDonorInput[]
  updateMany?: Prisma.DonationUpdateManyWithWhereWithoutDonorInput | Prisma.DonationUpdateManyWithWhereWithoutDonorInput[]
  deleteMany?: Prisma.DonationScalarWhereInput | Prisma.DonationScalarWhereInput[]
}

export type DecimalFieldUpdateOperationsInput = {
  set?: runtime.Decimal | runtime.DecimalJsLike | number | string
  increment?: runtime.Decimal | runtime.DecimalJsLike | number | string
  decrement?: runtime.Decimal | runtime.DecimalJsLike | number | string
  multiply?: runtime.Decimal | runtime.DecimalJsLike | number | string
  divide?: runtime.Decimal | runtime.DecimalJsLike | number | string
}

export type EnumDonationTypeFieldUpdateOperationsInput = {
  set?: $Enums.DonationType
}

export type EnumPaymentStatusFieldUpdateOperationsInput = {
  set?: $Enums.PaymentStatus
}

export type DonationCreateWithoutOrganizationInput = {
  id?: string
  amount: runtime.Decimal | runtime.DecimalJsLike | number | string
  currency?: string
  donationType?: $Enums.DonationType
  paymentMethod?: string | null
  transactionId?: string | null
  paymentStatus?: $Enums.PaymentStatus
  purpose?: string | null
  isAnonymous?: boolean
  recurringId?: string | null
  nextDueDate?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  donor: Prisma.AlumniProfileCreateNestedOneWithoutDonationsInput
}

export type DonationUncheckedCreateWithoutOrganizationInput = {
  id?: string
  donorId: string
  amount: runtime.Decimal | runtime.DecimalJsLike | number | string
  currency?: string
  donationType?: $Enums.DonationType
  paymentMethod?: string | null
  transactionId?: string | null
  paymentStatus?: $Enums.PaymentStatus
  purpose?: string | null
  isAnonymous?: boolean
  recurringId?: string | null
  nextDueDate?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type DonationCreateOrConnectWithoutOrganizationInput = {
  where: Prisma.DonationWhereUniqueInput
  create: Prisma.XOR<Prisma.DonationCreateWithoutOrganizationInput, Prisma.DonationUncheckedCreateWithoutOrganizationInput>
}

export type DonationCreateManyOrganizationInputEnvelope = {
  data: Prisma.DonationCreateManyOrganizationInput | Prisma.DonationCreateManyOrganizationInput[]
  skipDuplicates?: boolean
}

export type DonationUpsertWithWhereUniqueWithoutOrganizationInput = {
  where: Prisma.DonationWhereUniqueInput
  update: Prisma.XOR<Prisma.DonationUpdateWithoutOrganizationInput, Prisma.DonationUncheckedUpdateWithoutOrganizationInput>
  create: Prisma.XOR<Prisma.DonationCreateWithoutOrganizationInput, Prisma.DonationUncheckedCreateWithoutOrganizationInput>
}

export type DonationUpdateWithWhereUniqueWithoutOrganizationInput = {
  where: Prisma.DonationWhereUniqueInput
  data: Prisma.XOR<Prisma.DonationUpdateWithoutOrganizationInput, Prisma.DonationUncheckedUpdateWithoutOrganizationInput>
}

export type DonationUpdateManyWithWhereWithoutOrganizationInput = {
  where: Prisma.DonationScalarWhereInput
  data: Prisma.XOR<Prisma.DonationUpdateManyMutationInput, Prisma.DonationUncheckedUpdateManyWithoutOrganizationInput>
}

export type DonationScalarWhereInput = {
  AND?: Prisma.DonationScalarWhereInput | Prisma.DonationScalarWhereInput[]
  OR?: Prisma.DonationScalarWhereInput[]
  NOT?: Prisma.DonationScalarWhereInput | Prisma.DonationScalarWhereInput[]
  id?: Prisma.StringFilter<"Donation"> | string
  donorId?: Prisma.StringFilter<"Donation"> | string
  amount?: Prisma.DecimalFilter<"Donation"> | runtime.Decimal | runtime.DecimalJsLike | number | string
  currency?: Prisma.StringFilter<"Donation"> | string
  donationType?: Prisma.EnumDonationTypeFilter<"Donation"> | $Enums.DonationType
  organizationId?: Prisma.StringFilter<"Donation"> | string
  paymentMethod?: Prisma.StringNullableFilter<"Donation"> | string | null
  transactionId?: Prisma.StringNullableFilter<"Donation"> | string | null
  paymentStatus?: Prisma.EnumPaymentStatusFilter<"Donation"> | $Enums.PaymentStatus
  purpose?: Prisma.StringNullableFilter<"Donation"> | string | null
  isAnonymous?: Prisma.BoolFilter<"Donation"> | boolean
  recurringId?: Prisma.StringNullableFilter<"Donation"> | string | null
  nextDueDate?: Prisma.DateTimeNullableFilter<"Donation"> | Date | string | null
  createdAt?: Prisma.DateTimeFilter<"Donation"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Donation"> | Date | string
}

export type DonationCreateWithoutDonorInput = {
  id?: string
  amount: runtime.Decimal | runtime.DecimalJsLike | number | string
  currency?: string
  donationType?: $Enums.DonationType
  paymentMethod?: string | null
  transactionId?: string | null
  paymentStatus?: $Enums.PaymentStatus
  purpose?: string | null
  isAnonymous?: boolean
  recurringId?: string | null
  nextDueDate?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  organization: Prisma.OrganizationCreateNestedOneWithoutDonationsInput
}

export type DonationUncheckedCreateWithoutDonorInput = {
  id?: string
  amount: runtime.Decimal | runtime.DecimalJsLike | number | string
  currency?: string
  donationType?: $Enums.DonationType
  organizationId: string
  paymentMethod?: string | null
  transactionId?: string | null
  paymentStatus?: $Enums.PaymentStatus
  purpose?: string | null
  isAnonymous?: boolean
  recurringId?: string | null
  nextDueDate?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type DonationCreateOrConnectWithoutDonorInput = {
  where: Prisma.DonationWhereUniqueInput
  create: Prisma.XOR<Prisma.DonationCreateWithoutDonorInput, Prisma.DonationUncheckedCreateWithoutDonorInput>
}

export type DonationCreateManyDonorInputEnvelope = {
  data: Prisma.DonationCreateManyDonorInput | Prisma.DonationCreateManyDonorInput[]
  skipDuplicates?: boolean
}

export type DonationUpsertWithWhereUniqueWithoutDonorInput = {
  where: Prisma.DonationWhereUniqueInput
  update: Prisma.XOR<Prisma.DonationUpdateWithoutDonorInput, Prisma.DonationUncheckedUpdateWithoutDonorInput>
  create: Prisma.XOR<Prisma.DonationCreateWithoutDonorInput, Prisma.DonationUncheckedCreateWithoutDonorInput>
}

export type DonationUpdateWithWhereUniqueWithoutDonorInput = {
  where: Prisma.DonationWhereUniqueInput
  data: Prisma.XOR<Prisma.DonationUpdateWithoutDonorInput, Prisma.DonationUncheckedUpdateWithoutDonorInput>
}

export type DonationUpdateManyWithWhereWithoutDonorInput = {
  where: Prisma.DonationScalarWhereInput
  data: Prisma.XOR<Prisma.DonationUpdateManyMutationInput, Prisma.DonationUncheckedUpdateManyWithoutDonorInput>
}

export type DonationCreateManyOrganizationInput = {
  id?: string
  donorId: string
  amount: runtime.Decimal | runtime.DecimalJsLike | number | string
  currency?: string
  donationType?: $Enums.DonationType
  paymentMethod?: string | null
  transactionId?: string | null
  paymentStatus?: $Enums.PaymentStatus
  purpose?: string | null
  isAnonymous?: boolean
  recurringId?: string | null
  nextDueDate?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type DonationUpdateWithoutOrganizationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  amount?: Prisma.DecimalFieldUpdateOperationsInput | runtime.Decimal | runtime.DecimalJsLike | number | string
  currency?: Prisma.StringFieldUpdateOperationsInput | string
  donationType?: Prisma.EnumDonationTypeFieldUpdateOperationsInput | $Enums.DonationType
  paymentMethod?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  transactionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  paymentStatus?: Prisma.EnumPaymentStatusFieldUpdateOperationsInput | $Enums.PaymentStatus
  purpose?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isAnonymous?: Prisma.BoolFieldUpdateOperationsInput | boolean
  recurringId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nextDueDate?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  donor?: Prisma.AlumniProfileUpdateOneRequiredWithoutDonationsNestedInput
}

export type DonationUncheckedUpdateWithoutOrganizationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  donorId?: Prisma.StringFieldUpdateOperationsInput | string
  amount?: Prisma.DecimalFieldUpdateOperationsInput | runtime.Decimal | runtime.DecimalJsLike | number | string
  currency?: Prisma.StringFieldUpdateOperationsInput | string
  donationType?: Prisma.EnumDonationTypeFieldUpdateOperationsInput | $Enums.DonationType
  paymentMethod?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  transactionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  paymentStatus?: Prisma.EnumPaymentStatusFieldUpdateOperationsInput | $Enums.PaymentStatus
  purpose?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isAnonymous?: Prisma.BoolFieldUpdateOperationsInput | boolean
  recurringId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nextDueDate?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type DonationUncheckedUpdateManyWithoutOrganizationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  donorId?: Prisma.StringFieldUpdateOperationsInput | string
  amount?: Prisma.DecimalFieldUpdateOperationsInput | runtime.Decimal | runtime.DecimalJsLike | number | string
  currency?: Prisma.StringFieldUpdateOperationsInput | string
  donationType?: Prisma.EnumDonationTypeFieldUpdateOperationsInput | $Enums.DonationType
  paymentMethod?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  transactionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  paymentStatus?: Prisma.EnumPaymentStatusFieldUpdateOperationsInput | $Enums.PaymentStatus
  purpose?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isAnonymous?: Prisma.BoolFieldUpdateOperationsInput | boolean
  recurringId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nextDueDate?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type DonationCreateManyDonorInput = {
  id?: string
  amount: runtime.Decimal | runtime.DecimalJsLike | number | string
  currency?: string
  donationType?: $Enums.DonationType
  organizationId: string
  paymentMethod?: string | null
  transactionId?: string | null
  paymentStatus?: $Enums.PaymentStatus
  purpose?: string | null
  isAnonymous?: boolean
  recurringId?: string | null
  nextDueDate?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type DonationUpdateWithoutDonorInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  amount?: Prisma.DecimalFieldUpdateOperationsInput | runtime.Decimal | runtime.DecimalJsLike | number | string
  currency?: Prisma.StringFieldUpdateOperationsInput | string
  donationType?: Prisma.EnumDonationTypeFieldUpdateOperationsInput | $Enums.DonationType
  paymentMethod?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  transactionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  paymentStatus?: Prisma.EnumPaymentStatusFieldUpdateOperationsInput | $Enums.PaymentStatus
  purpose?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isAnonymous?: Prisma.BoolFieldUpdateOperationsInput | boolean
  recurringId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nextDueDate?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  organization?: Prisma.OrganizationUpdateOneRequiredWithoutDonationsNestedInput
}

export type DonationUncheckedUpdateWithoutDonorInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  amount?: Prisma.DecimalFieldUpdateOperationsInput | runtime.Decimal | runtime.DecimalJsLike | number | string
  currency?: Prisma.StringFieldUpdateOperationsInput | string
  donationType?: Prisma.EnumDonationTypeFieldUpdateOperationsInput | $Enums.DonationType
  organizationId?: Prisma.StringFieldUpdateOperationsInput | string
  paymentMethod?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  transactionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  paymentStatus?: Prisma.EnumPaymentStatusFieldUpdateOperationsInput | $Enums.PaymentStatus
  purpose?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isAnonymous?: Prisma.BoolFieldUpdateOperationsInput | boolean
  recurringId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nextDueDate?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type DonationUncheckedUpdateManyWithoutDonorInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  amount?: Prisma.DecimalFieldUpdateOperationsInput | runtime.Decimal | runtime.DecimalJsLike | number | string
  currency?: Prisma.StringFieldUpdateOperationsInput | string
  donationType?: Prisma.EnumDonationTypeFieldUpdateOperationsInput | $Enums.DonationType
  organizationId?: Prisma.StringFieldUpdateOperationsInput | string
  paymentMethod?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  transactionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  paymentStatus?: Prisma.EnumPaymentStatusFieldUpdateOperationsInput | $Enums.PaymentStatus
  purpose?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isAnonymous?: Prisma.BoolFieldUpdateOperationsInput | boolean
  recurringId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  nextDueDate?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}



export type DonationSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  donorId?: boolean
  amount?: boolean
  currency?: boolean
  donationType?: boolean
  organizationId?: boolean
  paymentMethod?: boolean
  transactionId?: boolean
  paymentStatus?: boolean
  purpose?: boolean
  isAnonymous?: boolean
  recurringId?: boolean
  nextDueDate?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  donor?: boolean | Prisma.AlumniProfileDefaultArgs<ExtArgs>
  organization?: boolean | Prisma.OrganizationDefaultArgs<ExtArgs>
}, ExtArgs["result"]["donation"]>



export type DonationSelectScalar = {
  id?: boolean
  donorId?: boolean
  amount?: boolean
  currency?: boolean
  donationType?: boolean
  organizationId?: boolean
  paymentMethod?: boolean
  transactionId?: boolean
  paymentStatus?: boolean
  purpose?: boolean
  isAnonymous?: boolean
  recurringId?: boolean
  nextDueDate?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type DonationOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "donorId" | "amount" | "currency" | "donationType" | "organizationId" | "paymentMethod" | "transactionId" | "paymentStatus" | "purpose" | "isAnonymous" | "recurringId" | "nextDueDate" | "createdAt" | "updatedAt", ExtArgs["result"]["donation"]>
export type DonationInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  donor?: boolean | Prisma.AlumniProfileDefaultArgs<ExtArgs>
  organization?: boolean | Prisma.OrganizationDefaultArgs<ExtArgs>
}

export type $DonationPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Donation"
  objects: {
    donor: Prisma.$AlumniProfilePayload<ExtArgs>
    organization: Prisma.$OrganizationPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    donorId: string
    amount: runtime.Decimal
    currency: string
    donationType: $Enums.DonationType
    organizationId: string
    paymentMethod: string | null
    transactionId: string | null
    paymentStatus: $Enums.PaymentStatus
    purpose: string | null
    isAnonymous: boolean
    recurringId: string | null
    nextDueDate: Date | null
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["donation"]>
  composites: {}
}

export type DonationGetPayload<S extends boolean | null | undefined | DonationDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$DonationPayload, S>

export type DonationCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<DonationFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: DonationCountAggregateInputType | true
  }

export interface DonationDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Donation'], meta: { name: 'Donation' } }
  /**
   * Find zero or one Donation that matches the filter.
   * @param {DonationFindUniqueArgs} args - Arguments to find a Donation
   * @example
   * // Get one Donation
   * const donation = await prisma.donation.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends DonationFindUniqueArgs>(args: Prisma.SelectSubset<T, DonationFindUniqueArgs<ExtArgs>>): Prisma.Prisma__DonationClient<runtime.Types.Result.GetResult<Prisma.$DonationPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Donation that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {DonationFindUniqueOrThrowArgs} args - Arguments to find a Donation
   * @example
   * // Get one Donation
   * const donation = await prisma.donation.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends DonationFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, DonationFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__DonationClient<runtime.Types.Result.GetResult<Prisma.$DonationPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Donation that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {DonationFindFirstArgs} args - Arguments to find a Donation
   * @example
   * // Get one Donation
   * const donation = await prisma.donation.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends DonationFindFirstArgs>(args?: Prisma.SelectSubset<T, DonationFindFirstArgs<ExtArgs>>): Prisma.Prisma__DonationClient<runtime.Types.Result.GetResult<Prisma.$DonationPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Donation that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {DonationFindFirstOrThrowArgs} args - Arguments to find a Donation
   * @example
   * // Get one Donation
   * const donation = await prisma.donation.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends DonationFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, DonationFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__DonationClient<runtime.Types.Result.GetResult<Prisma.$DonationPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Donations that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {DonationFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Donations
   * const donations = await prisma.donation.findMany()
   * 
   * // Get first 10 Donations
   * const donations = await prisma.donation.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const donationWithIdOnly = await prisma.donation.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends DonationFindManyArgs>(args?: Prisma.SelectSubset<T, DonationFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$DonationPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Donation.
   * @param {DonationCreateArgs} args - Arguments to create a Donation.
   * @example
   * // Create one Donation
   * const Donation = await prisma.donation.create({
   *   data: {
   *     // ... data to create a Donation
   *   }
   * })
   * 
   */
  create<T extends DonationCreateArgs>(args: Prisma.SelectSubset<T, DonationCreateArgs<ExtArgs>>): Prisma.Prisma__DonationClient<runtime.Types.Result.GetResult<Prisma.$DonationPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Donations.
   * @param {DonationCreateManyArgs} args - Arguments to create many Donations.
   * @example
   * // Create many Donations
   * const donation = await prisma.donation.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends DonationCreateManyArgs>(args?: Prisma.SelectSubset<T, DonationCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Donation.
   * @param {DonationDeleteArgs} args - Arguments to delete one Donation.
   * @example
   * // Delete one Donation
   * const Donation = await prisma.donation.delete({
   *   where: {
   *     // ... filter to delete one Donation
   *   }
   * })
   * 
   */
  delete<T extends DonationDeleteArgs>(args: Prisma.SelectSubset<T, DonationDeleteArgs<ExtArgs>>): Prisma.Prisma__DonationClient<runtime.Types.Result.GetResult<Prisma.$DonationPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Donation.
   * @param {DonationUpdateArgs} args - Arguments to update one Donation.
   * @example
   * // Update one Donation
   * const donation = await prisma.donation.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends DonationUpdateArgs>(args: Prisma.SelectSubset<T, DonationUpdateArgs<ExtArgs>>): Prisma.Prisma__DonationClient<runtime.Types.Result.GetResult<Prisma.$DonationPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Donations.
   * @param {DonationDeleteManyArgs} args - Arguments to filter Donations to delete.
   * @example
   * // Delete a few Donations
   * const { count } = await prisma.donation.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends DonationDeleteManyArgs>(args?: Prisma.SelectSubset<T, DonationDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Donations.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {DonationUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Donations
   * const donation = await prisma.donation.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends DonationUpdateManyArgs>(args: Prisma.SelectSubset<T, DonationUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Donation.
   * @param {DonationUpsertArgs} args - Arguments to update or create a Donation.
   * @example
   * // Update or create a Donation
   * const donation = await prisma.donation.upsert({
   *   create: {
   *     // ... data to create a Donation
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Donation we want to update
   *   }
   * })
   */
  upsert<T extends DonationUpsertArgs>(args: Prisma.SelectSubset<T, DonationUpsertArgs<ExtArgs>>): Prisma.Prisma__DonationClient<runtime.Types.Result.GetResult<Prisma.$DonationPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Donations.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {DonationCountArgs} args - Arguments to filter Donations to count.
   * @example
   * // Count the number of Donations
   * const count = await prisma.donation.count({
   *   where: {
   *     // ... the filter for the Donations we want to count
   *   }
   * })
  **/
  count<T extends DonationCountArgs>(
    args?: Prisma.Subset<T, DonationCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], DonationCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Donation.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {DonationAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends DonationAggregateArgs>(args: Prisma.Subset<T, DonationAggregateArgs>): Prisma.PrismaPromise<GetDonationAggregateType<T>>

  /**
   * Group by Donation.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {DonationGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends DonationGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: DonationGroupByArgs['orderBy'] }
      : { orderBy?: DonationGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, DonationGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetDonationGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Donation model
 */
readonly fields: DonationFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Donation.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__DonationClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  donor<T extends Prisma.AlumniProfileDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.AlumniProfileDefaultArgs<ExtArgs>>): Prisma.Prisma__AlumniProfileClient<runtime.Types.Result.GetResult<Prisma.$AlumniProfilePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  organization<T extends Prisma.OrganizationDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.OrganizationDefaultArgs<ExtArgs>>): Prisma.Prisma__OrganizationClient<runtime.Types.Result.GetResult<Prisma.$OrganizationPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Donation model
 */
export interface DonationFieldRefs {
  readonly id: Prisma.FieldRef<"Donation", 'String'>
  readonly donorId: Prisma.FieldRef<"Donation", 'String'>
  readonly amount: Prisma.FieldRef<"Donation", 'Decimal'>
  readonly currency: Prisma.FieldRef<"Donation", 'String'>
  readonly donationType: Prisma.FieldRef<"Donation", 'DonationType'>
  readonly organizationId: Prisma.FieldRef<"Donation", 'String'>
  readonly paymentMethod: Prisma.FieldRef<"Donation", 'String'>
  readonly transactionId: Prisma.FieldRef<"Donation", 'String'>
  readonly paymentStatus: Prisma.FieldRef<"Donation", 'PaymentStatus'>
  readonly purpose: Prisma.FieldRef<"Donation", 'String'>
  readonly isAnonymous: Prisma.FieldRef<"Donation", 'Boolean'>
  readonly recurringId: Prisma.FieldRef<"Donation", 'String'>
  readonly nextDueDate: Prisma.FieldRef<"Donation", 'DateTime'>
  readonly createdAt: Prisma.FieldRef<"Donation", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"Donation", 'DateTime'>
}
    

// Custom InputTypes
/**
 * Donation findUnique
 */
export type DonationFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Donation
   */
  select?: Prisma.DonationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Donation
   */
  omit?: Prisma.DonationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DonationInclude<ExtArgs> | null
  /**
   * Filter, which Donation to fetch.
   */
  where: Prisma.DonationWhereUniqueInput
}

/**
 * Donation findUniqueOrThrow
 */
export type DonationFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Donation
   */
  select?: Prisma.DonationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Donation
   */
  omit?: Prisma.DonationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DonationInclude<ExtArgs> | null
  /**
   * Filter, which Donation to fetch.
   */
  where: Prisma.DonationWhereUniqueInput
}

/**
 * Donation findFirst
 */
export type DonationFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Donation
   */
  select?: Prisma.DonationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Donation
   */
  omit?: Prisma.DonationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DonationInclude<ExtArgs> | null
  /**
   * Filter, which Donation to fetch.
   */
  where?: Prisma.DonationWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Donations to fetch.
   */
  orderBy?: Prisma.DonationOrderByWithRelationInput | Prisma.DonationOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Donations.
   */
  cursor?: Prisma.DonationWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Donations from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Donations.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Donations.
   */
  distinct?: Prisma.DonationScalarFieldEnum | Prisma.DonationScalarFieldEnum[]
}

/**
 * Donation findFirstOrThrow
 */
export type DonationFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Donation
   */
  select?: Prisma.DonationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Donation
   */
  omit?: Prisma.DonationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DonationInclude<ExtArgs> | null
  /**
   * Filter, which Donation to fetch.
   */
  where?: Prisma.DonationWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Donations to fetch.
   */
  orderBy?: Prisma.DonationOrderByWithRelationInput | Prisma.DonationOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Donations.
   */
  cursor?: Prisma.DonationWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Donations from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Donations.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Donations.
   */
  distinct?: Prisma.DonationScalarFieldEnum | Prisma.DonationScalarFieldEnum[]
}

/**
 * Donation findMany
 */
export type DonationFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Donation
   */
  select?: Prisma.DonationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Donation
   */
  omit?: Prisma.DonationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DonationInclude<ExtArgs> | null
  /**
   * Filter, which Donations to fetch.
   */
  where?: Prisma.DonationWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Donations to fetch.
   */
  orderBy?: Prisma.DonationOrderByWithRelationInput | Prisma.DonationOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Donations.
   */
  cursor?: Prisma.DonationWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Donations from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Donations.
   */
  skip?: number
  distinct?: Prisma.DonationScalarFieldEnum | Prisma.DonationScalarFieldEnum[]
}

/**
 * Donation create
 */
export type DonationCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Donation
   */
  select?: Prisma.DonationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Donation
   */
  omit?: Prisma.DonationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DonationInclude<ExtArgs> | null
  /**
   * The data needed to create a Donation.
   */
  data: Prisma.XOR<Prisma.DonationCreateInput, Prisma.DonationUncheckedCreateInput>
}

/**
 * Donation createMany
 */
export type DonationCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Donations.
   */
  data: Prisma.DonationCreateManyInput | Prisma.DonationCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Donation update
 */
export type DonationUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Donation
   */
  select?: Prisma.DonationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Donation
   */
  omit?: Prisma.DonationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DonationInclude<ExtArgs> | null
  /**
   * The data needed to update a Donation.
   */
  data: Prisma.XOR<Prisma.DonationUpdateInput, Prisma.DonationUncheckedUpdateInput>
  /**
   * Choose, which Donation to update.
   */
  where: Prisma.DonationWhereUniqueInput
}

/**
 * Donation updateMany
 */
export type DonationUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Donations.
   */
  data: Prisma.XOR<Prisma.DonationUpdateManyMutationInput, Prisma.DonationUncheckedUpdateManyInput>
  /**
   * Filter which Donations to update
   */
  where?: Prisma.DonationWhereInput
  /**
   * Limit how many Donations to update.
   */
  limit?: number
}

/**
 * Donation upsert
 */
export type DonationUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Donation
   */
  select?: Prisma.DonationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Donation
   */
  omit?: Prisma.DonationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DonationInclude<ExtArgs> | null
  /**
   * The filter to search for the Donation to update in case it exists.
   */
  where: Prisma.DonationWhereUniqueInput
  /**
   * In case the Donation found by the `where` argument doesn't exist, create a new Donation with this data.
   */
  create: Prisma.XOR<Prisma.DonationCreateInput, Prisma.DonationUncheckedCreateInput>
  /**
   * In case the Donation was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.DonationUpdateInput, Prisma.DonationUncheckedUpdateInput>
}

/**
 * Donation delete
 */
export type DonationDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Donation
   */
  select?: Prisma.DonationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Donation
   */
  omit?: Prisma.DonationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DonationInclude<ExtArgs> | null
  /**
   * Filter which Donation to delete.
   */
  where: Prisma.DonationWhereUniqueInput
}

/**
 * Donation deleteMany
 */
export type DonationDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Donations to delete
   */
  where?: Prisma.DonationWhereInput
  /**
   * Limit how many Donations to delete.
   */
  limit?: number
}

/**
 * Donation without action
 */
export type DonationDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Donation
   */
  select?: Prisma.DonationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Donation
   */
  omit?: Prisma.DonationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DonationInclude<ExtArgs> | null
}
