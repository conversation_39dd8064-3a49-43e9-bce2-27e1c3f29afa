
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `Event` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model Event
 * 
 */
export type EventModel = runtime.Types.Result.DefaultSelection<Prisma.$EventPayload>

export type AggregateEvent = {
  _count: EventCountAggregateOutputType | null
  _avg: EventAvgAggregateOutputType | null
  _sum: EventSumAggregateOutputType | null
  _min: EventMinAggregateOutputType | null
  _max: EventMaxAggregateOutputType | null
}

export type EventAvgAggregateOutputType = {
  maxAttendees: number | null
}

export type EventSumAggregateOutputType = {
  maxAttendees: number | null
}

export type EventMinAggregateOutputType = {
  id: string | null
  title: string | null
  description: string | null
  eventType: $Enums.EventType | null
  location: string | null
  isVirtual: boolean | null
  virtualLink: string | null
  startDateTime: Date | null
  endDateTime: Date | null
  maxAttendees: number | null
  imageUrl: string | null
  organizationId: string | null
  organizerId: string | null
  isPublished: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type EventMaxAggregateOutputType = {
  id: string | null
  title: string | null
  description: string | null
  eventType: $Enums.EventType | null
  location: string | null
  isVirtual: boolean | null
  virtualLink: string | null
  startDateTime: Date | null
  endDateTime: Date | null
  maxAttendees: number | null
  imageUrl: string | null
  organizationId: string | null
  organizerId: string | null
  isPublished: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type EventCountAggregateOutputType = {
  id: number
  title: number
  description: number
  eventType: number
  location: number
  isVirtual: number
  virtualLink: number
  startDateTime: number
  endDateTime: number
  maxAttendees: number
  imageUrl: number
  organizationId: number
  organizerId: number
  isPublished: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type EventAvgAggregateInputType = {
  maxAttendees?: true
}

export type EventSumAggregateInputType = {
  maxAttendees?: true
}

export type EventMinAggregateInputType = {
  id?: true
  title?: true
  description?: true
  eventType?: true
  location?: true
  isVirtual?: true
  virtualLink?: true
  startDateTime?: true
  endDateTime?: true
  maxAttendees?: true
  imageUrl?: true
  organizationId?: true
  organizerId?: true
  isPublished?: true
  createdAt?: true
  updatedAt?: true
}

export type EventMaxAggregateInputType = {
  id?: true
  title?: true
  description?: true
  eventType?: true
  location?: true
  isVirtual?: true
  virtualLink?: true
  startDateTime?: true
  endDateTime?: true
  maxAttendees?: true
  imageUrl?: true
  organizationId?: true
  organizerId?: true
  isPublished?: true
  createdAt?: true
  updatedAt?: true
}

export type EventCountAggregateInputType = {
  id?: true
  title?: true
  description?: true
  eventType?: true
  location?: true
  isVirtual?: true
  virtualLink?: true
  startDateTime?: true
  endDateTime?: true
  maxAttendees?: true
  imageUrl?: true
  organizationId?: true
  organizerId?: true
  isPublished?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type EventAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Event to aggregate.
   */
  where?: Prisma.EventWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Events to fetch.
   */
  orderBy?: Prisma.EventOrderByWithRelationInput | Prisma.EventOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.EventWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Events from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Events.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Events
  **/
  _count?: true | EventCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: EventAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: EventSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: EventMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: EventMaxAggregateInputType
}

export type GetEventAggregateType<T extends EventAggregateArgs> = {
      [P in keyof T & keyof AggregateEvent]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateEvent[P]>
    : Prisma.GetScalarType<T[P], AggregateEvent[P]>
}




export type EventGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.EventWhereInput
  orderBy?: Prisma.EventOrderByWithAggregationInput | Prisma.EventOrderByWithAggregationInput[]
  by: Prisma.EventScalarFieldEnum[] | Prisma.EventScalarFieldEnum
  having?: Prisma.EventScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: EventCountAggregateInputType | true
  _avg?: EventAvgAggregateInputType
  _sum?: EventSumAggregateInputType
  _min?: EventMinAggregateInputType
  _max?: EventMaxAggregateInputType
}

export type EventGroupByOutputType = {
  id: string
  title: string
  description: string
  eventType: $Enums.EventType
  location: string | null
  isVirtual: boolean
  virtualLink: string | null
  startDateTime: Date
  endDateTime: Date
  maxAttendees: number | null
  imageUrl: string | null
  organizationId: string | null
  organizerId: string | null
  isPublished: boolean
  createdAt: Date
  updatedAt: Date
  _count: EventCountAggregateOutputType | null
  _avg: EventAvgAggregateOutputType | null
  _sum: EventSumAggregateOutputType | null
  _min: EventMinAggregateOutputType | null
  _max: EventMaxAggregateOutputType | null
}

type GetEventGroupByPayload<T extends EventGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<EventGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof EventGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], EventGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], EventGroupByOutputType[P]>
      }
    >
  >



export type EventWhereInput = {
  AND?: Prisma.EventWhereInput | Prisma.EventWhereInput[]
  OR?: Prisma.EventWhereInput[]
  NOT?: Prisma.EventWhereInput | Prisma.EventWhereInput[]
  id?: Prisma.StringFilter<"Event"> | string
  title?: Prisma.StringFilter<"Event"> | string
  description?: Prisma.StringFilter<"Event"> | string
  eventType?: Prisma.EnumEventTypeFilter<"Event"> | $Enums.EventType
  location?: Prisma.StringNullableFilter<"Event"> | string | null
  isVirtual?: Prisma.BoolFilter<"Event"> | boolean
  virtualLink?: Prisma.StringNullableFilter<"Event"> | string | null
  startDateTime?: Prisma.DateTimeFilter<"Event"> | Date | string
  endDateTime?: Prisma.DateTimeFilter<"Event"> | Date | string
  maxAttendees?: Prisma.IntNullableFilter<"Event"> | number | null
  imageUrl?: Prisma.StringNullableFilter<"Event"> | string | null
  organizationId?: Prisma.StringNullableFilter<"Event"> | string | null
  organizerId?: Prisma.StringNullableFilter<"Event"> | string | null
  isPublished?: Prisma.BoolFilter<"Event"> | boolean
  createdAt?: Prisma.DateTimeFilter<"Event"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Event"> | Date | string
  organization?: Prisma.XOR<Prisma.OrganizationNullableScalarRelationFilter, Prisma.OrganizationWhereInput> | null
  registrations?: Prisma.EventRegistrationListRelationFilter
  organizer?: Prisma.XOR<Prisma.MemberNullableScalarRelationFilter, Prisma.MemberWhereInput> | null
}

export type EventOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  title?: Prisma.SortOrder
  description?: Prisma.SortOrder
  eventType?: Prisma.SortOrder
  location?: Prisma.SortOrderInput | Prisma.SortOrder
  isVirtual?: Prisma.SortOrder
  virtualLink?: Prisma.SortOrderInput | Prisma.SortOrder
  startDateTime?: Prisma.SortOrder
  endDateTime?: Prisma.SortOrder
  maxAttendees?: Prisma.SortOrderInput | Prisma.SortOrder
  imageUrl?: Prisma.SortOrderInput | Prisma.SortOrder
  organizationId?: Prisma.SortOrderInput | Prisma.SortOrder
  organizerId?: Prisma.SortOrderInput | Prisma.SortOrder
  isPublished?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  organization?: Prisma.OrganizationOrderByWithRelationInput
  registrations?: Prisma.EventRegistrationOrderByRelationAggregateInput
  organizer?: Prisma.MemberOrderByWithRelationInput
  _relevance?: Prisma.EventOrderByRelevanceInput
}

export type EventWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  AND?: Prisma.EventWhereInput | Prisma.EventWhereInput[]
  OR?: Prisma.EventWhereInput[]
  NOT?: Prisma.EventWhereInput | Prisma.EventWhereInput[]
  title?: Prisma.StringFilter<"Event"> | string
  description?: Prisma.StringFilter<"Event"> | string
  eventType?: Prisma.EnumEventTypeFilter<"Event"> | $Enums.EventType
  location?: Prisma.StringNullableFilter<"Event"> | string | null
  isVirtual?: Prisma.BoolFilter<"Event"> | boolean
  virtualLink?: Prisma.StringNullableFilter<"Event"> | string | null
  startDateTime?: Prisma.DateTimeFilter<"Event"> | Date | string
  endDateTime?: Prisma.DateTimeFilter<"Event"> | Date | string
  maxAttendees?: Prisma.IntNullableFilter<"Event"> | number | null
  imageUrl?: Prisma.StringNullableFilter<"Event"> | string | null
  organizationId?: Prisma.StringNullableFilter<"Event"> | string | null
  organizerId?: Prisma.StringNullableFilter<"Event"> | string | null
  isPublished?: Prisma.BoolFilter<"Event"> | boolean
  createdAt?: Prisma.DateTimeFilter<"Event"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Event"> | Date | string
  organization?: Prisma.XOR<Prisma.OrganizationNullableScalarRelationFilter, Prisma.OrganizationWhereInput> | null
  registrations?: Prisma.EventRegistrationListRelationFilter
  organizer?: Prisma.XOR<Prisma.MemberNullableScalarRelationFilter, Prisma.MemberWhereInput> | null
}, "id">

export type EventOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  title?: Prisma.SortOrder
  description?: Prisma.SortOrder
  eventType?: Prisma.SortOrder
  location?: Prisma.SortOrderInput | Prisma.SortOrder
  isVirtual?: Prisma.SortOrder
  virtualLink?: Prisma.SortOrderInput | Prisma.SortOrder
  startDateTime?: Prisma.SortOrder
  endDateTime?: Prisma.SortOrder
  maxAttendees?: Prisma.SortOrderInput | Prisma.SortOrder
  imageUrl?: Prisma.SortOrderInput | Prisma.SortOrder
  organizationId?: Prisma.SortOrderInput | Prisma.SortOrder
  organizerId?: Prisma.SortOrderInput | Prisma.SortOrder
  isPublished?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.EventCountOrderByAggregateInput
  _avg?: Prisma.EventAvgOrderByAggregateInput
  _max?: Prisma.EventMaxOrderByAggregateInput
  _min?: Prisma.EventMinOrderByAggregateInput
  _sum?: Prisma.EventSumOrderByAggregateInput
}

export type EventScalarWhereWithAggregatesInput = {
  AND?: Prisma.EventScalarWhereWithAggregatesInput | Prisma.EventScalarWhereWithAggregatesInput[]
  OR?: Prisma.EventScalarWhereWithAggregatesInput[]
  NOT?: Prisma.EventScalarWhereWithAggregatesInput | Prisma.EventScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"Event"> | string
  title?: Prisma.StringWithAggregatesFilter<"Event"> | string
  description?: Prisma.StringWithAggregatesFilter<"Event"> | string
  eventType?: Prisma.EnumEventTypeWithAggregatesFilter<"Event"> | $Enums.EventType
  location?: Prisma.StringNullableWithAggregatesFilter<"Event"> | string | null
  isVirtual?: Prisma.BoolWithAggregatesFilter<"Event"> | boolean
  virtualLink?: Prisma.StringNullableWithAggregatesFilter<"Event"> | string | null
  startDateTime?: Prisma.DateTimeWithAggregatesFilter<"Event"> | Date | string
  endDateTime?: Prisma.DateTimeWithAggregatesFilter<"Event"> | Date | string
  maxAttendees?: Prisma.IntNullableWithAggregatesFilter<"Event"> | number | null
  imageUrl?: Prisma.StringNullableWithAggregatesFilter<"Event"> | string | null
  organizationId?: Prisma.StringNullableWithAggregatesFilter<"Event"> | string | null
  organizerId?: Prisma.StringNullableWithAggregatesFilter<"Event"> | string | null
  isPublished?: Prisma.BoolWithAggregatesFilter<"Event"> | boolean
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"Event"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"Event"> | Date | string
}

export type EventCreateInput = {
  id?: string
  title: string
  description: string
  eventType?: $Enums.EventType
  location?: string | null
  isVirtual?: boolean
  virtualLink?: string | null
  startDateTime: Date | string
  endDateTime: Date | string
  maxAttendees?: number | null
  imageUrl?: string | null
  isPublished?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  organization?: Prisma.OrganizationCreateNestedOneWithoutEventsInput
  registrations?: Prisma.EventRegistrationCreateNestedManyWithoutEventInput
  organizer?: Prisma.MemberCreateNestedOneWithoutOrganizedEventsInput
}

export type EventUncheckedCreateInput = {
  id?: string
  title: string
  description: string
  eventType?: $Enums.EventType
  location?: string | null
  isVirtual?: boolean
  virtualLink?: string | null
  startDateTime: Date | string
  endDateTime: Date | string
  maxAttendees?: number | null
  imageUrl?: string | null
  organizationId?: string | null
  organizerId?: string | null
  isPublished?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  registrations?: Prisma.EventRegistrationUncheckedCreateNestedManyWithoutEventInput
}

export type EventUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  eventType?: Prisma.EnumEventTypeFieldUpdateOperationsInput | $Enums.EventType
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isVirtual?: Prisma.BoolFieldUpdateOperationsInput | boolean
  virtualLink?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  startDateTime?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  endDateTime?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  maxAttendees?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  imageUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isPublished?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  organization?: Prisma.OrganizationUpdateOneWithoutEventsNestedInput
  registrations?: Prisma.EventRegistrationUpdateManyWithoutEventNestedInput
  organizer?: Prisma.MemberUpdateOneWithoutOrganizedEventsNestedInput
}

export type EventUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  eventType?: Prisma.EnumEventTypeFieldUpdateOperationsInput | $Enums.EventType
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isVirtual?: Prisma.BoolFieldUpdateOperationsInput | boolean
  virtualLink?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  startDateTime?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  endDateTime?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  maxAttendees?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  imageUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  organizationId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  organizerId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isPublished?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  registrations?: Prisma.EventRegistrationUncheckedUpdateManyWithoutEventNestedInput
}

export type EventCreateManyInput = {
  id?: string
  title: string
  description: string
  eventType?: $Enums.EventType
  location?: string | null
  isVirtual?: boolean
  virtualLink?: string | null
  startDateTime: Date | string
  endDateTime: Date | string
  maxAttendees?: number | null
  imageUrl?: string | null
  organizationId?: string | null
  organizerId?: string | null
  isPublished?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type EventUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  eventType?: Prisma.EnumEventTypeFieldUpdateOperationsInput | $Enums.EventType
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isVirtual?: Prisma.BoolFieldUpdateOperationsInput | boolean
  virtualLink?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  startDateTime?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  endDateTime?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  maxAttendees?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  imageUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isPublished?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type EventUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  eventType?: Prisma.EnumEventTypeFieldUpdateOperationsInput | $Enums.EventType
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isVirtual?: Prisma.BoolFieldUpdateOperationsInput | boolean
  virtualLink?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  startDateTime?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  endDateTime?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  maxAttendees?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  imageUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  organizationId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  organizerId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isPublished?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type EventListRelationFilter = {
  every?: Prisma.EventWhereInput
  some?: Prisma.EventWhereInput
  none?: Prisma.EventWhereInput
}

export type EventOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type EventOrderByRelevanceInput = {
  fields: Prisma.EventOrderByRelevanceFieldEnum | Prisma.EventOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type EventCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  title?: Prisma.SortOrder
  description?: Prisma.SortOrder
  eventType?: Prisma.SortOrder
  location?: Prisma.SortOrder
  isVirtual?: Prisma.SortOrder
  virtualLink?: Prisma.SortOrder
  startDateTime?: Prisma.SortOrder
  endDateTime?: Prisma.SortOrder
  maxAttendees?: Prisma.SortOrder
  imageUrl?: Prisma.SortOrder
  organizationId?: Prisma.SortOrder
  organizerId?: Prisma.SortOrder
  isPublished?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type EventAvgOrderByAggregateInput = {
  maxAttendees?: Prisma.SortOrder
}

export type EventMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  title?: Prisma.SortOrder
  description?: Prisma.SortOrder
  eventType?: Prisma.SortOrder
  location?: Prisma.SortOrder
  isVirtual?: Prisma.SortOrder
  virtualLink?: Prisma.SortOrder
  startDateTime?: Prisma.SortOrder
  endDateTime?: Prisma.SortOrder
  maxAttendees?: Prisma.SortOrder
  imageUrl?: Prisma.SortOrder
  organizationId?: Prisma.SortOrder
  organizerId?: Prisma.SortOrder
  isPublished?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type EventMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  title?: Prisma.SortOrder
  description?: Prisma.SortOrder
  eventType?: Prisma.SortOrder
  location?: Prisma.SortOrder
  isVirtual?: Prisma.SortOrder
  virtualLink?: Prisma.SortOrder
  startDateTime?: Prisma.SortOrder
  endDateTime?: Prisma.SortOrder
  maxAttendees?: Prisma.SortOrder
  imageUrl?: Prisma.SortOrder
  organizationId?: Prisma.SortOrder
  organizerId?: Prisma.SortOrder
  isPublished?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type EventSumOrderByAggregateInput = {
  maxAttendees?: Prisma.SortOrder
}

export type EventScalarRelationFilter = {
  is?: Prisma.EventWhereInput
  isNot?: Prisma.EventWhereInput
}

export type EventCreateNestedManyWithoutOrganizationInput = {
  create?: Prisma.XOR<Prisma.EventCreateWithoutOrganizationInput, Prisma.EventUncheckedCreateWithoutOrganizationInput> | Prisma.EventCreateWithoutOrganizationInput[] | Prisma.EventUncheckedCreateWithoutOrganizationInput[]
  connectOrCreate?: Prisma.EventCreateOrConnectWithoutOrganizationInput | Prisma.EventCreateOrConnectWithoutOrganizationInput[]
  createMany?: Prisma.EventCreateManyOrganizationInputEnvelope
  connect?: Prisma.EventWhereUniqueInput | Prisma.EventWhereUniqueInput[]
}

export type EventUncheckedCreateNestedManyWithoutOrganizationInput = {
  create?: Prisma.XOR<Prisma.EventCreateWithoutOrganizationInput, Prisma.EventUncheckedCreateWithoutOrganizationInput> | Prisma.EventCreateWithoutOrganizationInput[] | Prisma.EventUncheckedCreateWithoutOrganizationInput[]
  connectOrCreate?: Prisma.EventCreateOrConnectWithoutOrganizationInput | Prisma.EventCreateOrConnectWithoutOrganizationInput[]
  createMany?: Prisma.EventCreateManyOrganizationInputEnvelope
  connect?: Prisma.EventWhereUniqueInput | Prisma.EventWhereUniqueInput[]
}

export type EventUpdateManyWithoutOrganizationNestedInput = {
  create?: Prisma.XOR<Prisma.EventCreateWithoutOrganizationInput, Prisma.EventUncheckedCreateWithoutOrganizationInput> | Prisma.EventCreateWithoutOrganizationInput[] | Prisma.EventUncheckedCreateWithoutOrganizationInput[]
  connectOrCreate?: Prisma.EventCreateOrConnectWithoutOrganizationInput | Prisma.EventCreateOrConnectWithoutOrganizationInput[]
  upsert?: Prisma.EventUpsertWithWhereUniqueWithoutOrganizationInput | Prisma.EventUpsertWithWhereUniqueWithoutOrganizationInput[]
  createMany?: Prisma.EventCreateManyOrganizationInputEnvelope
  set?: Prisma.EventWhereUniqueInput | Prisma.EventWhereUniqueInput[]
  disconnect?: Prisma.EventWhereUniqueInput | Prisma.EventWhereUniqueInput[]
  delete?: Prisma.EventWhereUniqueInput | Prisma.EventWhereUniqueInput[]
  connect?: Prisma.EventWhereUniqueInput | Prisma.EventWhereUniqueInput[]
  update?: Prisma.EventUpdateWithWhereUniqueWithoutOrganizationInput | Prisma.EventUpdateWithWhereUniqueWithoutOrganizationInput[]
  updateMany?: Prisma.EventUpdateManyWithWhereWithoutOrganizationInput | Prisma.EventUpdateManyWithWhereWithoutOrganizationInput[]
  deleteMany?: Prisma.EventScalarWhereInput | Prisma.EventScalarWhereInput[]
}

export type EventUncheckedUpdateManyWithoutOrganizationNestedInput = {
  create?: Prisma.XOR<Prisma.EventCreateWithoutOrganizationInput, Prisma.EventUncheckedCreateWithoutOrganizationInput> | Prisma.EventCreateWithoutOrganizationInput[] | Prisma.EventUncheckedCreateWithoutOrganizationInput[]
  connectOrCreate?: Prisma.EventCreateOrConnectWithoutOrganizationInput | Prisma.EventCreateOrConnectWithoutOrganizationInput[]
  upsert?: Prisma.EventUpsertWithWhereUniqueWithoutOrganizationInput | Prisma.EventUpsertWithWhereUniqueWithoutOrganizationInput[]
  createMany?: Prisma.EventCreateManyOrganizationInputEnvelope
  set?: Prisma.EventWhereUniqueInput | Prisma.EventWhereUniqueInput[]
  disconnect?: Prisma.EventWhereUniqueInput | Prisma.EventWhereUniqueInput[]
  delete?: Prisma.EventWhereUniqueInput | Prisma.EventWhereUniqueInput[]
  connect?: Prisma.EventWhereUniqueInput | Prisma.EventWhereUniqueInput[]
  update?: Prisma.EventUpdateWithWhereUniqueWithoutOrganizationInput | Prisma.EventUpdateWithWhereUniqueWithoutOrganizationInput[]
  updateMany?: Prisma.EventUpdateManyWithWhereWithoutOrganizationInput | Prisma.EventUpdateManyWithWhereWithoutOrganizationInput[]
  deleteMany?: Prisma.EventScalarWhereInput | Prisma.EventScalarWhereInput[]
}

export type EventCreateNestedManyWithoutOrganizerInput = {
  create?: Prisma.XOR<Prisma.EventCreateWithoutOrganizerInput, Prisma.EventUncheckedCreateWithoutOrganizerInput> | Prisma.EventCreateWithoutOrganizerInput[] | Prisma.EventUncheckedCreateWithoutOrganizerInput[]
  connectOrCreate?: Prisma.EventCreateOrConnectWithoutOrganizerInput | Prisma.EventCreateOrConnectWithoutOrganizerInput[]
  createMany?: Prisma.EventCreateManyOrganizerInputEnvelope
  connect?: Prisma.EventWhereUniqueInput | Prisma.EventWhereUniqueInput[]
}

export type EventUncheckedCreateNestedManyWithoutOrganizerInput = {
  create?: Prisma.XOR<Prisma.EventCreateWithoutOrganizerInput, Prisma.EventUncheckedCreateWithoutOrganizerInput> | Prisma.EventCreateWithoutOrganizerInput[] | Prisma.EventUncheckedCreateWithoutOrganizerInput[]
  connectOrCreate?: Prisma.EventCreateOrConnectWithoutOrganizerInput | Prisma.EventCreateOrConnectWithoutOrganizerInput[]
  createMany?: Prisma.EventCreateManyOrganizerInputEnvelope
  connect?: Prisma.EventWhereUniqueInput | Prisma.EventWhereUniqueInput[]
}

export type EventUpdateManyWithoutOrganizerNestedInput = {
  create?: Prisma.XOR<Prisma.EventCreateWithoutOrganizerInput, Prisma.EventUncheckedCreateWithoutOrganizerInput> | Prisma.EventCreateWithoutOrganizerInput[] | Prisma.EventUncheckedCreateWithoutOrganizerInput[]
  connectOrCreate?: Prisma.EventCreateOrConnectWithoutOrganizerInput | Prisma.EventCreateOrConnectWithoutOrganizerInput[]
  upsert?: Prisma.EventUpsertWithWhereUniqueWithoutOrganizerInput | Prisma.EventUpsertWithWhereUniqueWithoutOrganizerInput[]
  createMany?: Prisma.EventCreateManyOrganizerInputEnvelope
  set?: Prisma.EventWhereUniqueInput | Prisma.EventWhereUniqueInput[]
  disconnect?: Prisma.EventWhereUniqueInput | Prisma.EventWhereUniqueInput[]
  delete?: Prisma.EventWhereUniqueInput | Prisma.EventWhereUniqueInput[]
  connect?: Prisma.EventWhereUniqueInput | Prisma.EventWhereUniqueInput[]
  update?: Prisma.EventUpdateWithWhereUniqueWithoutOrganizerInput | Prisma.EventUpdateWithWhereUniqueWithoutOrganizerInput[]
  updateMany?: Prisma.EventUpdateManyWithWhereWithoutOrganizerInput | Prisma.EventUpdateManyWithWhereWithoutOrganizerInput[]
  deleteMany?: Prisma.EventScalarWhereInput | Prisma.EventScalarWhereInput[]
}

export type EventUncheckedUpdateManyWithoutOrganizerNestedInput = {
  create?: Prisma.XOR<Prisma.EventCreateWithoutOrganizerInput, Prisma.EventUncheckedCreateWithoutOrganizerInput> | Prisma.EventCreateWithoutOrganizerInput[] | Prisma.EventUncheckedCreateWithoutOrganizerInput[]
  connectOrCreate?: Prisma.EventCreateOrConnectWithoutOrganizerInput | Prisma.EventCreateOrConnectWithoutOrganizerInput[]
  upsert?: Prisma.EventUpsertWithWhereUniqueWithoutOrganizerInput | Prisma.EventUpsertWithWhereUniqueWithoutOrganizerInput[]
  createMany?: Prisma.EventCreateManyOrganizerInputEnvelope
  set?: Prisma.EventWhereUniqueInput | Prisma.EventWhereUniqueInput[]
  disconnect?: Prisma.EventWhereUniqueInput | Prisma.EventWhereUniqueInput[]
  delete?: Prisma.EventWhereUniqueInput | Prisma.EventWhereUniqueInput[]
  connect?: Prisma.EventWhereUniqueInput | Prisma.EventWhereUniqueInput[]
  update?: Prisma.EventUpdateWithWhereUniqueWithoutOrganizerInput | Prisma.EventUpdateWithWhereUniqueWithoutOrganizerInput[]
  updateMany?: Prisma.EventUpdateManyWithWhereWithoutOrganizerInput | Prisma.EventUpdateManyWithWhereWithoutOrganizerInput[]
  deleteMany?: Prisma.EventScalarWhereInput | Prisma.EventScalarWhereInput[]
}

export type EnumEventTypeFieldUpdateOperationsInput = {
  set?: $Enums.EventType
}

export type NullableIntFieldUpdateOperationsInput = {
  set?: number | null
  increment?: number
  decrement?: number
  multiply?: number
  divide?: number
}

export type EventCreateNestedOneWithoutRegistrationsInput = {
  create?: Prisma.XOR<Prisma.EventCreateWithoutRegistrationsInput, Prisma.EventUncheckedCreateWithoutRegistrationsInput>
  connectOrCreate?: Prisma.EventCreateOrConnectWithoutRegistrationsInput
  connect?: Prisma.EventWhereUniqueInput
}

export type EventUpdateOneRequiredWithoutRegistrationsNestedInput = {
  create?: Prisma.XOR<Prisma.EventCreateWithoutRegistrationsInput, Prisma.EventUncheckedCreateWithoutRegistrationsInput>
  connectOrCreate?: Prisma.EventCreateOrConnectWithoutRegistrationsInput
  upsert?: Prisma.EventUpsertWithoutRegistrationsInput
  connect?: Prisma.EventWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.EventUpdateToOneWithWhereWithoutRegistrationsInput, Prisma.EventUpdateWithoutRegistrationsInput>, Prisma.EventUncheckedUpdateWithoutRegistrationsInput>
}

export type EventCreateWithoutOrganizationInput = {
  id?: string
  title: string
  description: string
  eventType?: $Enums.EventType
  location?: string | null
  isVirtual?: boolean
  virtualLink?: string | null
  startDateTime: Date | string
  endDateTime: Date | string
  maxAttendees?: number | null
  imageUrl?: string | null
  isPublished?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  registrations?: Prisma.EventRegistrationCreateNestedManyWithoutEventInput
  organizer?: Prisma.MemberCreateNestedOneWithoutOrganizedEventsInput
}

export type EventUncheckedCreateWithoutOrganizationInput = {
  id?: string
  title: string
  description: string
  eventType?: $Enums.EventType
  location?: string | null
  isVirtual?: boolean
  virtualLink?: string | null
  startDateTime: Date | string
  endDateTime: Date | string
  maxAttendees?: number | null
  imageUrl?: string | null
  organizerId?: string | null
  isPublished?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  registrations?: Prisma.EventRegistrationUncheckedCreateNestedManyWithoutEventInput
}

export type EventCreateOrConnectWithoutOrganizationInput = {
  where: Prisma.EventWhereUniqueInput
  create: Prisma.XOR<Prisma.EventCreateWithoutOrganizationInput, Prisma.EventUncheckedCreateWithoutOrganizationInput>
}

export type EventCreateManyOrganizationInputEnvelope = {
  data: Prisma.EventCreateManyOrganizationInput | Prisma.EventCreateManyOrganizationInput[]
  skipDuplicates?: boolean
}

export type EventUpsertWithWhereUniqueWithoutOrganizationInput = {
  where: Prisma.EventWhereUniqueInput
  update: Prisma.XOR<Prisma.EventUpdateWithoutOrganizationInput, Prisma.EventUncheckedUpdateWithoutOrganizationInput>
  create: Prisma.XOR<Prisma.EventCreateWithoutOrganizationInput, Prisma.EventUncheckedCreateWithoutOrganizationInput>
}

export type EventUpdateWithWhereUniqueWithoutOrganizationInput = {
  where: Prisma.EventWhereUniqueInput
  data: Prisma.XOR<Prisma.EventUpdateWithoutOrganizationInput, Prisma.EventUncheckedUpdateWithoutOrganizationInput>
}

export type EventUpdateManyWithWhereWithoutOrganizationInput = {
  where: Prisma.EventScalarWhereInput
  data: Prisma.XOR<Prisma.EventUpdateManyMutationInput, Prisma.EventUncheckedUpdateManyWithoutOrganizationInput>
}

export type EventScalarWhereInput = {
  AND?: Prisma.EventScalarWhereInput | Prisma.EventScalarWhereInput[]
  OR?: Prisma.EventScalarWhereInput[]
  NOT?: Prisma.EventScalarWhereInput | Prisma.EventScalarWhereInput[]
  id?: Prisma.StringFilter<"Event"> | string
  title?: Prisma.StringFilter<"Event"> | string
  description?: Prisma.StringFilter<"Event"> | string
  eventType?: Prisma.EnumEventTypeFilter<"Event"> | $Enums.EventType
  location?: Prisma.StringNullableFilter<"Event"> | string | null
  isVirtual?: Prisma.BoolFilter<"Event"> | boolean
  virtualLink?: Prisma.StringNullableFilter<"Event"> | string | null
  startDateTime?: Prisma.DateTimeFilter<"Event"> | Date | string
  endDateTime?: Prisma.DateTimeFilter<"Event"> | Date | string
  maxAttendees?: Prisma.IntNullableFilter<"Event"> | number | null
  imageUrl?: Prisma.StringNullableFilter<"Event"> | string | null
  organizationId?: Prisma.StringNullableFilter<"Event"> | string | null
  organizerId?: Prisma.StringNullableFilter<"Event"> | string | null
  isPublished?: Prisma.BoolFilter<"Event"> | boolean
  createdAt?: Prisma.DateTimeFilter<"Event"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Event"> | Date | string
}

export type EventCreateWithoutOrganizerInput = {
  id?: string
  title: string
  description: string
  eventType?: $Enums.EventType
  location?: string | null
  isVirtual?: boolean
  virtualLink?: string | null
  startDateTime: Date | string
  endDateTime: Date | string
  maxAttendees?: number | null
  imageUrl?: string | null
  isPublished?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  organization?: Prisma.OrganizationCreateNestedOneWithoutEventsInput
  registrations?: Prisma.EventRegistrationCreateNestedManyWithoutEventInput
}

export type EventUncheckedCreateWithoutOrganizerInput = {
  id?: string
  title: string
  description: string
  eventType?: $Enums.EventType
  location?: string | null
  isVirtual?: boolean
  virtualLink?: string | null
  startDateTime: Date | string
  endDateTime: Date | string
  maxAttendees?: number | null
  imageUrl?: string | null
  organizationId?: string | null
  isPublished?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  registrations?: Prisma.EventRegistrationUncheckedCreateNestedManyWithoutEventInput
}

export type EventCreateOrConnectWithoutOrganizerInput = {
  where: Prisma.EventWhereUniqueInput
  create: Prisma.XOR<Prisma.EventCreateWithoutOrganizerInput, Prisma.EventUncheckedCreateWithoutOrganizerInput>
}

export type EventCreateManyOrganizerInputEnvelope = {
  data: Prisma.EventCreateManyOrganizerInput | Prisma.EventCreateManyOrganizerInput[]
  skipDuplicates?: boolean
}

export type EventUpsertWithWhereUniqueWithoutOrganizerInput = {
  where: Prisma.EventWhereUniqueInput
  update: Prisma.XOR<Prisma.EventUpdateWithoutOrganizerInput, Prisma.EventUncheckedUpdateWithoutOrganizerInput>
  create: Prisma.XOR<Prisma.EventCreateWithoutOrganizerInput, Prisma.EventUncheckedCreateWithoutOrganizerInput>
}

export type EventUpdateWithWhereUniqueWithoutOrganizerInput = {
  where: Prisma.EventWhereUniqueInput
  data: Prisma.XOR<Prisma.EventUpdateWithoutOrganizerInput, Prisma.EventUncheckedUpdateWithoutOrganizerInput>
}

export type EventUpdateManyWithWhereWithoutOrganizerInput = {
  where: Prisma.EventScalarWhereInput
  data: Prisma.XOR<Prisma.EventUpdateManyMutationInput, Prisma.EventUncheckedUpdateManyWithoutOrganizerInput>
}

export type EventCreateWithoutRegistrationsInput = {
  id?: string
  title: string
  description: string
  eventType?: $Enums.EventType
  location?: string | null
  isVirtual?: boolean
  virtualLink?: string | null
  startDateTime: Date | string
  endDateTime: Date | string
  maxAttendees?: number | null
  imageUrl?: string | null
  isPublished?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  organization?: Prisma.OrganizationCreateNestedOneWithoutEventsInput
  organizer?: Prisma.MemberCreateNestedOneWithoutOrganizedEventsInput
}

export type EventUncheckedCreateWithoutRegistrationsInput = {
  id?: string
  title: string
  description: string
  eventType?: $Enums.EventType
  location?: string | null
  isVirtual?: boolean
  virtualLink?: string | null
  startDateTime: Date | string
  endDateTime: Date | string
  maxAttendees?: number | null
  imageUrl?: string | null
  organizationId?: string | null
  organizerId?: string | null
  isPublished?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type EventCreateOrConnectWithoutRegistrationsInput = {
  where: Prisma.EventWhereUniqueInput
  create: Prisma.XOR<Prisma.EventCreateWithoutRegistrationsInput, Prisma.EventUncheckedCreateWithoutRegistrationsInput>
}

export type EventUpsertWithoutRegistrationsInput = {
  update: Prisma.XOR<Prisma.EventUpdateWithoutRegistrationsInput, Prisma.EventUncheckedUpdateWithoutRegistrationsInput>
  create: Prisma.XOR<Prisma.EventCreateWithoutRegistrationsInput, Prisma.EventUncheckedCreateWithoutRegistrationsInput>
  where?: Prisma.EventWhereInput
}

export type EventUpdateToOneWithWhereWithoutRegistrationsInput = {
  where?: Prisma.EventWhereInput
  data: Prisma.XOR<Prisma.EventUpdateWithoutRegistrationsInput, Prisma.EventUncheckedUpdateWithoutRegistrationsInput>
}

export type EventUpdateWithoutRegistrationsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  eventType?: Prisma.EnumEventTypeFieldUpdateOperationsInput | $Enums.EventType
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isVirtual?: Prisma.BoolFieldUpdateOperationsInput | boolean
  virtualLink?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  startDateTime?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  endDateTime?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  maxAttendees?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  imageUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isPublished?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  organization?: Prisma.OrganizationUpdateOneWithoutEventsNestedInput
  organizer?: Prisma.MemberUpdateOneWithoutOrganizedEventsNestedInput
}

export type EventUncheckedUpdateWithoutRegistrationsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  eventType?: Prisma.EnumEventTypeFieldUpdateOperationsInput | $Enums.EventType
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isVirtual?: Prisma.BoolFieldUpdateOperationsInput | boolean
  virtualLink?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  startDateTime?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  endDateTime?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  maxAttendees?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  imageUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  organizationId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  organizerId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isPublished?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type EventCreateManyOrganizationInput = {
  id?: string
  title: string
  description: string
  eventType?: $Enums.EventType
  location?: string | null
  isVirtual?: boolean
  virtualLink?: string | null
  startDateTime: Date | string
  endDateTime: Date | string
  maxAttendees?: number | null
  imageUrl?: string | null
  organizerId?: string | null
  isPublished?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type EventUpdateWithoutOrganizationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  eventType?: Prisma.EnumEventTypeFieldUpdateOperationsInput | $Enums.EventType
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isVirtual?: Prisma.BoolFieldUpdateOperationsInput | boolean
  virtualLink?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  startDateTime?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  endDateTime?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  maxAttendees?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  imageUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isPublished?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  registrations?: Prisma.EventRegistrationUpdateManyWithoutEventNestedInput
  organizer?: Prisma.MemberUpdateOneWithoutOrganizedEventsNestedInput
}

export type EventUncheckedUpdateWithoutOrganizationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  eventType?: Prisma.EnumEventTypeFieldUpdateOperationsInput | $Enums.EventType
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isVirtual?: Prisma.BoolFieldUpdateOperationsInput | boolean
  virtualLink?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  startDateTime?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  endDateTime?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  maxAttendees?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  imageUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  organizerId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isPublished?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  registrations?: Prisma.EventRegistrationUncheckedUpdateManyWithoutEventNestedInput
}

export type EventUncheckedUpdateManyWithoutOrganizationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  eventType?: Prisma.EnumEventTypeFieldUpdateOperationsInput | $Enums.EventType
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isVirtual?: Prisma.BoolFieldUpdateOperationsInput | boolean
  virtualLink?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  startDateTime?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  endDateTime?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  maxAttendees?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  imageUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  organizerId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isPublished?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type EventCreateManyOrganizerInput = {
  id?: string
  title: string
  description: string
  eventType?: $Enums.EventType
  location?: string | null
  isVirtual?: boolean
  virtualLink?: string | null
  startDateTime: Date | string
  endDateTime: Date | string
  maxAttendees?: number | null
  imageUrl?: string | null
  organizationId?: string | null
  isPublished?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type EventUpdateWithoutOrganizerInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  eventType?: Prisma.EnumEventTypeFieldUpdateOperationsInput | $Enums.EventType
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isVirtual?: Prisma.BoolFieldUpdateOperationsInput | boolean
  virtualLink?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  startDateTime?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  endDateTime?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  maxAttendees?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  imageUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isPublished?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  organization?: Prisma.OrganizationUpdateOneWithoutEventsNestedInput
  registrations?: Prisma.EventRegistrationUpdateManyWithoutEventNestedInput
}

export type EventUncheckedUpdateWithoutOrganizerInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  eventType?: Prisma.EnumEventTypeFieldUpdateOperationsInput | $Enums.EventType
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isVirtual?: Prisma.BoolFieldUpdateOperationsInput | boolean
  virtualLink?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  startDateTime?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  endDateTime?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  maxAttendees?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  imageUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  organizationId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isPublished?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  registrations?: Prisma.EventRegistrationUncheckedUpdateManyWithoutEventNestedInput
}

export type EventUncheckedUpdateManyWithoutOrganizerInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  eventType?: Prisma.EnumEventTypeFieldUpdateOperationsInput | $Enums.EventType
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isVirtual?: Prisma.BoolFieldUpdateOperationsInput | boolean
  virtualLink?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  startDateTime?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  endDateTime?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  maxAttendees?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  imageUrl?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  organizationId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isPublished?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}


/**
 * Count Type EventCountOutputType
 */

export type EventCountOutputType = {
  registrations: number
}

export type EventCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  registrations?: boolean | EventCountOutputTypeCountRegistrationsArgs
}

/**
 * EventCountOutputType without action
 */
export type EventCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the EventCountOutputType
   */
  select?: Prisma.EventCountOutputTypeSelect<ExtArgs> | null
}

/**
 * EventCountOutputType without action
 */
export type EventCountOutputTypeCountRegistrationsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.EventRegistrationWhereInput
}


export type EventSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  title?: boolean
  description?: boolean
  eventType?: boolean
  location?: boolean
  isVirtual?: boolean
  virtualLink?: boolean
  startDateTime?: boolean
  endDateTime?: boolean
  maxAttendees?: boolean
  imageUrl?: boolean
  organizationId?: boolean
  organizerId?: boolean
  isPublished?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  organization?: boolean | Prisma.Event$organizationArgs<ExtArgs>
  registrations?: boolean | Prisma.Event$registrationsArgs<ExtArgs>
  organizer?: boolean | Prisma.Event$organizerArgs<ExtArgs>
  _count?: boolean | Prisma.EventCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["event"]>



export type EventSelectScalar = {
  id?: boolean
  title?: boolean
  description?: boolean
  eventType?: boolean
  location?: boolean
  isVirtual?: boolean
  virtualLink?: boolean
  startDateTime?: boolean
  endDateTime?: boolean
  maxAttendees?: boolean
  imageUrl?: boolean
  organizationId?: boolean
  organizerId?: boolean
  isPublished?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type EventOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "title" | "description" | "eventType" | "location" | "isVirtual" | "virtualLink" | "startDateTime" | "endDateTime" | "maxAttendees" | "imageUrl" | "organizationId" | "organizerId" | "isPublished" | "createdAt" | "updatedAt", ExtArgs["result"]["event"]>
export type EventInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  organization?: boolean | Prisma.Event$organizationArgs<ExtArgs>
  registrations?: boolean | Prisma.Event$registrationsArgs<ExtArgs>
  organizer?: boolean | Prisma.Event$organizerArgs<ExtArgs>
  _count?: boolean | Prisma.EventCountOutputTypeDefaultArgs<ExtArgs>
}

export type $EventPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Event"
  objects: {
    organization: Prisma.$OrganizationPayload<ExtArgs> | null
    registrations: Prisma.$EventRegistrationPayload<ExtArgs>[]
    organizer: Prisma.$MemberPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    title: string
    description: string
    eventType: $Enums.EventType
    location: string | null
    isVirtual: boolean
    virtualLink: string | null
    startDateTime: Date
    endDateTime: Date
    maxAttendees: number | null
    imageUrl: string | null
    organizationId: string | null
    organizerId: string | null
    isPublished: boolean
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["event"]>
  composites: {}
}

export type EventGetPayload<S extends boolean | null | undefined | EventDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$EventPayload, S>

export type EventCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<EventFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: EventCountAggregateInputType | true
  }

export interface EventDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Event'], meta: { name: 'Event' } }
  /**
   * Find zero or one Event that matches the filter.
   * @param {EventFindUniqueArgs} args - Arguments to find a Event
   * @example
   * // Get one Event
   * const event = await prisma.event.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends EventFindUniqueArgs>(args: Prisma.SelectSubset<T, EventFindUniqueArgs<ExtArgs>>): Prisma.Prisma__EventClient<runtime.Types.Result.GetResult<Prisma.$EventPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Event that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {EventFindUniqueOrThrowArgs} args - Arguments to find a Event
   * @example
   * // Get one Event
   * const event = await prisma.event.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends EventFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, EventFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__EventClient<runtime.Types.Result.GetResult<Prisma.$EventPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Event that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {EventFindFirstArgs} args - Arguments to find a Event
   * @example
   * // Get one Event
   * const event = await prisma.event.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends EventFindFirstArgs>(args?: Prisma.SelectSubset<T, EventFindFirstArgs<ExtArgs>>): Prisma.Prisma__EventClient<runtime.Types.Result.GetResult<Prisma.$EventPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Event that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {EventFindFirstOrThrowArgs} args - Arguments to find a Event
   * @example
   * // Get one Event
   * const event = await prisma.event.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends EventFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, EventFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__EventClient<runtime.Types.Result.GetResult<Prisma.$EventPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Events that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {EventFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Events
   * const events = await prisma.event.findMany()
   * 
   * // Get first 10 Events
   * const events = await prisma.event.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const eventWithIdOnly = await prisma.event.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends EventFindManyArgs>(args?: Prisma.SelectSubset<T, EventFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$EventPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Event.
   * @param {EventCreateArgs} args - Arguments to create a Event.
   * @example
   * // Create one Event
   * const Event = await prisma.event.create({
   *   data: {
   *     // ... data to create a Event
   *   }
   * })
   * 
   */
  create<T extends EventCreateArgs>(args: Prisma.SelectSubset<T, EventCreateArgs<ExtArgs>>): Prisma.Prisma__EventClient<runtime.Types.Result.GetResult<Prisma.$EventPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Events.
   * @param {EventCreateManyArgs} args - Arguments to create many Events.
   * @example
   * // Create many Events
   * const event = await prisma.event.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends EventCreateManyArgs>(args?: Prisma.SelectSubset<T, EventCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Event.
   * @param {EventDeleteArgs} args - Arguments to delete one Event.
   * @example
   * // Delete one Event
   * const Event = await prisma.event.delete({
   *   where: {
   *     // ... filter to delete one Event
   *   }
   * })
   * 
   */
  delete<T extends EventDeleteArgs>(args: Prisma.SelectSubset<T, EventDeleteArgs<ExtArgs>>): Prisma.Prisma__EventClient<runtime.Types.Result.GetResult<Prisma.$EventPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Event.
   * @param {EventUpdateArgs} args - Arguments to update one Event.
   * @example
   * // Update one Event
   * const event = await prisma.event.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends EventUpdateArgs>(args: Prisma.SelectSubset<T, EventUpdateArgs<ExtArgs>>): Prisma.Prisma__EventClient<runtime.Types.Result.GetResult<Prisma.$EventPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Events.
   * @param {EventDeleteManyArgs} args - Arguments to filter Events to delete.
   * @example
   * // Delete a few Events
   * const { count } = await prisma.event.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends EventDeleteManyArgs>(args?: Prisma.SelectSubset<T, EventDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Events.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {EventUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Events
   * const event = await prisma.event.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends EventUpdateManyArgs>(args: Prisma.SelectSubset<T, EventUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Event.
   * @param {EventUpsertArgs} args - Arguments to update or create a Event.
   * @example
   * // Update or create a Event
   * const event = await prisma.event.upsert({
   *   create: {
   *     // ... data to create a Event
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Event we want to update
   *   }
   * })
   */
  upsert<T extends EventUpsertArgs>(args: Prisma.SelectSubset<T, EventUpsertArgs<ExtArgs>>): Prisma.Prisma__EventClient<runtime.Types.Result.GetResult<Prisma.$EventPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Events.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {EventCountArgs} args - Arguments to filter Events to count.
   * @example
   * // Count the number of Events
   * const count = await prisma.event.count({
   *   where: {
   *     // ... the filter for the Events we want to count
   *   }
   * })
  **/
  count<T extends EventCountArgs>(
    args?: Prisma.Subset<T, EventCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], EventCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Event.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {EventAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends EventAggregateArgs>(args: Prisma.Subset<T, EventAggregateArgs>): Prisma.PrismaPromise<GetEventAggregateType<T>>

  /**
   * Group by Event.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {EventGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends EventGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: EventGroupByArgs['orderBy'] }
      : { orderBy?: EventGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, EventGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetEventGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Event model
 */
readonly fields: EventFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Event.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__EventClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  organization<T extends Prisma.Event$organizationArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Event$organizationArgs<ExtArgs>>): Prisma.Prisma__OrganizationClient<runtime.Types.Result.GetResult<Prisma.$OrganizationPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  registrations<T extends Prisma.Event$registrationsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Event$registrationsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$EventRegistrationPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  organizer<T extends Prisma.Event$organizerArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Event$organizerArgs<ExtArgs>>): Prisma.Prisma__MemberClient<runtime.Types.Result.GetResult<Prisma.$MemberPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Event model
 */
export interface EventFieldRefs {
  readonly id: Prisma.FieldRef<"Event", 'String'>
  readonly title: Prisma.FieldRef<"Event", 'String'>
  readonly description: Prisma.FieldRef<"Event", 'String'>
  readonly eventType: Prisma.FieldRef<"Event", 'EventType'>
  readonly location: Prisma.FieldRef<"Event", 'String'>
  readonly isVirtual: Prisma.FieldRef<"Event", 'Boolean'>
  readonly virtualLink: Prisma.FieldRef<"Event", 'String'>
  readonly startDateTime: Prisma.FieldRef<"Event", 'DateTime'>
  readonly endDateTime: Prisma.FieldRef<"Event", 'DateTime'>
  readonly maxAttendees: Prisma.FieldRef<"Event", 'Int'>
  readonly imageUrl: Prisma.FieldRef<"Event", 'String'>
  readonly organizationId: Prisma.FieldRef<"Event", 'String'>
  readonly organizerId: Prisma.FieldRef<"Event", 'String'>
  readonly isPublished: Prisma.FieldRef<"Event", 'Boolean'>
  readonly createdAt: Prisma.FieldRef<"Event", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"Event", 'DateTime'>
}
    

// Custom InputTypes
/**
 * Event findUnique
 */
export type EventFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Event
   */
  select?: Prisma.EventSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Event
   */
  omit?: Prisma.EventOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.EventInclude<ExtArgs> | null
  /**
   * Filter, which Event to fetch.
   */
  where: Prisma.EventWhereUniqueInput
}

/**
 * Event findUniqueOrThrow
 */
export type EventFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Event
   */
  select?: Prisma.EventSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Event
   */
  omit?: Prisma.EventOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.EventInclude<ExtArgs> | null
  /**
   * Filter, which Event to fetch.
   */
  where: Prisma.EventWhereUniqueInput
}

/**
 * Event findFirst
 */
export type EventFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Event
   */
  select?: Prisma.EventSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Event
   */
  omit?: Prisma.EventOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.EventInclude<ExtArgs> | null
  /**
   * Filter, which Event to fetch.
   */
  where?: Prisma.EventWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Events to fetch.
   */
  orderBy?: Prisma.EventOrderByWithRelationInput | Prisma.EventOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Events.
   */
  cursor?: Prisma.EventWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Events from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Events.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Events.
   */
  distinct?: Prisma.EventScalarFieldEnum | Prisma.EventScalarFieldEnum[]
}

/**
 * Event findFirstOrThrow
 */
export type EventFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Event
   */
  select?: Prisma.EventSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Event
   */
  omit?: Prisma.EventOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.EventInclude<ExtArgs> | null
  /**
   * Filter, which Event to fetch.
   */
  where?: Prisma.EventWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Events to fetch.
   */
  orderBy?: Prisma.EventOrderByWithRelationInput | Prisma.EventOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Events.
   */
  cursor?: Prisma.EventWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Events from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Events.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Events.
   */
  distinct?: Prisma.EventScalarFieldEnum | Prisma.EventScalarFieldEnum[]
}

/**
 * Event findMany
 */
export type EventFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Event
   */
  select?: Prisma.EventSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Event
   */
  omit?: Prisma.EventOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.EventInclude<ExtArgs> | null
  /**
   * Filter, which Events to fetch.
   */
  where?: Prisma.EventWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Events to fetch.
   */
  orderBy?: Prisma.EventOrderByWithRelationInput | Prisma.EventOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Events.
   */
  cursor?: Prisma.EventWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Events from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Events.
   */
  skip?: number
  distinct?: Prisma.EventScalarFieldEnum | Prisma.EventScalarFieldEnum[]
}

/**
 * Event create
 */
export type EventCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Event
   */
  select?: Prisma.EventSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Event
   */
  omit?: Prisma.EventOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.EventInclude<ExtArgs> | null
  /**
   * The data needed to create a Event.
   */
  data: Prisma.XOR<Prisma.EventCreateInput, Prisma.EventUncheckedCreateInput>
}

/**
 * Event createMany
 */
export type EventCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Events.
   */
  data: Prisma.EventCreateManyInput | Prisma.EventCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Event update
 */
export type EventUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Event
   */
  select?: Prisma.EventSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Event
   */
  omit?: Prisma.EventOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.EventInclude<ExtArgs> | null
  /**
   * The data needed to update a Event.
   */
  data: Prisma.XOR<Prisma.EventUpdateInput, Prisma.EventUncheckedUpdateInput>
  /**
   * Choose, which Event to update.
   */
  where: Prisma.EventWhereUniqueInput
}

/**
 * Event updateMany
 */
export type EventUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Events.
   */
  data: Prisma.XOR<Prisma.EventUpdateManyMutationInput, Prisma.EventUncheckedUpdateManyInput>
  /**
   * Filter which Events to update
   */
  where?: Prisma.EventWhereInput
  /**
   * Limit how many Events to update.
   */
  limit?: number
}

/**
 * Event upsert
 */
export type EventUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Event
   */
  select?: Prisma.EventSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Event
   */
  omit?: Prisma.EventOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.EventInclude<ExtArgs> | null
  /**
   * The filter to search for the Event to update in case it exists.
   */
  where: Prisma.EventWhereUniqueInput
  /**
   * In case the Event found by the `where` argument doesn't exist, create a new Event with this data.
   */
  create: Prisma.XOR<Prisma.EventCreateInput, Prisma.EventUncheckedCreateInput>
  /**
   * In case the Event was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.EventUpdateInput, Prisma.EventUncheckedUpdateInput>
}

/**
 * Event delete
 */
export type EventDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Event
   */
  select?: Prisma.EventSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Event
   */
  omit?: Prisma.EventOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.EventInclude<ExtArgs> | null
  /**
   * Filter which Event to delete.
   */
  where: Prisma.EventWhereUniqueInput
}

/**
 * Event deleteMany
 */
export type EventDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Events to delete
   */
  where?: Prisma.EventWhereInput
  /**
   * Limit how many Events to delete.
   */
  limit?: number
}

/**
 * Event.organization
 */
export type Event$organizationArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Organization
   */
  select?: Prisma.OrganizationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Organization
   */
  omit?: Prisma.OrganizationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.OrganizationInclude<ExtArgs> | null
  where?: Prisma.OrganizationWhereInput
}

/**
 * Event.registrations
 */
export type Event$registrationsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the EventRegistration
   */
  select?: Prisma.EventRegistrationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the EventRegistration
   */
  omit?: Prisma.EventRegistrationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.EventRegistrationInclude<ExtArgs> | null
  where?: Prisma.EventRegistrationWhereInput
  orderBy?: Prisma.EventRegistrationOrderByWithRelationInput | Prisma.EventRegistrationOrderByWithRelationInput[]
  cursor?: Prisma.EventRegistrationWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.EventRegistrationScalarFieldEnum | Prisma.EventRegistrationScalarFieldEnum[]
}

/**
 * Event.organizer
 */
export type Event$organizerArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Member
   */
  select?: Prisma.MemberSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Member
   */
  omit?: Prisma.MemberOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MemberInclude<ExtArgs> | null
  where?: Prisma.MemberWhereInput
}

/**
 * Event without action
 */
export type EventDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Event
   */
  select?: Prisma.EventSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Event
   */
  omit?: Prisma.EventOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.EventInclude<ExtArgs> | null
}
