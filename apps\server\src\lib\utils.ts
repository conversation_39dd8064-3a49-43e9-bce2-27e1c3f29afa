import type { ProfileVisibility } from "../../prisma/generated/client";
import type { EnhancedContext } from "./context";

/**
 * Privacy and visibility utilities
 */
export function canViewProfile(
  profileVisibility: ProfileVisibility,
  viewerContext: EnhancedContext,
  profileUserId: string
): boolean {
  // Owner can always view their own profile
  if (viewerContext.user?.id === profileUserId) {
    return true;
  }

  switch (profileVisibility) {
    case "PUBLIC":
      return true;
    case "ALUMNI_ONLY":
      // Must be authenticated and have an alumni profile
      return !!viewerContext.user && !!viewerContext.alumniProfile;
    case "PRIVATE":
      return false;
    default:
      return false;
  }
}

/**
 * Check if user can access organization-scoped content
 */
export function canAccessOrganizationContent(
  context: EnhancedContext,
  requiredOrganizationId?: string
): boolean {
  if (!requiredOrganizationId) {
    return true; // Public content
  }

  return context.organization?.id === requiredOrganizationId;
}

/**
 * Check if user has specific role in organization
 */
export function hasRole(
  context: EnhancedContext,
  roles: string | string[]
): boolean {
  if (!context.member) {
    return false;
  }

  const roleArray = Array.isArray(roles) ? roles : [roles];
  return roleArray.includes(context.member.role);
}

/**
 * Check if user is admin (administrator or super_admin)
 */
export function isAdmin(context: EnhancedContext): boolean {
  return hasRole(context, ["administrator", "super_admin"]);
}

/**
 * Check if user is super admin
 */
export function isSuperAdmin(context: EnhancedContext): boolean {
  return hasRole(context, "super_admin");
}

/**
 * Pagination utilities
 */
export interface PaginationInput {
  page?: number;
  limit?: number;
}

export interface PaginationResult<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export function getPaginationParams(input: PaginationInput) {
  const page = Math.max(1, input.page || 1);
  const limit = Math.min(100, Math.max(1, input.limit || 20));
  const skip = (page - 1) * limit;

  return { page, limit, skip };
}

export function createPaginationResult<T>(
  data: T[],
  total: number,
  page: number,
  limit: number
): PaginationResult<T> {
  const totalPages = Math.ceil(total / limit);

  return {
    data,
    pagination: {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    },
  };
}

/**
 * Search and filtering utilities
 */
export function buildSearchFilter(searchTerm?: string, fields: string[] = []) {
  if (!searchTerm || fields.length === 0) {
    return {};
  }

  return {
    OR: fields.map((field) => ({
      [field]: {
        contains: searchTerm,
        mode: "insensitive" as const,
      },
    })),
  };
}

/**
 * Date range filtering
 */
export interface DateRangeFilter {
  from?: Date;
  to?: Date;
}

export function buildDateRangeFilter(
  dateRange: DateRangeFilter,
  field = "createdAt"
) {
  const filter: any = {};

  if (dateRange.from || dateRange.to) {
    filter[field] = {};
    if (dateRange.from) {
      filter[field].gte = dateRange.from;
    }
    if (dateRange.to) {
      filter[field].lte = dateRange.to;
    }
  }

  return filter;
}

/**
 * Generate unique identifiers
 */
export function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Sanitize user input
 */
export function sanitizeString(input: string): string {
  return input.trim().replace(/\s+/g, " ");
}

/**
 * Format currency for South African context
 */
export function formatCurrency(amount: number, currency = "ZAR"): string {
  return new Intl.NumberFormat("en-ZA", {
    style: "currency",
    currency,
  }).format(amount);
}