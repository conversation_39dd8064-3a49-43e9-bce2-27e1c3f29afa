
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `Message` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model Message
 * 
 */
export type MessageModel = runtime.Types.Result.DefaultSelection<Prisma.$MessagePayload>

export type AggregateMessage = {
  _count: MessageCountAggregateOutputType | null
  _min: MessageMinAggregateOutputType | null
  _max: MessageMaxAggregateOutputType | null
}

export type MessageMinAggregateOutputType = {
  id: string | null
  senderId: string | null
  receiverId: string | null
  content: string | null
  isRead: boolean | null
  createdAt: Date | null
}

export type MessageMaxAggregateOutputType = {
  id: string | null
  senderId: string | null
  receiverId: string | null
  content: string | null
  isRead: boolean | null
  createdAt: Date | null
}

export type MessageCountAggregateOutputType = {
  id: number
  senderId: number
  receiverId: number
  content: number
  isRead: number
  createdAt: number
  _all: number
}


export type MessageMinAggregateInputType = {
  id?: true
  senderId?: true
  receiverId?: true
  content?: true
  isRead?: true
  createdAt?: true
}

export type MessageMaxAggregateInputType = {
  id?: true
  senderId?: true
  receiverId?: true
  content?: true
  isRead?: true
  createdAt?: true
}

export type MessageCountAggregateInputType = {
  id?: true
  senderId?: true
  receiverId?: true
  content?: true
  isRead?: true
  createdAt?: true
  _all?: true
}

export type MessageAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Message to aggregate.
   */
  where?: Prisma.MessageWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Messages to fetch.
   */
  orderBy?: Prisma.MessageOrderByWithRelationInput | Prisma.MessageOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.MessageWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Messages from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Messages.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Messages
  **/
  _count?: true | MessageCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: MessageMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: MessageMaxAggregateInputType
}

export type GetMessageAggregateType<T extends MessageAggregateArgs> = {
      [P in keyof T & keyof AggregateMessage]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateMessage[P]>
    : Prisma.GetScalarType<T[P], AggregateMessage[P]>
}




export type MessageGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.MessageWhereInput
  orderBy?: Prisma.MessageOrderByWithAggregationInput | Prisma.MessageOrderByWithAggregationInput[]
  by: Prisma.MessageScalarFieldEnum[] | Prisma.MessageScalarFieldEnum
  having?: Prisma.MessageScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: MessageCountAggregateInputType | true
  _min?: MessageMinAggregateInputType
  _max?: MessageMaxAggregateInputType
}

export type MessageGroupByOutputType = {
  id: string
  senderId: string
  receiverId: string
  content: string
  isRead: boolean
  createdAt: Date
  _count: MessageCountAggregateOutputType | null
  _min: MessageMinAggregateOutputType | null
  _max: MessageMaxAggregateOutputType | null
}

type GetMessageGroupByPayload<T extends MessageGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<MessageGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof MessageGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], MessageGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], MessageGroupByOutputType[P]>
      }
    >
  >



export type MessageWhereInput = {
  AND?: Prisma.MessageWhereInput | Prisma.MessageWhereInput[]
  OR?: Prisma.MessageWhereInput[]
  NOT?: Prisma.MessageWhereInput | Prisma.MessageWhereInput[]
  id?: Prisma.StringFilter<"Message"> | string
  senderId?: Prisma.StringFilter<"Message"> | string
  receiverId?: Prisma.StringFilter<"Message"> | string
  content?: Prisma.StringFilter<"Message"> | string
  isRead?: Prisma.BoolFilter<"Message"> | boolean
  createdAt?: Prisma.DateTimeFilter<"Message"> | Date | string
  sender?: Prisma.XOR<Prisma.AlumniProfileScalarRelationFilter, Prisma.AlumniProfileWhereInput>
  receiver?: Prisma.XOR<Prisma.AlumniProfileScalarRelationFilter, Prisma.AlumniProfileWhereInput>
}

export type MessageOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  senderId?: Prisma.SortOrder
  receiverId?: Prisma.SortOrder
  content?: Prisma.SortOrder
  isRead?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  sender?: Prisma.AlumniProfileOrderByWithRelationInput
  receiver?: Prisma.AlumniProfileOrderByWithRelationInput
  _relevance?: Prisma.MessageOrderByRelevanceInput
}

export type MessageWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  AND?: Prisma.MessageWhereInput | Prisma.MessageWhereInput[]
  OR?: Prisma.MessageWhereInput[]
  NOT?: Prisma.MessageWhereInput | Prisma.MessageWhereInput[]
  senderId?: Prisma.StringFilter<"Message"> | string
  receiverId?: Prisma.StringFilter<"Message"> | string
  content?: Prisma.StringFilter<"Message"> | string
  isRead?: Prisma.BoolFilter<"Message"> | boolean
  createdAt?: Prisma.DateTimeFilter<"Message"> | Date | string
  sender?: Prisma.XOR<Prisma.AlumniProfileScalarRelationFilter, Prisma.AlumniProfileWhereInput>
  receiver?: Prisma.XOR<Prisma.AlumniProfileScalarRelationFilter, Prisma.AlumniProfileWhereInput>
}, "id">

export type MessageOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  senderId?: Prisma.SortOrder
  receiverId?: Prisma.SortOrder
  content?: Prisma.SortOrder
  isRead?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  _count?: Prisma.MessageCountOrderByAggregateInput
  _max?: Prisma.MessageMaxOrderByAggregateInput
  _min?: Prisma.MessageMinOrderByAggregateInput
}

export type MessageScalarWhereWithAggregatesInput = {
  AND?: Prisma.MessageScalarWhereWithAggregatesInput | Prisma.MessageScalarWhereWithAggregatesInput[]
  OR?: Prisma.MessageScalarWhereWithAggregatesInput[]
  NOT?: Prisma.MessageScalarWhereWithAggregatesInput | Prisma.MessageScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"Message"> | string
  senderId?: Prisma.StringWithAggregatesFilter<"Message"> | string
  receiverId?: Prisma.StringWithAggregatesFilter<"Message"> | string
  content?: Prisma.StringWithAggregatesFilter<"Message"> | string
  isRead?: Prisma.BoolWithAggregatesFilter<"Message"> | boolean
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"Message"> | Date | string
}

export type MessageCreateInput = {
  id?: string
  content: string
  isRead?: boolean
  createdAt?: Date | string
  sender: Prisma.AlumniProfileCreateNestedOneWithoutMessagesInput
  receiver: Prisma.AlumniProfileCreateNestedOneWithoutReceivedMessagesInput
}

export type MessageUncheckedCreateInput = {
  id?: string
  senderId: string
  receiverId: string
  content: string
  isRead?: boolean
  createdAt?: Date | string
}

export type MessageUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  content?: Prisma.StringFieldUpdateOperationsInput | string
  isRead?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sender?: Prisma.AlumniProfileUpdateOneRequiredWithoutMessagesNestedInput
  receiver?: Prisma.AlumniProfileUpdateOneRequiredWithoutReceivedMessagesNestedInput
}

export type MessageUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  senderId?: Prisma.StringFieldUpdateOperationsInput | string
  receiverId?: Prisma.StringFieldUpdateOperationsInput | string
  content?: Prisma.StringFieldUpdateOperationsInput | string
  isRead?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type MessageCreateManyInput = {
  id?: string
  senderId: string
  receiverId: string
  content: string
  isRead?: boolean
  createdAt?: Date | string
}

export type MessageUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  content?: Prisma.StringFieldUpdateOperationsInput | string
  isRead?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type MessageUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  senderId?: Prisma.StringFieldUpdateOperationsInput | string
  receiverId?: Prisma.StringFieldUpdateOperationsInput | string
  content?: Prisma.StringFieldUpdateOperationsInput | string
  isRead?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type MessageListRelationFilter = {
  every?: Prisma.MessageWhereInput
  some?: Prisma.MessageWhereInput
  none?: Prisma.MessageWhereInput
}

export type MessageOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type MessageOrderByRelevanceInput = {
  fields: Prisma.MessageOrderByRelevanceFieldEnum | Prisma.MessageOrderByRelevanceFieldEnum[]
  sort: Prisma.SortOrder
  search: string
}

export type MessageCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  senderId?: Prisma.SortOrder
  receiverId?: Prisma.SortOrder
  content?: Prisma.SortOrder
  isRead?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
}

export type MessageMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  senderId?: Prisma.SortOrder
  receiverId?: Prisma.SortOrder
  content?: Prisma.SortOrder
  isRead?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
}

export type MessageMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  senderId?: Prisma.SortOrder
  receiverId?: Prisma.SortOrder
  content?: Prisma.SortOrder
  isRead?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
}

export type MessageCreateNestedManyWithoutSenderInput = {
  create?: Prisma.XOR<Prisma.MessageCreateWithoutSenderInput, Prisma.MessageUncheckedCreateWithoutSenderInput> | Prisma.MessageCreateWithoutSenderInput[] | Prisma.MessageUncheckedCreateWithoutSenderInput[]
  connectOrCreate?: Prisma.MessageCreateOrConnectWithoutSenderInput | Prisma.MessageCreateOrConnectWithoutSenderInput[]
  createMany?: Prisma.MessageCreateManySenderInputEnvelope
  connect?: Prisma.MessageWhereUniqueInput | Prisma.MessageWhereUniqueInput[]
}

export type MessageCreateNestedManyWithoutReceiverInput = {
  create?: Prisma.XOR<Prisma.MessageCreateWithoutReceiverInput, Prisma.MessageUncheckedCreateWithoutReceiverInput> | Prisma.MessageCreateWithoutReceiverInput[] | Prisma.MessageUncheckedCreateWithoutReceiverInput[]
  connectOrCreate?: Prisma.MessageCreateOrConnectWithoutReceiverInput | Prisma.MessageCreateOrConnectWithoutReceiverInput[]
  createMany?: Prisma.MessageCreateManyReceiverInputEnvelope
  connect?: Prisma.MessageWhereUniqueInput | Prisma.MessageWhereUniqueInput[]
}

export type MessageUncheckedCreateNestedManyWithoutSenderInput = {
  create?: Prisma.XOR<Prisma.MessageCreateWithoutSenderInput, Prisma.MessageUncheckedCreateWithoutSenderInput> | Prisma.MessageCreateWithoutSenderInput[] | Prisma.MessageUncheckedCreateWithoutSenderInput[]
  connectOrCreate?: Prisma.MessageCreateOrConnectWithoutSenderInput | Prisma.MessageCreateOrConnectWithoutSenderInput[]
  createMany?: Prisma.MessageCreateManySenderInputEnvelope
  connect?: Prisma.MessageWhereUniqueInput | Prisma.MessageWhereUniqueInput[]
}

export type MessageUncheckedCreateNestedManyWithoutReceiverInput = {
  create?: Prisma.XOR<Prisma.MessageCreateWithoutReceiverInput, Prisma.MessageUncheckedCreateWithoutReceiverInput> | Prisma.MessageCreateWithoutReceiverInput[] | Prisma.MessageUncheckedCreateWithoutReceiverInput[]
  connectOrCreate?: Prisma.MessageCreateOrConnectWithoutReceiverInput | Prisma.MessageCreateOrConnectWithoutReceiverInput[]
  createMany?: Prisma.MessageCreateManyReceiverInputEnvelope
  connect?: Prisma.MessageWhereUniqueInput | Prisma.MessageWhereUniqueInput[]
}

export type MessageUpdateManyWithoutSenderNestedInput = {
  create?: Prisma.XOR<Prisma.MessageCreateWithoutSenderInput, Prisma.MessageUncheckedCreateWithoutSenderInput> | Prisma.MessageCreateWithoutSenderInput[] | Prisma.MessageUncheckedCreateWithoutSenderInput[]
  connectOrCreate?: Prisma.MessageCreateOrConnectWithoutSenderInput | Prisma.MessageCreateOrConnectWithoutSenderInput[]
  upsert?: Prisma.MessageUpsertWithWhereUniqueWithoutSenderInput | Prisma.MessageUpsertWithWhereUniqueWithoutSenderInput[]
  createMany?: Prisma.MessageCreateManySenderInputEnvelope
  set?: Prisma.MessageWhereUniqueInput | Prisma.MessageWhereUniqueInput[]
  disconnect?: Prisma.MessageWhereUniqueInput | Prisma.MessageWhereUniqueInput[]
  delete?: Prisma.MessageWhereUniqueInput | Prisma.MessageWhereUniqueInput[]
  connect?: Prisma.MessageWhereUniqueInput | Prisma.MessageWhereUniqueInput[]
  update?: Prisma.MessageUpdateWithWhereUniqueWithoutSenderInput | Prisma.MessageUpdateWithWhereUniqueWithoutSenderInput[]
  updateMany?: Prisma.MessageUpdateManyWithWhereWithoutSenderInput | Prisma.MessageUpdateManyWithWhereWithoutSenderInput[]
  deleteMany?: Prisma.MessageScalarWhereInput | Prisma.MessageScalarWhereInput[]
}

export type MessageUpdateManyWithoutReceiverNestedInput = {
  create?: Prisma.XOR<Prisma.MessageCreateWithoutReceiverInput, Prisma.MessageUncheckedCreateWithoutReceiverInput> | Prisma.MessageCreateWithoutReceiverInput[] | Prisma.MessageUncheckedCreateWithoutReceiverInput[]
  connectOrCreate?: Prisma.MessageCreateOrConnectWithoutReceiverInput | Prisma.MessageCreateOrConnectWithoutReceiverInput[]
  upsert?: Prisma.MessageUpsertWithWhereUniqueWithoutReceiverInput | Prisma.MessageUpsertWithWhereUniqueWithoutReceiverInput[]
  createMany?: Prisma.MessageCreateManyReceiverInputEnvelope
  set?: Prisma.MessageWhereUniqueInput | Prisma.MessageWhereUniqueInput[]
  disconnect?: Prisma.MessageWhereUniqueInput | Prisma.MessageWhereUniqueInput[]
  delete?: Prisma.MessageWhereUniqueInput | Prisma.MessageWhereUniqueInput[]
  connect?: Prisma.MessageWhereUniqueInput | Prisma.MessageWhereUniqueInput[]
  update?: Prisma.MessageUpdateWithWhereUniqueWithoutReceiverInput | Prisma.MessageUpdateWithWhereUniqueWithoutReceiverInput[]
  updateMany?: Prisma.MessageUpdateManyWithWhereWithoutReceiverInput | Prisma.MessageUpdateManyWithWhereWithoutReceiverInput[]
  deleteMany?: Prisma.MessageScalarWhereInput | Prisma.MessageScalarWhereInput[]
}

export type MessageUncheckedUpdateManyWithoutSenderNestedInput = {
  create?: Prisma.XOR<Prisma.MessageCreateWithoutSenderInput, Prisma.MessageUncheckedCreateWithoutSenderInput> | Prisma.MessageCreateWithoutSenderInput[] | Prisma.MessageUncheckedCreateWithoutSenderInput[]
  connectOrCreate?: Prisma.MessageCreateOrConnectWithoutSenderInput | Prisma.MessageCreateOrConnectWithoutSenderInput[]
  upsert?: Prisma.MessageUpsertWithWhereUniqueWithoutSenderInput | Prisma.MessageUpsertWithWhereUniqueWithoutSenderInput[]
  createMany?: Prisma.MessageCreateManySenderInputEnvelope
  set?: Prisma.MessageWhereUniqueInput | Prisma.MessageWhereUniqueInput[]
  disconnect?: Prisma.MessageWhereUniqueInput | Prisma.MessageWhereUniqueInput[]
  delete?: Prisma.MessageWhereUniqueInput | Prisma.MessageWhereUniqueInput[]
  connect?: Prisma.MessageWhereUniqueInput | Prisma.MessageWhereUniqueInput[]
  update?: Prisma.MessageUpdateWithWhereUniqueWithoutSenderInput | Prisma.MessageUpdateWithWhereUniqueWithoutSenderInput[]
  updateMany?: Prisma.MessageUpdateManyWithWhereWithoutSenderInput | Prisma.MessageUpdateManyWithWhereWithoutSenderInput[]
  deleteMany?: Prisma.MessageScalarWhereInput | Prisma.MessageScalarWhereInput[]
}

export type MessageUncheckedUpdateManyWithoutReceiverNestedInput = {
  create?: Prisma.XOR<Prisma.MessageCreateWithoutReceiverInput, Prisma.MessageUncheckedCreateWithoutReceiverInput> | Prisma.MessageCreateWithoutReceiverInput[] | Prisma.MessageUncheckedCreateWithoutReceiverInput[]
  connectOrCreate?: Prisma.MessageCreateOrConnectWithoutReceiverInput | Prisma.MessageCreateOrConnectWithoutReceiverInput[]
  upsert?: Prisma.MessageUpsertWithWhereUniqueWithoutReceiverInput | Prisma.MessageUpsertWithWhereUniqueWithoutReceiverInput[]
  createMany?: Prisma.MessageCreateManyReceiverInputEnvelope
  set?: Prisma.MessageWhereUniqueInput | Prisma.MessageWhereUniqueInput[]
  disconnect?: Prisma.MessageWhereUniqueInput | Prisma.MessageWhereUniqueInput[]
  delete?: Prisma.MessageWhereUniqueInput | Prisma.MessageWhereUniqueInput[]
  connect?: Prisma.MessageWhereUniqueInput | Prisma.MessageWhereUniqueInput[]
  update?: Prisma.MessageUpdateWithWhereUniqueWithoutReceiverInput | Prisma.MessageUpdateWithWhereUniqueWithoutReceiverInput[]
  updateMany?: Prisma.MessageUpdateManyWithWhereWithoutReceiverInput | Prisma.MessageUpdateManyWithWhereWithoutReceiverInput[]
  deleteMany?: Prisma.MessageScalarWhereInput | Prisma.MessageScalarWhereInput[]
}

export type MessageCreateWithoutSenderInput = {
  id?: string
  content: string
  isRead?: boolean
  createdAt?: Date | string
  receiver: Prisma.AlumniProfileCreateNestedOneWithoutReceivedMessagesInput
}

export type MessageUncheckedCreateWithoutSenderInput = {
  id?: string
  receiverId: string
  content: string
  isRead?: boolean
  createdAt?: Date | string
}

export type MessageCreateOrConnectWithoutSenderInput = {
  where: Prisma.MessageWhereUniqueInput
  create: Prisma.XOR<Prisma.MessageCreateWithoutSenderInput, Prisma.MessageUncheckedCreateWithoutSenderInput>
}

export type MessageCreateManySenderInputEnvelope = {
  data: Prisma.MessageCreateManySenderInput | Prisma.MessageCreateManySenderInput[]
  skipDuplicates?: boolean
}

export type MessageCreateWithoutReceiverInput = {
  id?: string
  content: string
  isRead?: boolean
  createdAt?: Date | string
  sender: Prisma.AlumniProfileCreateNestedOneWithoutMessagesInput
}

export type MessageUncheckedCreateWithoutReceiverInput = {
  id?: string
  senderId: string
  content: string
  isRead?: boolean
  createdAt?: Date | string
}

export type MessageCreateOrConnectWithoutReceiverInput = {
  where: Prisma.MessageWhereUniqueInput
  create: Prisma.XOR<Prisma.MessageCreateWithoutReceiverInput, Prisma.MessageUncheckedCreateWithoutReceiverInput>
}

export type MessageCreateManyReceiverInputEnvelope = {
  data: Prisma.MessageCreateManyReceiverInput | Prisma.MessageCreateManyReceiverInput[]
  skipDuplicates?: boolean
}

export type MessageUpsertWithWhereUniqueWithoutSenderInput = {
  where: Prisma.MessageWhereUniqueInput
  update: Prisma.XOR<Prisma.MessageUpdateWithoutSenderInput, Prisma.MessageUncheckedUpdateWithoutSenderInput>
  create: Prisma.XOR<Prisma.MessageCreateWithoutSenderInput, Prisma.MessageUncheckedCreateWithoutSenderInput>
}

export type MessageUpdateWithWhereUniqueWithoutSenderInput = {
  where: Prisma.MessageWhereUniqueInput
  data: Prisma.XOR<Prisma.MessageUpdateWithoutSenderInput, Prisma.MessageUncheckedUpdateWithoutSenderInput>
}

export type MessageUpdateManyWithWhereWithoutSenderInput = {
  where: Prisma.MessageScalarWhereInput
  data: Prisma.XOR<Prisma.MessageUpdateManyMutationInput, Prisma.MessageUncheckedUpdateManyWithoutSenderInput>
}

export type MessageScalarWhereInput = {
  AND?: Prisma.MessageScalarWhereInput | Prisma.MessageScalarWhereInput[]
  OR?: Prisma.MessageScalarWhereInput[]
  NOT?: Prisma.MessageScalarWhereInput | Prisma.MessageScalarWhereInput[]
  id?: Prisma.StringFilter<"Message"> | string
  senderId?: Prisma.StringFilter<"Message"> | string
  receiverId?: Prisma.StringFilter<"Message"> | string
  content?: Prisma.StringFilter<"Message"> | string
  isRead?: Prisma.BoolFilter<"Message"> | boolean
  createdAt?: Prisma.DateTimeFilter<"Message"> | Date | string
}

export type MessageUpsertWithWhereUniqueWithoutReceiverInput = {
  where: Prisma.MessageWhereUniqueInput
  update: Prisma.XOR<Prisma.MessageUpdateWithoutReceiverInput, Prisma.MessageUncheckedUpdateWithoutReceiverInput>
  create: Prisma.XOR<Prisma.MessageCreateWithoutReceiverInput, Prisma.MessageUncheckedCreateWithoutReceiverInput>
}

export type MessageUpdateWithWhereUniqueWithoutReceiverInput = {
  where: Prisma.MessageWhereUniqueInput
  data: Prisma.XOR<Prisma.MessageUpdateWithoutReceiverInput, Prisma.MessageUncheckedUpdateWithoutReceiverInput>
}

export type MessageUpdateManyWithWhereWithoutReceiverInput = {
  where: Prisma.MessageScalarWhereInput
  data: Prisma.XOR<Prisma.MessageUpdateManyMutationInput, Prisma.MessageUncheckedUpdateManyWithoutReceiverInput>
}

export type MessageCreateManySenderInput = {
  id?: string
  receiverId: string
  content: string
  isRead?: boolean
  createdAt?: Date | string
}

export type MessageCreateManyReceiverInput = {
  id?: string
  senderId: string
  content: string
  isRead?: boolean
  createdAt?: Date | string
}

export type MessageUpdateWithoutSenderInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  content?: Prisma.StringFieldUpdateOperationsInput | string
  isRead?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  receiver?: Prisma.AlumniProfileUpdateOneRequiredWithoutReceivedMessagesNestedInput
}

export type MessageUncheckedUpdateWithoutSenderInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  receiverId?: Prisma.StringFieldUpdateOperationsInput | string
  content?: Prisma.StringFieldUpdateOperationsInput | string
  isRead?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type MessageUncheckedUpdateManyWithoutSenderInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  receiverId?: Prisma.StringFieldUpdateOperationsInput | string
  content?: Prisma.StringFieldUpdateOperationsInput | string
  isRead?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type MessageUpdateWithoutReceiverInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  content?: Prisma.StringFieldUpdateOperationsInput | string
  isRead?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sender?: Prisma.AlumniProfileUpdateOneRequiredWithoutMessagesNestedInput
}

export type MessageUncheckedUpdateWithoutReceiverInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  senderId?: Prisma.StringFieldUpdateOperationsInput | string
  content?: Prisma.StringFieldUpdateOperationsInput | string
  isRead?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type MessageUncheckedUpdateManyWithoutReceiverInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  senderId?: Prisma.StringFieldUpdateOperationsInput | string
  content?: Prisma.StringFieldUpdateOperationsInput | string
  isRead?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}



export type MessageSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  senderId?: boolean
  receiverId?: boolean
  content?: boolean
  isRead?: boolean
  createdAt?: boolean
  sender?: boolean | Prisma.AlumniProfileDefaultArgs<ExtArgs>
  receiver?: boolean | Prisma.AlumniProfileDefaultArgs<ExtArgs>
}, ExtArgs["result"]["message"]>



export type MessageSelectScalar = {
  id?: boolean
  senderId?: boolean
  receiverId?: boolean
  content?: boolean
  isRead?: boolean
  createdAt?: boolean
}

export type MessageOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "senderId" | "receiverId" | "content" | "isRead" | "createdAt", ExtArgs["result"]["message"]>
export type MessageInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  sender?: boolean | Prisma.AlumniProfileDefaultArgs<ExtArgs>
  receiver?: boolean | Prisma.AlumniProfileDefaultArgs<ExtArgs>
}

export type $MessagePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Message"
  objects: {
    sender: Prisma.$AlumniProfilePayload<ExtArgs>
    receiver: Prisma.$AlumniProfilePayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    senderId: string
    receiverId: string
    content: string
    isRead: boolean
    createdAt: Date
  }, ExtArgs["result"]["message"]>
  composites: {}
}

export type MessageGetPayload<S extends boolean | null | undefined | MessageDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$MessagePayload, S>

export type MessageCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<MessageFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: MessageCountAggregateInputType | true
  }

export interface MessageDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Message'], meta: { name: 'Message' } }
  /**
   * Find zero or one Message that matches the filter.
   * @param {MessageFindUniqueArgs} args - Arguments to find a Message
   * @example
   * // Get one Message
   * const message = await prisma.message.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends MessageFindUniqueArgs>(args: Prisma.SelectSubset<T, MessageFindUniqueArgs<ExtArgs>>): Prisma.Prisma__MessageClient<runtime.Types.Result.GetResult<Prisma.$MessagePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Message that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {MessageFindUniqueOrThrowArgs} args - Arguments to find a Message
   * @example
   * // Get one Message
   * const message = await prisma.message.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends MessageFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, MessageFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__MessageClient<runtime.Types.Result.GetResult<Prisma.$MessagePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Message that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {MessageFindFirstArgs} args - Arguments to find a Message
   * @example
   * // Get one Message
   * const message = await prisma.message.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends MessageFindFirstArgs>(args?: Prisma.SelectSubset<T, MessageFindFirstArgs<ExtArgs>>): Prisma.Prisma__MessageClient<runtime.Types.Result.GetResult<Prisma.$MessagePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Message that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {MessageFindFirstOrThrowArgs} args - Arguments to find a Message
   * @example
   * // Get one Message
   * const message = await prisma.message.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends MessageFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, MessageFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__MessageClient<runtime.Types.Result.GetResult<Prisma.$MessagePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Messages that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {MessageFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Messages
   * const messages = await prisma.message.findMany()
   * 
   * // Get first 10 Messages
   * const messages = await prisma.message.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const messageWithIdOnly = await prisma.message.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends MessageFindManyArgs>(args?: Prisma.SelectSubset<T, MessageFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$MessagePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Message.
   * @param {MessageCreateArgs} args - Arguments to create a Message.
   * @example
   * // Create one Message
   * const Message = await prisma.message.create({
   *   data: {
   *     // ... data to create a Message
   *   }
   * })
   * 
   */
  create<T extends MessageCreateArgs>(args: Prisma.SelectSubset<T, MessageCreateArgs<ExtArgs>>): Prisma.Prisma__MessageClient<runtime.Types.Result.GetResult<Prisma.$MessagePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Messages.
   * @param {MessageCreateManyArgs} args - Arguments to create many Messages.
   * @example
   * // Create many Messages
   * const message = await prisma.message.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends MessageCreateManyArgs>(args?: Prisma.SelectSubset<T, MessageCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Delete a Message.
   * @param {MessageDeleteArgs} args - Arguments to delete one Message.
   * @example
   * // Delete one Message
   * const Message = await prisma.message.delete({
   *   where: {
   *     // ... filter to delete one Message
   *   }
   * })
   * 
   */
  delete<T extends MessageDeleteArgs>(args: Prisma.SelectSubset<T, MessageDeleteArgs<ExtArgs>>): Prisma.Prisma__MessageClient<runtime.Types.Result.GetResult<Prisma.$MessagePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Message.
   * @param {MessageUpdateArgs} args - Arguments to update one Message.
   * @example
   * // Update one Message
   * const message = await prisma.message.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends MessageUpdateArgs>(args: Prisma.SelectSubset<T, MessageUpdateArgs<ExtArgs>>): Prisma.Prisma__MessageClient<runtime.Types.Result.GetResult<Prisma.$MessagePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Messages.
   * @param {MessageDeleteManyArgs} args - Arguments to filter Messages to delete.
   * @example
   * // Delete a few Messages
   * const { count } = await prisma.message.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends MessageDeleteManyArgs>(args?: Prisma.SelectSubset<T, MessageDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Messages.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {MessageUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Messages
   * const message = await prisma.message.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends MessageUpdateManyArgs>(args: Prisma.SelectSubset<T, MessageUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create or update one Message.
   * @param {MessageUpsertArgs} args - Arguments to update or create a Message.
   * @example
   * // Update or create a Message
   * const message = await prisma.message.upsert({
   *   create: {
   *     // ... data to create a Message
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Message we want to update
   *   }
   * })
   */
  upsert<T extends MessageUpsertArgs>(args: Prisma.SelectSubset<T, MessageUpsertArgs<ExtArgs>>): Prisma.Prisma__MessageClient<runtime.Types.Result.GetResult<Prisma.$MessagePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Messages.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {MessageCountArgs} args - Arguments to filter Messages to count.
   * @example
   * // Count the number of Messages
   * const count = await prisma.message.count({
   *   where: {
   *     // ... the filter for the Messages we want to count
   *   }
   * })
  **/
  count<T extends MessageCountArgs>(
    args?: Prisma.Subset<T, MessageCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], MessageCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Message.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {MessageAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends MessageAggregateArgs>(args: Prisma.Subset<T, MessageAggregateArgs>): Prisma.PrismaPromise<GetMessageAggregateType<T>>

  /**
   * Group by Message.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {MessageGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends MessageGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: MessageGroupByArgs['orderBy'] }
      : { orderBy?: MessageGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, MessageGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetMessageGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Message model
 */
readonly fields: MessageFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Message.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__MessageClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  sender<T extends Prisma.AlumniProfileDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.AlumniProfileDefaultArgs<ExtArgs>>): Prisma.Prisma__AlumniProfileClient<runtime.Types.Result.GetResult<Prisma.$AlumniProfilePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  receiver<T extends Prisma.AlumniProfileDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.AlumniProfileDefaultArgs<ExtArgs>>): Prisma.Prisma__AlumniProfileClient<runtime.Types.Result.GetResult<Prisma.$AlumniProfilePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Message model
 */
export interface MessageFieldRefs {
  readonly id: Prisma.FieldRef<"Message", 'String'>
  readonly senderId: Prisma.FieldRef<"Message", 'String'>
  readonly receiverId: Prisma.FieldRef<"Message", 'String'>
  readonly content: Prisma.FieldRef<"Message", 'String'>
  readonly isRead: Prisma.FieldRef<"Message", 'Boolean'>
  readonly createdAt: Prisma.FieldRef<"Message", 'DateTime'>
}
    

// Custom InputTypes
/**
 * Message findUnique
 */
export type MessageFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Message
   */
  select?: Prisma.MessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Message
   */
  omit?: Prisma.MessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageInclude<ExtArgs> | null
  /**
   * Filter, which Message to fetch.
   */
  where: Prisma.MessageWhereUniqueInput
}

/**
 * Message findUniqueOrThrow
 */
export type MessageFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Message
   */
  select?: Prisma.MessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Message
   */
  omit?: Prisma.MessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageInclude<ExtArgs> | null
  /**
   * Filter, which Message to fetch.
   */
  where: Prisma.MessageWhereUniqueInput
}

/**
 * Message findFirst
 */
export type MessageFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Message
   */
  select?: Prisma.MessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Message
   */
  omit?: Prisma.MessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageInclude<ExtArgs> | null
  /**
   * Filter, which Message to fetch.
   */
  where?: Prisma.MessageWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Messages to fetch.
   */
  orderBy?: Prisma.MessageOrderByWithRelationInput | Prisma.MessageOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Messages.
   */
  cursor?: Prisma.MessageWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Messages from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Messages.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Messages.
   */
  distinct?: Prisma.MessageScalarFieldEnum | Prisma.MessageScalarFieldEnum[]
}

/**
 * Message findFirstOrThrow
 */
export type MessageFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Message
   */
  select?: Prisma.MessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Message
   */
  omit?: Prisma.MessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageInclude<ExtArgs> | null
  /**
   * Filter, which Message to fetch.
   */
  where?: Prisma.MessageWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Messages to fetch.
   */
  orderBy?: Prisma.MessageOrderByWithRelationInput | Prisma.MessageOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Messages.
   */
  cursor?: Prisma.MessageWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Messages from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Messages.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Messages.
   */
  distinct?: Prisma.MessageScalarFieldEnum | Prisma.MessageScalarFieldEnum[]
}

/**
 * Message findMany
 */
export type MessageFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Message
   */
  select?: Prisma.MessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Message
   */
  omit?: Prisma.MessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageInclude<ExtArgs> | null
  /**
   * Filter, which Messages to fetch.
   */
  where?: Prisma.MessageWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Messages to fetch.
   */
  orderBy?: Prisma.MessageOrderByWithRelationInput | Prisma.MessageOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Messages.
   */
  cursor?: Prisma.MessageWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Messages from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Messages.
   */
  skip?: number
  distinct?: Prisma.MessageScalarFieldEnum | Prisma.MessageScalarFieldEnum[]
}

/**
 * Message create
 */
export type MessageCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Message
   */
  select?: Prisma.MessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Message
   */
  omit?: Prisma.MessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageInclude<ExtArgs> | null
  /**
   * The data needed to create a Message.
   */
  data: Prisma.XOR<Prisma.MessageCreateInput, Prisma.MessageUncheckedCreateInput>
}

/**
 * Message createMany
 */
export type MessageCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Messages.
   */
  data: Prisma.MessageCreateManyInput | Prisma.MessageCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Message update
 */
export type MessageUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Message
   */
  select?: Prisma.MessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Message
   */
  omit?: Prisma.MessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageInclude<ExtArgs> | null
  /**
   * The data needed to update a Message.
   */
  data: Prisma.XOR<Prisma.MessageUpdateInput, Prisma.MessageUncheckedUpdateInput>
  /**
   * Choose, which Message to update.
   */
  where: Prisma.MessageWhereUniqueInput
}

/**
 * Message updateMany
 */
export type MessageUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Messages.
   */
  data: Prisma.XOR<Prisma.MessageUpdateManyMutationInput, Prisma.MessageUncheckedUpdateManyInput>
  /**
   * Filter which Messages to update
   */
  where?: Prisma.MessageWhereInput
  /**
   * Limit how many Messages to update.
   */
  limit?: number
}

/**
 * Message upsert
 */
export type MessageUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Message
   */
  select?: Prisma.MessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Message
   */
  omit?: Prisma.MessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageInclude<ExtArgs> | null
  /**
   * The filter to search for the Message to update in case it exists.
   */
  where: Prisma.MessageWhereUniqueInput
  /**
   * In case the Message found by the `where` argument doesn't exist, create a new Message with this data.
   */
  create: Prisma.XOR<Prisma.MessageCreateInput, Prisma.MessageUncheckedCreateInput>
  /**
   * In case the Message was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.MessageUpdateInput, Prisma.MessageUncheckedUpdateInput>
}

/**
 * Message delete
 */
export type MessageDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Message
   */
  select?: Prisma.MessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Message
   */
  omit?: Prisma.MessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageInclude<ExtArgs> | null
  /**
   * Filter which Message to delete.
   */
  where: Prisma.MessageWhereUniqueInput
}

/**
 * Message deleteMany
 */
export type MessageDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Messages to delete
   */
  where?: Prisma.MessageWhereInput
  /**
   * Limit how many Messages to delete.
   */
  limit?: number
}

/**
 * Message without action
 */
export type MessageDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Message
   */
  select?: Prisma.MessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Message
   */
  omit?: Prisma.MessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.MessageInclude<ExtArgs> | null
}
