{"name": "server", "main": "src/index.ts", "type": "module", "scripts": {"build": "tsdown", "check-types": "tsc -b", "compile": "bun build --compile --minify --sourcemap --bytecode ./src/index.ts --outfile server", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "db:push": "prisma db push", "db:studio": "prisma studio", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev"}, "dependencies": {"@ai-sdk/google": "^2.0.3", "@better-auth/expo": "^1.3.4", "@hono/node-server": "^1.18.1", "@orpc/client": "^1.7.10", "@orpc/server": "^1.7.10", "@prisma/client": "^6.13.0", "ai": "^5.0.8", "better-auth": "^1.3.4", "dotenv": "^17.2.1", "hono": "^4.8.12", "zod": "^4.0.15"}, "devDependencies": {"@types/node": "^24.2.0", "prisma": "^6.13.0", "tsdown": "^0.13.3", "tsx": "^4.20.3", "typescript": "^5.9.2"}}