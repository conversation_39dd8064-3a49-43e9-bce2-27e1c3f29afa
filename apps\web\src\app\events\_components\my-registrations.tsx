"use client"

import { useMemo, useState } from "react"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { orpc, client } from "@/utils/orpc"
import { Pagination, PaginationContent, PaginationItem, PaginationNext, PaginationPrevious } from "@/components/ui/pagination"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export default function MyRegistrations() {
  const [page, setPage] = useState(1)
  const [status, setStatus] = useState<"REGISTERED" | "ATTENDED" | "NO_SHOW" | "CANCELLED" | undefined>(undefined)
  const queryClient = useQueryClient()

  const list = useQuery(orpc.events.myRegistrations.queryOptions({ input: { page, limit: 10, status } }))

  const totalPages = useMemo(() => {
    const total = (list.data as any)?.total ?? 0
    const limit = (list.data as any)?.limit ?? 10
    return Math.max(1, Math.ceil(total / limit))
  }, [list.data])

  const updateStatus = useMutation({
    mutationFn: async ({ eventId, s }: { eventId: string; s: "REGISTERED" | "ATTENDED" | "NO_SHOW" | "CANCELLED" }) => client.events.updateRegistration({ eventId, status: s }),
    onSuccess: async () => queryClient.invalidateQueries(),
  })

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <div className="text-sm font-medium text-muted-foreground">My Registrations</div>
        <div className="flex items-center gap-2">
          <Select value={status} onValueChange={(v) => { setPage(1); setStatus(v as any) }}>
            <SelectTrigger className="w-[160px]"><SelectValue placeholder="All statuses" /></SelectTrigger>
            <SelectContent>
              <SelectItem value="REGISTERED">Registered</SelectItem>
              <SelectItem value="ATTENDED">Attended</SelectItem>
              <SelectItem value="NO_SHOW">No show</SelectItem>
              <SelectItem value="CANCELLED">Cancelled</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid gap-3">
        {list.isLoading && (<div className="text-sm text-muted-foreground">Loading...</div>)}
        {(list.data as any)?.data?.map((r: any) => (
          <div key={r.id} className="rounded-lg border bg-card p-4 flex items-center justify-between">
            <div>
              <div className="font-medium">{r.event?.title}</div>
              <div className="text-sm text-muted-foreground">{new Date(r.event?.startDateTime).toLocaleString()}</div>
            </div>
            <div className="flex items-center gap-2">
              <Button size="sm" variant="outline" onClick={() => updateStatus.mutate({ eventId: r.eventId, s: "ATTENDED" })} disabled={updateStatus.isPending}>Mark attended</Button>
              <Button size="sm" variant="outline" onClick={() => updateStatus.mutate({ eventId: r.eventId, s: "CANCELLED" })} disabled={updateStatus.isPending}>Cancel</Button>
            </div>
          </div>
        ))}
      </div>

      <div className="flex items-center justify-end">
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious href="#" onClick={(e) => { e.preventDefault(); setPage((p) => Math.max(1, p - 1)) }} />
            </PaginationItem>
            <PaginationItem>
              <span className="text-sm text-muted-foreground px-3 py-2">Page {page} of {totalPages}</span>
            </PaginationItem>
            <PaginationItem>
              <PaginationNext href="#" onClick={(e) => { e.preventDefault(); setPage((p) => Math.min(totalPages, p + 1)) }} />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </div>
    </div>
  )
}


