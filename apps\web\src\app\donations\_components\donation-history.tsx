"use client"

import { useMemo, useState } from "react"
import { useQuery } from "@tanstack/react-query"
import { orpc } from "@/utils/orpc"
import { Pagination, PaginationContent, PaginationItem, PaginationNext, PaginationPrevious } from "@/components/ui/pagination"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export default function DonationHistory() {
  const [page, setPage] = useState(1)
  const [type, setType] = useState<"ONE_TIME" | "MONTHLY" | "QUARTERLY" | "ANNUAL" | undefined>()
  const [status, setStatus] = useState<"PENDING" | "COMPLETED" | "FAILED" | "REFUNDED" | "CANCELLED" | undefined>()

  const list = useQuery(orpc.donations.myDonations.queryOptions({ input: { page, limit: 10, donationType: type, paymentStatus: status } }))

  const totalPages = useMemo(() => {
    const total = (list.data as any)?.total ?? 0
    const limit = (list.data as any)?.limit ?? 10
    return Math.max(1, Math.ceil(total / limit))
  }, [list.data])

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <div className="text-sm font-medium text-muted-foreground">My Donations</div>
        <div className="flex items-center gap-2">
          <Select value={type} onValueChange={(v) => { setPage(1); setType(v as any) }}>
            <SelectTrigger className="w-[160px]"><SelectValue placeholder="All types" /></SelectTrigger>
            <SelectContent>
              <SelectItem value="ONE_TIME">One-time</SelectItem>
              <SelectItem value="MONTHLY">Monthly</SelectItem>
              <SelectItem value="QUARTERLY">Quarterly</SelectItem>
              <SelectItem value="ANNUAL">Annual</SelectItem>
            </SelectContent>
          </Select>
          <Select value={status} onValueChange={(v) => { setPage(1); setStatus(v as any) }}>
            <SelectTrigger className="w-[160px]"><SelectValue placeholder="All statuses" /></SelectTrigger>
            <SelectContent>
              <SelectItem value="PENDING">Pending</SelectItem>
              <SelectItem value="COMPLETED">Completed</SelectItem>
              <SelectItem value="FAILED">Failed</SelectItem>
              <SelectItem value="REFUNDED">Refunded</SelectItem>
              <SelectItem value="CANCELLED">Cancelled</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid gap-3">
        {list.isLoading && (<div className="text-sm text-muted-foreground">Loading...</div>)}
        {(list.data as any)?.data?.map((d: any) => (
          <div key={d.id} className="rounded-lg border bg-card p-4 flex items-center justify-between">
            <div>
              <div className="font-medium">{d.donationType} • {d.currency} {d.amount}</div>
              <div className="text-sm text-muted-foreground">{new Date(d.createdAt).toLocaleString()} • {d.paymentStatus}</div>
            </div>
            <div className="text-xs text-muted-foreground">{d.transactionId || "—"}</div>
          </div>
        ))}
      </div>

      <div className="flex items-center justify-end">
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious href="#" onClick={(e) => { e.preventDefault(); setPage((p) => Math.max(1, p - 1)) }} />
            </PaginationItem>
            <PaginationItem>
              <span className="text-sm text-muted-foreground px-3 py-2">Page {page} of {totalPages}</span>
            </PaginationItem>
            <PaginationItem>
              <PaginationNext href="#" onClick={(e) => { e.preventDefault(); setPage((p) => Math.min(totalPages, p + 1)) }} />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </div>
    </div>
  )
}


